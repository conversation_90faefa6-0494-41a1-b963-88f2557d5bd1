import { test, expect } from '@playwright/test';

/**
 * Privacy Policy page tests
 * 
 * Tests the Privacy Policy page functionality including:
 * - Page accessibility via direct URL
 * - Footer link navigation
 * - Content rendering
 * - Responsive design
 */
test.describe('Privacy Policy Page', () => {
  test('should load Privacy Policy page directly', async ({ page }) => {
    await page.goto('/privacy-policy');
    
    // Check if the page loads successfully
    await expect(page).toHaveTitle(/Privacy Policy.*Ice Box Hockey/);
    
    // Check for main heading
    await expect(page.locator('h1')).toContainText('Privacy Policy');
    
    // Check for key sections
    await expect(page.locator('h2')).toContainText('Information We Collect');
    await expect(page.locator('h2')).toContainText('How We Use Your Information');
    await expect(page.locator('h2')).toContainText('Data Storage and Security');
    await expect(page.locator('h2')).toContainText('Contact Us');
  });

  test('should navigate to Privacy Policy from footer link', async ({ page }) => {
    // Start from homepage
    await page.goto('/');
    
    // Scroll to footer to ensure it's visible
    await page.locator('footer').scrollIntoViewIfNeeded();
    
    // Click on Privacy Policy link in footer
    await page.locator('footer a[href="/privacy-policy"]').click();
    
    // Verify navigation to Privacy Policy page
    await expect(page).toHaveURL('/privacy-policy');
    await expect(page.locator('h1')).toContainText('Privacy Policy');
  });

  test('should display contact information', async ({ page }) => {
    await page.goto('/privacy-policy');
    
    // Check for contact section
    const contactSection = page.locator('section').filter({ hasText: 'Contact Us' });
    await expect(contactSection).toBeVisible();
    
    // Check for business details
    await expect(contactSection).toContainText('The Ice Box Hockey');
    await expect(contactSection).toContainText('23770 S Western Ave');
    await expect(contactSection).toContainText('Harbor City, CA 90710');
    await expect(contactSection).toContainText('<EMAIL>');
  });

  test('should have proper SEO meta tags', async ({ page }) => {
    await page.goto('/privacy-policy');
    
    // Check meta description
    const metaDescription = page.locator('meta[name="description"]');
    await expect(metaDescription).toHaveAttribute('content', /privacy policy.*Ice Box Hockey/);
    
    // Check title
    await expect(page).toHaveTitle(/Privacy Policy.*Ice Box Hockey/);
  });

  test('should be responsive on mobile', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    await page.goto('/privacy-policy');
    
    // Check if content is properly displayed on mobile
    await expect(page.locator('h1')).toBeVisible();
    await expect(page.locator('h2').first()).toBeVisible();
    
    // Check if contact section is readable on mobile
    const contactSection = page.locator('section').filter({ hasText: 'Contact Us' });
    await expect(contactSection).toBeVisible();
  });

  test('should have working email link', async ({ page }) => {
    await page.goto('/privacy-policy');
    
    // Check for email link in contact section
    const emailLink = page.locator('a[href="mailto:<EMAIL>"]');
    await expect(emailLink).toBeVisible();
    await expect(emailLink).toHaveAttribute('href', 'mailto:<EMAIL>');
  });

  test('should display last updated date', async ({ page }) => {
    await page.goto('/privacy-policy');
    
    // Check for last updated information
    await expect(page.locator('text=Last Updated:')).toBeVisible();
    
    // Verify it shows a current date (should be today's date since it's dynamically generated)
    const currentYear = new Date().getFullYear().toString();
    await expect(page.locator('text=Last Updated:').locator('..')).toContainText(currentYear);
  });

  test('should have proper heading hierarchy', async ({ page }) => {
    await page.goto('/privacy-policy');
    
    // Check heading structure
    const h1 = page.locator('h1');
    const h2Elements = page.locator('h2');
    const h3Elements = page.locator('h3');
    
    // Should have exactly one h1
    await expect(h1).toHaveCount(1);
    
    // Should have multiple h2 sections
    await expect(h2Elements).toHaveCount(8); // Based on our content structure
    
    // Should have h3 subsections
    const h3Count = await h3Elements.count();
    expect(h3Count).toBeGreaterThan(0);
  });
});