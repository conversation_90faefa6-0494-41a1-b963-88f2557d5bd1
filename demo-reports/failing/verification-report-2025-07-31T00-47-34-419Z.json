{"metadata": {"generatedAt": "2025-07-31T00:47:34.419Z", "version": "1.0.0", "reportType": "production-deployment-verification"}, "summary": {"overallStatus": "failed", "deploymentReady": false, "totalChecks": 10, "passedChecks": 3, "failedChecks": 2, "warningChecks": 5, "executionTime": 0, "criticalIssues": ["Build verification failed", "2 build errors found", "1 test suites failed"], "recommendations": ["Fix critical issues before deployment", "Address warnings for optimal production performance", "Review and fix TypeScript compilation errors", "Fix failing tests to ensure application stability", "Optimize application performance for better user experience"]}, "deploymentDecision": {"ready": false, "confidence": 0, "blockers": ["Build verification failed", "2 build errors found", "1 test suites failed"], "warnings": ["1 build warnings found", "2 individual test failures found", "Lighthouse performance score (0) below threshold (90)", "Service worker not registered", "PWA manifest validation failed", "External dependencies unavailable: Google Maps API"], "recommendations": ["Fix critical issues before deployment", "Address warnings for optimal production performance", "Review and fix TypeScript compilation errors", "Fix failing tests to ensure application stability", "Optimize application performance for better user experience"]}, "originalReport": {"timestamp": "2025-07-31T00:47:34.419Z", "overallStatus": "failed", "buildVerification": {"success": false, "buildTime": 0, "errors": [{"file": "src/components/ProductCard.tsx", "line": 45, "column": 12, "message": "Property \"price\" does not exist on type \"Product\"", "type": "typescript"}, {"file": "src/utils/api.ts", "line": 23, "column": 8, "message": "Cannot find module \"./config\"", "type": "bundling"}], "warnings": [{"file": "src/hooks/useSearch.ts", "line": 15, "column": 5, "message": "Unused variable \"searchHistory\"", "type": "typescript"}], "outputSize": {"total": 0, "chunks": []}}, "testResults": [{"passed": false, "duration": 2500, "testCount": 35, "failures": [{"testName": "ProductCard should display price correctly", "error": "TypeError: Cannot read property \"price\" of undefined", "stack": "TypeError: Cannot read property \"price\" of undefined\n    at ProductCard.test.tsx:25:15", "duration": 150}, {"testName": "API integration should handle errors", "error": "Module not found: ./config", "stack": "Module not found: ./config\n    at api.test.ts:10:1", "duration": 100}]}], "performanceMetrics": {"lcp": 0, "fid": 0, "cls": 0, "fcp": 0, "lighthouse": {"performance": 0, "accessibility": 0, "bestPractices": 0, "seo": 0}}, "accessibilityResults": {"compliant": false, "violations": [], "warnings": [], "testedPages": []}, "pwaValidation": {"serviceWorkerRegistered": false, "manifestValid": false, "offlineFunctionality": false, "installable": false, "cacheStrategy": {"staticAssetsCache": false, "apiResponseCache": false, "offlinePages": []}}, "dependencyStatus": {"googleMaps": {"service": "Google Maps API", "available": false, "responseTime": 0, "error": "Network timeout"}, "cdnResources": [], "apiEndpoints": []}, "recommendations": ["Fix critical build errors before deployment", "Resolve TypeScript compilation issues", "Address module resolution problems", "Cannot proceed with deployment until build succeeds"], "deploymentReady": false}, "detailedSections": [{"name": "Build Verification", "status": "failed", "summary": "Build failed with 2 errors and 1 warnings", "details": {"success": false, "buildTime": 0, "errors": [{"file": "src/components/ProductCard.tsx", "line": 45, "column": 12, "message": "Property \"price\" does not exist on type \"Product\"", "type": "typescript"}, {"file": "src/utils/api.ts", "line": 23, "column": 8, "message": "Cannot find module \"./config\"", "type": "bundling"}], "warnings": [{"file": "src/hooks/useSearch.ts", "line": 15, "column": 5, "message": "Unused variable \"searchHistory\"", "type": "typescript"}], "outputSize": {"total": 0, "chunks": []}}, "errors": ["src/components/ProductCard.tsx:45:12 - Property \"price\" does not exist on type \"Product\"", "src/utils/api.ts:23:8 - Cannot find module \"./config\""], "warnings": ["src/hooks/useSearch.ts:15:5 - Unused variable \"searchHistory\""], "recommendations": ["Fix TypeScript compilation errors before deployment", "Address build warnings to improve code quality"]}, {"name": "Test Results", "status": "failed", "summary": "0/1 test suites passed. 33/35 individual tests passed", "details": [{"passed": false, "duration": 2500, "testCount": 35, "failures": [{"testName": "ProductCard should display price correctly", "error": "TypeError: Cannot read property \"price\" of undefined", "stack": "TypeError: Cannot read property \"price\" of undefined\n    at ProductCard.test.tsx:25:15", "duration": 150}, {"testName": "API integration should handle errors", "error": "Module not found: ./config", "stack": "Module not found: ./config\n    at api.test.ts:10:1", "duration": 100}]}], "errors": ["Test suite 1 failed", "ProductCard should display price correctly: TypeError: Cannot read property \"price\" of undefined", "API integration should handle errors: Module not found: ./config"], "warnings": [], "recommendations": ["Fix failing test suites before deployment", "Review and fix individual test failures"]}, {"name": "Performance Metrics", "status": "warning", "summary": "LCP: 0ms, FID: 0ms, CLS: 0, Lighthouse: 0/100", "details": {"lcp": 0, "fid": 0, "cls": 0, "fcp": 0, "lighthouse": {"performance": 0, "accessibility": 0, "bestPractices": 0, "seo": 0}}, "errors": ["Lighthouse performance score (0) critically low"], "warnings": [], "recommendations": ["Follow Lighthouse recommendations to improve overall performance score"]}, {"name": "Accessibility Validation", "status": "warning", "summary": "0 violations found (0 critical, 0 serious)", "details": {"compliant": false, "violations": [], "warnings": [], "testedPages": []}, "errors": [], "warnings": [], "recommendations": []}, {"name": "PWA Validation", "status": "warning", "summary": "SW not registered, Manifest invalid, Offline not ready, Not installable", "details": {"serviceWorkerRegistered": false, "manifestValid": false, "offlineFunctionality": false, "installable": false, "cacheStrategy": {"staticAssetsCache": false, "apiResponseCache": false, "offlinePages": []}}, "errors": ["Service worker not registered", "PWA manifest validation failed"], "warnings": ["Offline functionality not working properly", "PWA not installable", "Static assets not properly cached"], "recommendations": ["Implement and register service worker for PWA functionality", "Fix PWA manifest validation issues", "Implement offline functionality with proper caching strategy"]}, {"name": "External Dependencies", "status": "warning", "summary": "0/1 external dependencies available", "details": {"googleMaps": {"service": "Google Maps API", "available": false, "responseTime": 0, "error": "Network timeout"}, "cdnResources": [], "apiEndpoints": []}, "errors": ["Google Maps API unavailable: Network timeout"], "warnings": [], "recommendations": ["Verify external service availability before deployment", "Consider implementing fallback mechanisms for external dependencies"]}], "rawData": {"buildVerification": {"success": false, "buildTime": 0, "errors": [{"file": "src/components/ProductCard.tsx", "line": 45, "column": 12, "message": "Property \"price\" does not exist on type \"Product\"", "type": "typescript"}, {"file": "src/utils/api.ts", "line": 23, "column": 8, "message": "Cannot find module \"./config\"", "type": "bundling"}], "warnings": [{"file": "src/hooks/useSearch.ts", "line": 15, "column": 5, "message": "Unused variable \"searchHistory\"", "type": "typescript"}], "outputSize": {"total": 0, "chunks": []}}, "testResults": [{"passed": false, "duration": 2500, "testCount": 35, "failures": [{"testName": "ProductCard should display price correctly", "error": "TypeError: Cannot read property \"price\" of undefined", "stack": "TypeError: Cannot read property \"price\" of undefined\n    at ProductCard.test.tsx:25:15", "duration": 150}, {"testName": "API integration should handle errors", "error": "Module not found: ./config", "stack": "Module not found: ./config\n    at api.test.ts:10:1", "duration": 100}]}], "performanceMetrics": {"lcp": 0, "fid": 0, "cls": 0, "fcp": 0, "lighthouse": {"performance": 0, "accessibility": 0, "bestPractices": 0, "seo": 0}}, "accessibilityResults": {"compliant": false, "violations": [], "warnings": [], "testedPages": []}, "pwaValidation": {"serviceWorkerRegistered": false, "manifestValid": false, "offlineFunctionality": false, "installable": false, "cacheStrategy": {"staticAssetsCache": false, "apiResponseCache": false, "offlinePages": []}}, "dependencyStatus": {"googleMaps": {"service": "Google Maps API", "available": false, "responseTime": 0, "error": "Network timeout"}, "cdnResources": [], "apiEndpoints": []}}}