{"metadata": {"generatedAt": "2025-07-31T00:47:34.405Z", "version": "1.0.0", "reportType": "production-deployment-verification"}, "summary": {"overallStatus": "passed", "deploymentReady": true, "totalChecks": 14, "passedChecks": 14, "failedChecks": 0, "warningChecks": 0, "executionTime": 0, "criticalIssues": [], "recommendations": ["All verification checks passed - ready for deployment"]}, "deploymentDecision": {"ready": true, "confidence": 100, "blockers": [], "warnings": [], "recommendations": ["All verification checks passed - ready for deployment"]}, "originalReport": {"timestamp": "2025-07-31T00:47:34.405Z", "overallStatus": "passed", "buildVerification": {"success": true, "buildTime": 8500, "errors": [], "warnings": [], "outputSize": {"total": 1572864, "chunks": [{"name": "main", "size": 786432, "modules": ["src/main.tsx", "src/App.tsx"]}, {"name": "vendor", "size": 524288, "modules": ["node_modules/react"]}, {"name": "utils", "size": 262144, "modules": ["src/utils/index.ts"]}]}}, "testResults": [{"passed": true, "duration": 5200, "testCount": 35, "failures": [], "coverage": {"lines": 92, "functions": 95, "branches": 88, "statements": 93}}, {"passed": true, "duration": 8500, "testCount": 28, "failures": [], "coverage": {"lines": 87, "functions": 90, "branches": 85, "statements": 88}}], "performanceMetrics": {"lcp": 1800, "fid": 45, "cls": 0.05, "fcp": 1200, "lighthouse": {"performance": 96, "accessibility": 98, "bestPractices": 92, "seo": 95}}, "accessibilityResults": {"compliant": true, "violations": [], "warnings": [], "testedPages": ["/", "/team-sales", "/harbor-city", "/products"]}, "pwaValidation": {"serviceWorkerRegistered": true, "manifestValid": true, "offlineFunctionality": true, "installable": true, "cacheStrategy": {"staticAssetsCache": true, "apiResponseCache": true, "offlinePages": ["/", "/offline", "/team-sales"]}}, "dependencyStatus": {"googleMaps": {"service": "Google Maps API", "available": true, "responseTime": 120}, "cdnResources": [{"service": "Google Fonts", "available": true, "responseTime": 85}, {"service": "Font Awesome CDN", "available": true, "responseTime": 95}], "apiEndpoints": [{"service": "Weather API", "available": true, "responseTime": 180}]}, "recommendations": ["All verification checks passed - ready for deployment", "Consider implementing additional performance monitoring", "Maintain current accessibility standards"], "deploymentReady": true}, "detailedSections": [{"name": "Build Verification", "status": "passed", "summary": "Build completed successfully in 8500ms. Output size: 1.50MB", "details": {"success": true, "buildTime": 8500, "errors": [], "warnings": [], "outputSize": {"total": 1572864, "chunks": [{"name": "main", "size": 786432, "modules": ["src/main.tsx", "src/App.tsx"]}, {"name": "vendor", "size": 524288, "modules": ["node_modules/react"]}, {"name": "utils", "size": 262144, "modules": ["src/utils/index.ts"]}]}}, "errors": [], "warnings": [], "recommendations": []}, {"name": "Test Results", "status": "passed", "summary": "2/2 test suites passed. 63/63 individual tests passed", "details": [{"passed": true, "duration": 5200, "testCount": 35, "failures": [], "coverage": {"lines": 92, "functions": 95, "branches": 88, "statements": 93}}, {"passed": true, "duration": 8500, "testCount": 28, "failures": [], "coverage": {"lines": 87, "functions": 90, "branches": 85, "statements": 88}}], "errors": [], "warnings": [], "recommendations": []}, {"name": "Performance Metrics", "status": "passed", "summary": "LCP: 1800ms, FID: 45ms, CLS: 0.05, Lighthouse: 96/100", "details": {"lcp": 1800, "fid": 45, "cls": 0.05, "fcp": 1200, "lighthouse": {"performance": 96, "accessibility": 98, "bestPractices": 92, "seo": 95}}, "errors": [], "warnings": [], "recommendations": []}, {"name": "Accessibility Validation", "status": "passed", "summary": "WCAG compliance achieved. 4 pages tested", "details": {"compliant": true, "violations": [], "warnings": [], "testedPages": ["/", "/team-sales", "/harbor-city", "/products"]}, "errors": [], "warnings": [], "recommendations": []}, {"name": "PWA Validation", "status": "passed", "summary": "SW registered, Manifest valid, Offline ready, Installable", "details": {"serviceWorkerRegistered": true, "manifestValid": true, "offlineFunctionality": true, "installable": true, "cacheStrategy": {"staticAssetsCache": true, "apiResponseCache": true, "offlinePages": ["/", "/offline", "/team-sales"]}}, "errors": [], "warnings": [], "recommendations": []}, {"name": "External Dependencies", "status": "passed", "summary": "4/4 external dependencies available", "details": {"googleMaps": {"service": "Google Maps API", "available": true, "responseTime": 120}, "cdnResources": [{"service": "Google Fonts", "available": true, "responseTime": 85}, {"service": "Font Awesome CDN", "available": true, "responseTime": 95}], "apiEndpoints": [{"service": "Weather API", "available": true, "responseTime": 180}]}, "errors": [], "warnings": [], "recommendations": []}], "rawData": {"buildVerification": {"success": true, "buildTime": 8500, "errors": [], "warnings": [], "outputSize": {"total": 1572864, "chunks": [{"name": "main", "size": 786432, "modules": ["src/main.tsx", "src/App.tsx"]}, {"name": "vendor", "size": 524288, "modules": ["node_modules/react"]}, {"name": "utils", "size": 262144, "modules": ["src/utils/index.ts"]}]}}, "testResults": [{"passed": true, "duration": 5200, "testCount": 35, "failures": [], "coverage": {"lines": 92, "functions": 95, "branches": 88, "statements": 93}}, {"passed": true, "duration": 8500, "testCount": 28, "failures": [], "coverage": {"lines": 87, "functions": 90, "branches": 85, "statements": 88}}], "performanceMetrics": {"lcp": 1800, "fid": 45, "cls": 0.05, "fcp": 1200, "lighthouse": {"performance": 96, "accessibility": 98, "bestPractices": 92, "seo": 95}}, "accessibilityResults": {"compliant": true, "violations": [], "warnings": [], "testedPages": ["/", "/team-sales", "/harbor-city", "/products"]}, "pwaValidation": {"serviceWorkerRegistered": true, "manifestValid": true, "offlineFunctionality": true, "installable": true, "cacheStrategy": {"staticAssetsCache": true, "apiResponseCache": true, "offlinePages": ["/", "/offline", "/team-sales"]}}, "dependencyStatus": {"googleMaps": {"service": "Google Maps API", "available": true, "responseTime": 120}, "cdnResources": [{"service": "Google Fonts", "available": true, "responseTime": 85}, {"service": "Font Awesome CDN", "available": true, "responseTime": 95}], "apiEndpoints": [{"service": "Weather API", "available": true, "responseTime": 180}]}}}