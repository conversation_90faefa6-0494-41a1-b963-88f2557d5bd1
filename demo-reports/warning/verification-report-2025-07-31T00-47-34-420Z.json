{"metadata": {"generatedAt": "2025-07-31T00:47:34.420Z", "version": "1.0.0", "reportType": "production-deployment-verification"}, "summary": {"overallStatus": "warning", "deploymentReady": false, "totalChecks": 14, "passedChecks": 7, "failedChecks": 1, "warningChecks": 6, "executionTime": 0, "criticalIssues": ["1 test suites failed"], "recommendations": ["Fix critical issues before deployment", "Address warnings for optimal production performance", "Fix failing tests to ensure application stability", "Optimize application performance for better user experience"]}, "deploymentDecision": {"ready": false, "confidence": 16, "blockers": ["1 test suites failed"], "warnings": ["2 build warnings found", "1 individual test failures found", "LCP (2800ms) exceeds threshold (2500ms)", "FID (120ms) exceeds threshold (100ms)", "CLS (0.12) exceeds threshold (0.1)", "Lighthouse performance score (75) below threshold (90)", "2 minor accessibility violations found", "External dependencies unavailable: Font Awesome CDN"], "recommendations": ["Fix critical issues before deployment", "Address warnings for optimal production performance", "Fix failing tests to ensure application stability", "Optimize application performance for better user experience"]}, "originalReport": {"timestamp": "2025-07-31T00:47:34.420Z", "overallStatus": "warning", "buildVerification": {"success": true, "buildTime": 15200, "errors": [], "warnings": [{"file": "src/components/Header.tsx", "line": 32, "column": 8, "message": "React Hook useEffect has a missing dependency", "type": "typescript"}, {"file": "src/pages/TeamSales.tsx", "line": 67, "column": 15, "message": "img element should have alt attribute", "type": "typescript"}], "outputSize": {"total": 4194304, "chunks": [{"name": "main", "size": 2097152, "modules": ["src/main.tsx", "src/App.tsx"]}, {"name": "vendor", "size": 1572864, "modules": ["node_modules/react", "node_modules/react-dom"]}, {"name": "utils", "size": 524288, "modules": ["src/utils/index.ts"]}]}}, "testResults": [{"passed": true, "duration": 6800, "testCount": 42, "failures": [], "coverage": {"lines": 78, "functions": 82, "branches": 75, "statements": 79}}, {"passed": false, "duration": 4200, "testCount": 18, "failures": [{"testName": "Mobile navigation should close on outside click", "error": "Expected element to be hidden but it was visible", "stack": "Error: Expected element to be hidden but it was visible\n    at navigation.test.tsx:89:12", "duration": 200}]}], "performanceMetrics": {"lcp": 2800, "fid": 120, "cls": 0.12, "fcp": 2200, "lighthouse": {"performance": 75, "accessibility": 88, "bestPractices": 85, "seo": 92}}, "accessibilityResults": {"compliant": false, "violations": [{"rule": "color-contrast", "impact": "moderate", "element": "button.secondary", "description": "Element has insufficient color contrast of 3.8:1"}, {"rule": "alt-text", "impact": "minor", "element": "img.decoration", "description": "Decorative image should have empty alt attribute"}], "warnings": [{"rule": "aria-labels", "element": "button.icon-only", "description": "Icon-only button should have aria-label"}], "testedPages": ["/", "/team-sales", "/harbor-city"]}, "pwaValidation": {"serviceWorkerRegistered": true, "manifestValid": true, "offlineFunctionality": false, "installable": true, "cacheStrategy": {"staticAssetsCache": true, "apiResponseCache": false, "offlinePages": ["/"]}}, "dependencyStatus": {"googleMaps": {"service": "Google Maps API", "available": true, "responseTime": 250}, "cdnResources": [{"service": "Google Fonts", "available": true, "responseTime": 150}, {"service": "Font Awesome CDN", "available": false, "responseTime": 0, "error": "Service temporarily unavailable"}], "apiEndpoints": [{"service": "Weather API", "available": true, "responseTime": 350}]}, "recommendations": ["Address performance issues to improve user experience", "Fix failing test case for mobile navigation", "Optimize bundle size to reduce load times", "Implement proper offline functionality for PWA", "Consider deployment with caution due to performance concerns"], "deploymentReady": false}, "detailedSections": [{"name": "Build Verification", "status": "passed", "summary": "Build completed successfully in 15200ms. Output size: 4.00MB", "details": {"success": true, "buildTime": 15200, "errors": [], "warnings": [{"file": "src/components/Header.tsx", "line": 32, "column": 8, "message": "React Hook useEffect has a missing dependency", "type": "typescript"}, {"file": "src/pages/TeamSales.tsx", "line": 67, "column": 15, "message": "img element should have alt attribute", "type": "typescript"}], "outputSize": {"total": 4194304, "chunks": [{"name": "main", "size": 2097152, "modules": ["src/main.tsx", "src/App.tsx"]}, {"name": "vendor", "size": 1572864, "modules": ["node_modules/react", "node_modules/react-dom"]}, {"name": "utils", "size": 524288, "modules": ["src/utils/index.ts"]}]}}, "errors": [], "warnings": ["src/components/Header.tsx:32:8 - React Hook useEffect has a missing dependency", "src/pages/TeamSales.tsx:67:15 - img element should have alt attribute"], "recommendations": ["Address build warnings to improve code quality"]}, {"name": "Test Results", "status": "failed", "summary": "1/2 test suites passed. 59/60 individual tests passed", "details": [{"passed": true, "duration": 6800, "testCount": 42, "failures": [], "coverage": {"lines": 78, "functions": 82, "branches": 75, "statements": 79}}, {"passed": false, "duration": 4200, "testCount": 18, "failures": [{"testName": "Mobile navigation should close on outside click", "error": "Expected element to be hidden but it was visible", "stack": "Error: Expected element to be hidden but it was visible\n    at navigation.test.tsx:89:12", "duration": 200}]}], "errors": ["Test suite 2 failed", "Mobile navigation should close on outside click: Expected element to be hidden but it was visible"], "warnings": [], "recommendations": ["Fix failing test suites before deployment", "Review and fix individual test failures"]}, {"name": "Performance Metrics", "status": "failed", "summary": "LCP: 2800ms, FID: 120ms, CLS: 0.12, Lighthouse: 75/100", "details": {"lcp": 2800, "fid": 120, "cls": 0.12, "fcp": 2200, "lighthouse": {"performance": 75, "accessibility": 88, "bestPractices": 85, "seo": 92}}, "errors": [], "warnings": ["LCP (2800ms) exceeds recommended threshold", "FID (120ms) exceeds recommended threshold", "CLS (0.12) exceeds recommended threshold", "Lighthouse performance score (75) below recommended threshold"], "recommendations": ["Optimize Largest Contentful Paint by reducing server response times and optimizing critical resources", "Improve First Input Delay by reducing JavaScript execution time and optimizing event handlers", "Reduce Cumulative Layout Shift by setting explicit dimensions for images and avoiding dynamic content insertion", "Follow Lighthouse recommendations to improve overall performance score"]}, {"name": "Accessibility Validation", "status": "warning", "summary": "2 violations found (0 critical, 0 serious)", "details": {"compliant": false, "violations": [{"rule": "color-contrast", "impact": "moderate", "element": "button.secondary", "description": "Element has insufficient color contrast of 3.8:1"}, {"rule": "alt-text", "impact": "minor", "element": "img.decoration", "description": "Decorative image should have empty alt attribute"}], "warnings": [{"rule": "aria-labels", "element": "button.icon-only", "description": "Icon-only button should have aria-label"}], "testedPages": ["/", "/team-sales", "/harbor-city"]}, "errors": [], "warnings": ["color-contrast: Element has insufficient color contrast of 3.8:1 (button.secondary)", "alt-text: Decorative image should have empty alt attribute (img.decoration)", "aria-labels: Icon-only button should have aria-label (button.icon-only)"], "recommendations": ["Review accessibility violations and implement fixes according to WCAG guidelines"]}, {"name": "PWA Validation", "status": "warning", "summary": "SW registered, Manifest valid, Offline not ready, Installable", "details": {"serviceWorkerRegistered": true, "manifestValid": true, "offlineFunctionality": false, "installable": true, "cacheStrategy": {"staticAssetsCache": true, "apiResponseCache": false, "offlinePages": ["/"]}}, "errors": [], "warnings": ["Offline functionality not working properly"], "recommendations": ["Implement offline functionality with proper caching strategy"]}, {"name": "External Dependencies", "status": "warning", "summary": "3/4 external dependencies available", "details": {"googleMaps": {"service": "Google Maps API", "available": true, "responseTime": 250}, "cdnResources": [{"service": "Google Fonts", "available": true, "responseTime": 150}, {"service": "Font Awesome CDN", "available": false, "responseTime": 0, "error": "Service temporarily unavailable"}], "apiEndpoints": [{"service": "Weather API", "available": true, "responseTime": 350}]}, "errors": [], "warnings": ["CDN resource unavailable: Font Awesome CDN - Service temporarily unavailable"], "recommendations": ["Verify external service availability before deployment", "Consider implementing fallback mechanisms for external dependencies"]}], "rawData": {"buildVerification": {"success": true, "buildTime": 15200, "errors": [], "warnings": [{"file": "src/components/Header.tsx", "line": 32, "column": 8, "message": "React Hook useEffect has a missing dependency", "type": "typescript"}, {"file": "src/pages/TeamSales.tsx", "line": 67, "column": 15, "message": "img element should have alt attribute", "type": "typescript"}], "outputSize": {"total": 4194304, "chunks": [{"name": "main", "size": 2097152, "modules": ["src/main.tsx", "src/App.tsx"]}, {"name": "vendor", "size": 1572864, "modules": ["node_modules/react", "node_modules/react-dom"]}, {"name": "utils", "size": 524288, "modules": ["src/utils/index.ts"]}]}}, "testResults": [{"passed": true, "duration": 6800, "testCount": 42, "failures": [], "coverage": {"lines": 78, "functions": 82, "branches": 75, "statements": 79}}, {"passed": false, "duration": 4200, "testCount": 18, "failures": [{"testName": "Mobile navigation should close on outside click", "error": "Expected element to be hidden but it was visible", "stack": "Error: Expected element to be hidden but it was visible\n    at navigation.test.tsx:89:12", "duration": 200}]}], "performanceMetrics": {"lcp": 2800, "fid": 120, "cls": 0.12, "fcp": 2200, "lighthouse": {"performance": 75, "accessibility": 88, "bestPractices": 85, "seo": 92}}, "accessibilityResults": {"compliant": false, "violations": [{"rule": "color-contrast", "impact": "moderate", "element": "button.secondary", "description": "Element has insufficient color contrast of 3.8:1"}, {"rule": "alt-text", "impact": "minor", "element": "img.decoration", "description": "Decorative image should have empty alt attribute"}], "warnings": [{"rule": "aria-labels", "element": "button.icon-only", "description": "Icon-only button should have aria-label"}], "testedPages": ["/", "/team-sales", "/harbor-city"]}, "pwaValidation": {"serviceWorkerRegistered": true, "manifestValid": true, "offlineFunctionality": false, "installable": true, "cacheStrategy": {"staticAssetsCache": true, "apiResponseCache": false, "offlinePages": ["/"]}}, "dependencyStatus": {"googleMaps": {"service": "Google Maps API", "available": true, "responseTime": 250}, "cdnResources": [{"service": "Google Fonts", "available": true, "responseTime": 150}, {"service": "Font Awesome CDN", "available": false, "responseTime": 0, "error": "Service temporarily unavailable"}], "apiEndpoints": [{"service": "Weather API", "available": true, "responseTime": 350}]}}}