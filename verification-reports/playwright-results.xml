<testsuites id="" name="" tests="40" failures="31" skipped="0" errors="0" time="69.196421">
<testsuite name="privacy-policy.spec.ts" timestamp="2025-08-02T00:27:59.086Z" hostname="chromium" tests="8" failures="6" skipped="0" time="57.689" errors="0">
<testcase name="Privacy Policy Page › should load Privacy Policy page directly" classname="privacy-policy.spec.ts" time="7.543">
<failure message="privacy-policy.spec.ts:13:3 should load Privacy Policy page directly" type="FAILURE">
<![CDATA[  [chromium] › privacy-policy.spec.ts:13:3 › Privacy Policy Page › should load Privacy Policy page directly 

    Error: expect.toContainText: Error: strict mode violation: locator('h2') resolved to 9 elements:
        1) <h2 class="text-2xl font-semibold text-gray-900 mb-4">Information We Collect</h2> aka getByRole('heading', { name: 'Information We Collect' })
        2) <h2 class="text-2xl font-semibold text-gray-900 mb-4">How We Use Your Information</h2> aka getByRole('heading', { name: 'How We Use Your Information' })
        3) <h2 class="text-2xl font-semibold text-gray-900 mb-4">Data Storage and Security</h2> aka getByRole('heading', { name: 'Data Storage and Security' })
        4) <h2 class="text-2xl font-semibold text-gray-900 mb-4">Third-Party Services</h2> aka getByRole('heading', { name: 'Third-Party Services' })
        5) <h2 class="text-2xl font-semibold text-gray-900 mb-4">Cookies and Tracking Technologies</h2> aka getByRole('heading', { name: 'Cookies and Tracking' })
        6) <h2 class="text-2xl font-semibold text-gray-900 mb-4">Your Privacy Rights</h2> aka getByRole('heading', { name: 'Your Privacy Rights' })
        7) <h2 class="text-2xl font-semibold text-gray-900 mb-4">Children's Privacy</h2> aka getByRole('heading', { name: 'Children\'s Privacy' })
        8) <h2 class="text-2xl font-semibold text-gray-900 mb-4">Changes to This Privacy Policy</h2> aka getByRole('heading', { name: 'Changes to This Privacy Policy' })
        9) <h2 class="text-2xl font-semibold text-gray-900 mb-4">Contact Us</h2> aka getByRole('main').getByRole('heading', { name: 'Contact Us' })

    Call log:
      - Expect "toContainText" with timeout 10000ms
      - waiting for locator('h2')


      21 |     
      22 |     // Check for key sections
    > 23 |     await expect(page.locator('h2')).toContainText('Information We Collect');
         |                                      ^
      24 |     await expect(page.locator('h2')).toContainText('How We Use Your Information');
      25 |     await expect(page.locator('h2')).toContainText('Data Storage and Security');
      26 |     await expect(page.locator('h2')).toContainText('Contact Us');
        at /Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts:23:38

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-70c35-rivacy-Policy-page-directly-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-70c35-rivacy-Policy-page-directly-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|playwright-artifacts/privacy-policy-Privacy-Pol-70c35-rivacy-Policy-page-directly-chromium/test-failed-1.png]]

[[ATTACHMENT|playwright-artifacts/privacy-policy-Privacy-Pol-70c35-rivacy-Policy-page-directly-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Privacy Policy Page › should navigate to Privacy Policy from footer link" classname="privacy-policy.spec.ts" time="8.089">
</testcase>
<testcase name="Privacy Policy Page › should display contact information" classname="privacy-policy.spec.ts" time="7.585">
<failure message="privacy-policy.spec.ts:44:3 should display contact information" type="FAILURE">
<![CDATA[  [chromium] › privacy-policy.spec.ts:44:3 › Privacy Policy Page › should display contact information 

    Error: expect.toBeVisible: Error: strict mode violation: locator('section').filter({ hasText: 'Contact Us' }) resolved to 2 elements:
        1) <section>…</section> aka getByText('Your Privacy RightsYou have')
        2) <section>…</section> aka getByText('Contact UsIf you have any')

    Call log:
      - Expect "toBeVisible" with timeout 10000ms
      - waiting for locator('section').filter({ hasText: 'Contact Us' })


      47 |     // Check for contact section
      48 |     const contactSection = page.locator('section').filter({ hasText: 'Contact Us' });
    > 49 |     await expect(contactSection).toBeVisible();
         |                                  ^
      50 |     
      51 |     // Check for business details
      52 |     await expect(contactSection).toContainText('The Ice Box Hockey');
        at /Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts:49:34

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-464f2-display-contact-information-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-464f2-display-contact-information-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|playwright-artifacts/privacy-policy-Privacy-Pol-464f2-display-contact-information-chromium/test-failed-1.png]]

[[ATTACHMENT|playwright-artifacts/privacy-policy-Privacy-Pol-464f2-display-contact-information-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Privacy Policy Page › should have proper SEO meta tags" classname="privacy-policy.spec.ts" time="7.887">
<failure message="privacy-policy.spec.ts:58:3 should have proper SEO meta tags" type="FAILURE">
<![CDATA[  [chromium] › privacy-policy.spec.ts:58:3 › Privacy Policy Page › should have proper SEO meta tags 

    Error: expect.toHaveAttribute: Error: strict mode violation: locator('meta[name="description"]') resolved to 2 elements:
        1) <meta name="description" content="Ice Box Hockey - Your premier destination for hockey equipment, gear, and accessories. Find top brands, expert advice, and everything you need for the game."/> aka locator('meta[name="description"]').first()
        2) <meta data-rh="true" name="description" content="Learn about how The Ice Box Hockey collects, uses, and protects your personal information. Read our comprehensive privacy policy."/> aka locator('meta[name="description"]').nth(1)

    Call log:
      - Expect "toHaveAttribute" with timeout 10000ms
      - waiting for locator('meta[name="description"]')
        3 × locator resolved to <meta name="description" content="Ice Box Hockey - Your premier destination for hockey equipment, gear, and accessories. Find top brands, expert advice, and everything you need for the game."/>
          - unexpected value "Ice Box Hockey - Your premier destination for hockey equipment, gear, and accessories. Find top brands, expert advice, and everything you need for the game."


      61 |     // Check meta description
      62 |     const metaDescription = page.locator('meta[name="description"]');
    > 63 |     await expect(metaDescription).toHaveAttribute('content', /privacy policy.*Ice Box Hockey/);
         |                                   ^
      64 |     
      65 |     // Check title
      66 |     await expect(page).toHaveTitle(/Privacy Policy.*Ice Box Hockey/);
        at /Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts:63:35

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-7b6a4-d-have-proper-SEO-meta-tags-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-7b6a4-d-have-proper-SEO-meta-tags-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|playwright-artifacts/privacy-policy-Privacy-Pol-7b6a4-d-have-proper-SEO-meta-tags-chromium/test-failed-1.png]]

[[ATTACHMENT|playwright-artifacts/privacy-policy-Privacy-Pol-7b6a4-d-have-proper-SEO-meta-tags-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Privacy Policy Page › should be responsive on mobile" classname="privacy-policy.spec.ts" time="7.773">
<failure message="privacy-policy.spec.ts:69:3 should be responsive on mobile" type="FAILURE">
<![CDATA[  [chromium] › privacy-policy.spec.ts:69:3 › Privacy Policy Page › should be responsive on mobile ──

    Error: expect.toBeVisible: Error: strict mode violation: locator('section').filter({ hasText: 'Contact Us' }) resolved to 2 elements:
        1) <section>…</section> aka getByText('Your Privacy RightsYou have')
        2) <section>…</section> aka getByText('Contact UsIf you have any')

    Call log:
      - Expect "toBeVisible" with timeout 10000ms
      - waiting for locator('section').filter({ hasText: 'Contact Us' })


      78 |     // Check if contact section is readable on mobile
      79 |     const contactSection = page.locator('section').filter({ hasText: 'Contact Us' });
    > 80 |     await expect(contactSection).toBeVisible();
         |                                  ^
      81 |   });
      82 |
      83 |   test('should have working email link', async ({ page }) => {
        at /Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts:80:34

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-f8298-uld-be-responsive-on-mobile-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-f8298-uld-be-responsive-on-mobile-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|playwright-artifacts/privacy-policy-Privacy-Pol-f8298-uld-be-responsive-on-mobile-chromium/test-failed-1.png]]

[[ATTACHMENT|playwright-artifacts/privacy-policy-Privacy-Pol-f8298-uld-be-responsive-on-mobile-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Privacy Policy Page › should have working email link" classname="privacy-policy.spec.ts" time="3.107">
<failure message="privacy-policy.spec.ts:83:3 should have working email link" type="FAILURE">
<![CDATA[  [chromium] › privacy-policy.spec.ts:83:3 › Privacy Policy Page › should have working email link ──

    Error: expect.toBeVisible: Error: strict mode violation: locator('a[href="mailto:<EMAIL>"]') resolved to 2 elements:
        1) <a href="mailto:<EMAIL>" class="text-blue-600 hover:text-blue-800"><EMAIL></a> aka getByRole('main').getByRole('link', { name: '<EMAIL>' })
        2) <a href="mailto:<EMAIL>" class="ml-3 text-white/90 hover:text-blue-400 transition-colors"><EMAIL></a> aka locator('#contact').getByRole('link', { name: '<EMAIL>' })

    Call log:
      - Expect "toBeVisible" with timeout 10000ms
      - waiting for locator('a[href="mailto:<EMAIL>"]')


      86 |     // Check for email link in contact section
      87 |     const emailLink = page.locator('a[href="mailto:<EMAIL>"]');
    > 88 |     await expect(emailLink).toBeVisible();
         |                             ^
      89 |     await expect(emailLink).toHaveAttribute('href', 'mailto:<EMAIL>');
      90 |   });
      91 |
        at /Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts:88:29

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-c02f5-uld-have-working-email-link-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-c02f5-uld-have-working-email-link-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|playwright-artifacts/privacy-policy-Privacy-Pol-c02f5-uld-have-working-email-link-chromium/test-failed-1.png]]

[[ATTACHMENT|playwright-artifacts/privacy-policy-Privacy-Pol-c02f5-uld-have-working-email-link-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Privacy Policy Page › should display last updated date" classname="privacy-policy.spec.ts" time="3.26">
</testcase>
<testcase name="Privacy Policy Page › should have proper heading hierarchy" classname="privacy-policy.spec.ts" time="12.445">
<failure message="privacy-policy.spec.ts:103:3 should have proper heading hierarchy" type="FAILURE">
<![CDATA[  [chromium] › privacy-policy.spec.ts:103:3 › Privacy Policy Page › should have proper heading hierarchy 

    Error: Timed out 10000ms waiting for expect(locator).toHaveCount(expected)

    Locator: locator('h2')
    Expected: 8
    Received: 9
    Call log:
      - Expect "toHaveCount" with timeout 10000ms
      - waiting for locator('h2')
        13 × locator resolved to 9 elements
           - unexpected value "9"


      113 |     
      114 |     // Should have multiple h2 sections
    > 115 |     await expect(h2Elements).toHaveCount(8); // Based on our content structure
          |                              ^
      116 |     
      117 |     // Should have h3 subsections
      118 |     const h3Count = await h3Elements.count();
        at /Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts:115:30

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-10ffc-ve-proper-heading-hierarchy-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-10ffc-ve-proper-heading-hierarchy-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|playwright-artifacts/privacy-policy-Privacy-Pol-10ffc-ve-proper-heading-hierarchy-chromium/test-failed-1.png]]

[[ATTACHMENT|playwright-artifacts/privacy-policy-Privacy-Pol-10ffc-ve-proper-heading-hierarchy-chromium/error-context.md]]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="privacy-policy.spec.ts" timestamp="2025-08-02T00:27:59.086Z" hostname="firefox" tests="8" failures="7" skipped="0" time="58.414" errors="0">
<testcase name="Privacy Policy Page › should load Privacy Policy page directly" classname="privacy-policy.spec.ts" time="6.02">
<failure message="privacy-policy.spec.ts:13:3 should load Privacy Policy page directly" type="FAILURE">
<![CDATA[  [firefox] › privacy-policy.spec.ts:13:3 › Privacy Policy Page › should load Privacy Policy page directly 

    Error: expect.toContainText: Error: strict mode violation: locator('h2') resolved to 9 elements:
        1) <h2 class="text-2xl font-semibold text-gray-900 mb-4">Information We Collect</h2> aka getByRole('heading', { name: 'Information We Collect' })
        2) <h2 class="text-2xl font-semibold text-gray-900 mb-4">How We Use Your Information</h2> aka getByRole('heading', { name: 'How We Use Your Information' })
        3) <h2 class="text-2xl font-semibold text-gray-900 mb-4">Data Storage and Security</h2> aka getByRole('heading', { name: 'Data Storage and Security' })
        4) <h2 class="text-2xl font-semibold text-gray-900 mb-4">Third-Party Services</h2> aka getByRole('heading', { name: 'Third-Party Services' })
        5) <h2 class="text-2xl font-semibold text-gray-900 mb-4">Cookies and Tracking Technologies</h2> aka getByRole('heading', { name: 'Cookies and Tracking' })
        6) <h2 class="text-2xl font-semibold text-gray-900 mb-4">Your Privacy Rights</h2> aka getByRole('heading', { name: 'Your Privacy Rights' })
        7) <h2 class="text-2xl font-semibold text-gray-900 mb-4">Children's Privacy</h2> aka getByRole('heading', { name: 'Children\'s Privacy' })
        8) <h2 class="text-2xl font-semibold text-gray-900 mb-4">Changes to This Privacy Policy</h2> aka getByRole('heading', { name: 'Changes to This Privacy Policy' })
        9) <h2 class="text-2xl font-semibold text-gray-900 mb-4">Contact Us</h2> aka getByRole('main').getByRole('heading', { name: 'Contact Us' })

    Call log:
      - Expect "toContainText" with timeout 10000ms
      - waiting for locator('h2')


      21 |     
      22 |     // Check for key sections
    > 23 |     await expect(page.locator('h2')).toContainText('Information We Collect');
         |                                      ^
      24 |     await expect(page.locator('h2')).toContainText('How We Use Your Information');
      25 |     await expect(page.locator('h2')).toContainText('Data Storage and Security');
      26 |     await expect(page.locator('h2')).toContainText('Contact Us');
        at /Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts:23:38

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-70c35-rivacy-Policy-page-directly-firefox/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-70c35-rivacy-Policy-page-directly-firefox/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|playwright-artifacts/privacy-policy-Privacy-Pol-70c35-rivacy-Policy-page-directly-firefox/test-failed-1.png]]

[[ATTACHMENT|playwright-artifacts/privacy-policy-Privacy-Pol-70c35-rivacy-Policy-page-directly-firefox/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Privacy Policy Page › should navigate to Privacy Policy from footer link" classname="privacy-policy.spec.ts" time="5.885">
<failure message="privacy-policy.spec.ts:29:3 should navigate to Privacy Policy from footer link" type="FAILURE">
<![CDATA[  [firefox] › privacy-policy.spec.ts:29:3 › Privacy Policy Page › should navigate to Privacy Policy from footer link 

    Error: expect.toContainText: Error: strict mode violation: locator('h1') resolved to 2 elements:
        1) <h1 class="sr-only">The Ice Box - Premier Hockey Equipment Store</h1> aka getByText('The Ice Box - Premier Hockey')
        2) <h1 class="text-4xl md:text-5xl font-bold text-white mb-6 text-6xl md:text-7xl leading-tight mb-6">The Ice Box</h1> aka getByText('The Ice Box', { exact: true })

    Call log:
      - Expect "toContainText" with timeout 10000ms
      - waiting for locator('h1')


      39 |     // Verify navigation to Privacy Policy page
      40 |     await expect(page).toHaveURL('/privacy-policy');
    > 41 |     await expect(page.locator('h1')).toContainText('Privacy Policy');
         |                                      ^
      42 |   });
      43 |
      44 |   test('should display contact information', async ({ page }) => {
        at /Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts:41:38

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-5025e-acy-Policy-from-footer-link-firefox/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-5025e-acy-Policy-from-footer-link-firefox/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|playwright-artifacts/privacy-policy-Privacy-Pol-5025e-acy-Policy-from-footer-link-firefox/test-failed-1.png]]

[[ATTACHMENT|playwright-artifacts/privacy-policy-Privacy-Pol-5025e-acy-Policy-from-footer-link-firefox/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Privacy Policy Page › should display contact information" classname="privacy-policy.spec.ts" time="5.063">
<failure message="privacy-policy.spec.ts:44:3 should display contact information" type="FAILURE">
<![CDATA[  [firefox] › privacy-policy.spec.ts:44:3 › Privacy Policy Page › should display contact information 

    Error: expect.toBeVisible: Error: strict mode violation: locator('section').filter({ hasText: 'Contact Us' }) resolved to 2 elements:
        1) <section>…</section> aka getByText('Your Privacy RightsYou have')
        2) <section>…</section> aka getByText('Contact UsIf you have any')

    Call log:
      - Expect "toBeVisible" with timeout 10000ms
      - waiting for locator('section').filter({ hasText: 'Contact Us' })


      47 |     // Check for contact section
      48 |     const contactSection = page.locator('section').filter({ hasText: 'Contact Us' });
    > 49 |     await expect(contactSection).toBeVisible();
         |                                  ^
      50 |     
      51 |     // Check for business details
      52 |     await expect(contactSection).toContainText('The Ice Box Hockey');
        at /Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts:49:34

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-464f2-display-contact-information-firefox/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-464f2-display-contact-information-firefox/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|playwright-artifacts/privacy-policy-Privacy-Pol-464f2-display-contact-information-firefox/test-failed-1.png]]

[[ATTACHMENT|playwright-artifacts/privacy-policy-Privacy-Pol-464f2-display-contact-information-firefox/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Privacy Policy Page › should have proper SEO meta tags" classname="privacy-policy.spec.ts" time="6.209">
<failure message="privacy-policy.spec.ts:58:3 should have proper SEO meta tags" type="FAILURE">
<![CDATA[  [firefox] › privacy-policy.spec.ts:58:3 › Privacy Policy Page › should have proper SEO meta tags ─

    Error: expect.toHaveAttribute: Error: strict mode violation: locator('meta[name="description"]') resolved to 2 elements:
        1) <meta name="description" content="Ice Box Hockey - Your premier destination for hockey equipment, gear, and accessories. Find top brands, expert advice, and everything you need for the game."/> aka locator('meta[name="description"]').first()
        2) <meta data-rh="true" name="description" content="Learn about how The Ice Box Hockey collects, uses, and protects your personal information. Read our comprehensive privacy policy."/> aka locator('meta[name="description"]').nth(1)

    Call log:
      - Expect "toHaveAttribute" with timeout 10000ms
      - waiting for locator('meta[name="description"]')


      61 |     // Check meta description
      62 |     const metaDescription = page.locator('meta[name="description"]');
    > 63 |     await expect(metaDescription).toHaveAttribute('content', /privacy policy.*Ice Box Hockey/);
         |                                   ^
      64 |     
      65 |     // Check title
      66 |     await expect(page).toHaveTitle(/Privacy Policy.*Ice Box Hockey/);
        at /Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts:63:35

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-7b6a4-d-have-proper-SEO-meta-tags-firefox/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-7b6a4-d-have-proper-SEO-meta-tags-firefox/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|playwright-artifacts/privacy-policy-Privacy-Pol-7b6a4-d-have-proper-SEO-meta-tags-firefox/test-failed-1.png]]

[[ATTACHMENT|playwright-artifacts/privacy-policy-Privacy-Pol-7b6a4-d-have-proper-SEO-meta-tags-firefox/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Privacy Policy Page › should be responsive on mobile" classname="privacy-policy.spec.ts" time="5.856">
<failure message="privacy-policy.spec.ts:69:3 should be responsive on mobile" type="FAILURE">
<![CDATA[  [firefox] › privacy-policy.spec.ts:69:3 › Privacy Policy Page › should be responsive on mobile ───

    Error: expect.toBeVisible: Error: strict mode violation: locator('section').filter({ hasText: 'Contact Us' }) resolved to 2 elements:
        1) <section>…</section> aka getByText('Your Privacy RightsYou have')
        2) <section>…</section> aka getByText('Contact UsIf you have any')

    Call log:
      - Expect "toBeVisible" with timeout 10000ms
      - waiting for locator('section').filter({ hasText: 'Contact Us' })


      78 |     // Check if contact section is readable on mobile
      79 |     const contactSection = page.locator('section').filter({ hasText: 'Contact Us' });
    > 80 |     await expect(contactSection).toBeVisible();
         |                                  ^
      81 |   });
      82 |
      83 |   test('should have working email link', async ({ page }) => {
        at /Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts:80:34

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-f8298-uld-be-responsive-on-mobile-firefox/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-f8298-uld-be-responsive-on-mobile-firefox/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|playwright-artifacts/privacy-policy-Privacy-Pol-f8298-uld-be-responsive-on-mobile-firefox/test-failed-1.png]]

[[ATTACHMENT|playwright-artifacts/privacy-policy-Privacy-Pol-f8298-uld-be-responsive-on-mobile-firefox/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Privacy Policy Page › should have working email link" classname="privacy-policy.spec.ts" time="7.194">
<failure message="privacy-policy.spec.ts:83:3 should have working email link" type="FAILURE">
<![CDATA[  [firefox] › privacy-policy.spec.ts:83:3 › Privacy Policy Page › should have working email link ───

    Error: expect.toBeVisible: Error: strict mode violation: locator('a[href="mailto:<EMAIL>"]') resolved to 2 elements:
        1) <a href="mailto:<EMAIL>" class="text-blue-600 hover:text-blue-800"><EMAIL></a> aka getByRole('main').getByRole('link', { name: '<EMAIL>' })
        2) <a href="mailto:<EMAIL>" class="ml-3 text-white/90 hover:text-blue-400 transition-colors"><EMAIL></a> aka locator('#contact').getByRole('link', { name: '<EMAIL>' })

    Call log:
      - Expect "toBeVisible" with timeout 10000ms
      - waiting for locator('a[href="mailto:<EMAIL>"]')


      86 |     // Check for email link in contact section
      87 |     const emailLink = page.locator('a[href="mailto:<EMAIL>"]');
    > 88 |     await expect(emailLink).toBeVisible();
         |                             ^
      89 |     await expect(emailLink).toHaveAttribute('href', 'mailto:<EMAIL>');
      90 |   });
      91 |
        at /Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts:88:29

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-c02f5-uld-have-working-email-link-firefox/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-c02f5-uld-have-working-email-link-firefox/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|playwright-artifacts/privacy-policy-Privacy-Pol-c02f5-uld-have-working-email-link-firefox/test-failed-1.png]]

[[ATTACHMENT|playwright-artifacts/privacy-policy-Privacy-Pol-c02f5-uld-have-working-email-link-firefox/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Privacy Policy Page › should display last updated date" classname="privacy-policy.spec.ts" time="6.297">
</testcase>
<testcase name="Privacy Policy Page › should have proper heading hierarchy" classname="privacy-policy.spec.ts" time="15.89">
<failure message="privacy-policy.spec.ts:103:3 should have proper heading hierarchy" type="FAILURE">
<![CDATA[  [firefox] › privacy-policy.spec.ts:103:3 › Privacy Policy Page › should have proper heading hierarchy 

    Error: Timed out 10000ms waiting for expect(locator).toHaveCount(expected)

    Locator: locator('h2')
    Expected: 8
    Received: 9
    Call log:
      - Expect "toHaveCount" with timeout 10000ms
      - waiting for locator('h2')
        13 × locator resolved to 9 elements
           - unexpected value "9"


      113 |     
      114 |     // Should have multiple h2 sections
    > 115 |     await expect(h2Elements).toHaveCount(8); // Based on our content structure
          |                              ^
      116 |     
      117 |     // Should have h3 subsections
      118 |     const h3Count = await h3Elements.count();
        at /Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts:115:30

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-10ffc-ve-proper-heading-hierarchy-firefox/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-10ffc-ve-proper-heading-hierarchy-firefox/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|playwright-artifacts/privacy-policy-Privacy-Pol-10ffc-ve-proper-heading-hierarchy-firefox/test-failed-1.png]]

[[ATTACHMENT|playwright-artifacts/privacy-policy-Privacy-Pol-10ffc-ve-proper-heading-hierarchy-firefox/error-context.md]]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="privacy-policy.spec.ts" timestamp="2025-08-02T00:27:59.086Z" hostname="webkit" tests="8" failures="7" skipped="0" time="31.387" errors="0">
<testcase name="Privacy Policy Page › should load Privacy Policy page directly" classname="privacy-policy.spec.ts" time="3.866">
<failure message="privacy-policy.spec.ts:13:3 should load Privacy Policy page directly" type="FAILURE">
<![CDATA[  [webkit] › privacy-policy.spec.ts:13:3 › Privacy Policy Page › should load Privacy Policy page directly 

    Error: expect.toContainText: Error: strict mode violation: locator('h2') resolved to 9 elements:
        1) <h2 class="text-2xl font-semibold text-gray-900 mb-4">Information We Collect</h2> aka getByRole('heading', { name: 'Information We Collect' })
        2) <h2 class="text-2xl font-semibold text-gray-900 mb-4">How We Use Your Information</h2> aka getByRole('heading', { name: 'How We Use Your Information' })
        3) <h2 class="text-2xl font-semibold text-gray-900 mb-4">Data Storage and Security</h2> aka getByRole('heading', { name: 'Data Storage and Security' })
        4) <h2 class="text-2xl font-semibold text-gray-900 mb-4">Third-Party Services</h2> aka getByRole('heading', { name: 'Third-Party Services' })
        5) <h2 class="text-2xl font-semibold text-gray-900 mb-4">Cookies and Tracking Technologies</h2> aka getByRole('heading', { name: 'Cookies and Tracking' })
        6) <h2 class="text-2xl font-semibold text-gray-900 mb-4">Your Privacy Rights</h2> aka getByRole('heading', { name: 'Your Privacy Rights' })
        7) <h2 class="text-2xl font-semibold text-gray-900 mb-4">Children's Privacy</h2> aka getByRole('heading', { name: 'Children\'s Privacy' })
        8) <h2 class="text-2xl font-semibold text-gray-900 mb-4">Changes to This Privacy Policy</h2> aka getByRole('heading', { name: 'Changes to This Privacy Policy' })
        9) <h2 class="text-2xl font-semibold text-gray-900 mb-4">Contact Us</h2> aka getByRole('main').getByRole('heading', { name: 'Contact Us' })

    Call log:
      - Expect "toContainText" with timeout 10000ms
      - waiting for locator('h2')


      21 |     
      22 |     // Check for key sections
    > 23 |     await expect(page.locator('h2')).toContainText('Information We Collect');
         |                                      ^
      24 |     await expect(page.locator('h2')).toContainText('How We Use Your Information');
      25 |     await expect(page.locator('h2')).toContainText('Data Storage and Security');
      26 |     await expect(page.locator('h2')).toContainText('Contact Us');
        at /Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts:23:38

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-70c35-rivacy-Policy-page-directly-webkit/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-70c35-rivacy-Policy-page-directly-webkit/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|playwright-artifacts/privacy-policy-Privacy-Pol-70c35-rivacy-Policy-page-directly-webkit/test-failed-1.png]]

[[ATTACHMENT|playwright-artifacts/privacy-policy-Privacy-Pol-70c35-rivacy-Policy-page-directly-webkit/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Privacy Policy Page › should navigate to Privacy Policy from footer link" classname="privacy-policy.spec.ts" time="2.876">
<failure message="privacy-policy.spec.ts:29:3 should navigate to Privacy Policy from footer link" type="FAILURE">
<![CDATA[  [webkit] › privacy-policy.spec.ts:29:3 › Privacy Policy Page › should navigate to Privacy Policy from footer link 

    Error: expect.toContainText: Error: strict mode violation: locator('h1') resolved to 2 elements:
        1) <h1 class="sr-only">The Ice Box - Premier Hockey Equipment Store</h1> aka getByText('The Ice Box - Premier Hockey')
        2) <h1 class="text-4xl md:text-5xl font-bold text-white mb-6 text-6xl md:text-7xl leading-tight mb-6">The Ice Box</h1> aka getByText('The Ice Box', { exact: true })

    Call log:
      - Expect "toContainText" with timeout 10000ms
      - waiting for locator('h1')


      39 |     // Verify navigation to Privacy Policy page
      40 |     await expect(page).toHaveURL('/privacy-policy');
    > 41 |     await expect(page.locator('h1')).toContainText('Privacy Policy');
         |                                      ^
      42 |   });
      43 |
      44 |   test('should display contact information', async ({ page }) => {
        at /Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts:41:38

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-5025e-acy-Policy-from-footer-link-webkit/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-5025e-acy-Policy-from-footer-link-webkit/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|playwright-artifacts/privacy-policy-Privacy-Pol-5025e-acy-Policy-from-footer-link-webkit/test-failed-1.png]]

[[ATTACHMENT|playwright-artifacts/privacy-policy-Privacy-Pol-5025e-acy-Policy-from-footer-link-webkit/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Privacy Policy Page › should display contact information" classname="privacy-policy.spec.ts" time="2.373">
<failure message="privacy-policy.spec.ts:44:3 should display contact information" type="FAILURE">
<![CDATA[  [webkit] › privacy-policy.spec.ts:44:3 › Privacy Policy Page › should display contact information 

    Error: expect.toBeVisible: Error: strict mode violation: locator('section').filter({ hasText: 'Contact Us' }) resolved to 2 elements:
        1) <section>…</section> aka getByText('Your Privacy RightsYou have')
        2) <section>…</section> aka getByText('Contact UsIf you have any')

    Call log:
      - Expect "toBeVisible" with timeout 10000ms
      - waiting for locator('section').filter({ hasText: 'Contact Us' })


      47 |     // Check for contact section
      48 |     const contactSection = page.locator('section').filter({ hasText: 'Contact Us' });
    > 49 |     await expect(contactSection).toBeVisible();
         |                                  ^
      50 |     
      51 |     // Check for business details
      52 |     await expect(contactSection).toContainText('The Ice Box Hockey');
        at /Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts:49:34

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-464f2-display-contact-information-webkit/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-464f2-display-contact-information-webkit/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|playwright-artifacts/privacy-policy-Privacy-Pol-464f2-display-contact-information-webkit/test-failed-1.png]]

[[ATTACHMENT|playwright-artifacts/privacy-policy-Privacy-Pol-464f2-display-contact-information-webkit/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Privacy Policy Page › should have proper SEO meta tags" classname="privacy-policy.spec.ts" time="2.601">
<failure message="privacy-policy.spec.ts:58:3 should have proper SEO meta tags" type="FAILURE">
<![CDATA[  [webkit] › privacy-policy.spec.ts:58:3 › Privacy Policy Page › should have proper SEO meta tags ──

    Error: expect.toHaveAttribute: Error: strict mode violation: locator('meta[name="description"]') resolved to 2 elements:
        1) <meta name="description" content="Ice Box Hockey - Your premier destination for hockey equipment, gear, and accessories. Find top brands, expert advice, and everything you need for the game."/> aka locator('meta[name="description"]').first()
        2) <meta data-rh="true" name="description" content="Learn about how The Ice Box Hockey collects, uses, and protects your personal information. Read our comprehensive privacy policy."/> aka locator('meta[name="description"]').nth(1)

    Call log:
      - Expect "toHaveAttribute" with timeout 10000ms
      - waiting for locator('meta[name="description"]')
        3 × locator resolved to <meta name="description" content="Ice Box Hockey - Your premier destination for hockey equipment, gear, and accessories. Find top brands, expert advice, and everything you need for the game."/>
          - unexpected value "Ice Box Hockey - Your premier destination for hockey equipment, gear, and accessories. Find top brands, expert advice, and everything you need for the game."


      61 |     // Check meta description
      62 |     const metaDescription = page.locator('meta[name="description"]');
    > 63 |     await expect(metaDescription).toHaveAttribute('content', /privacy policy.*Ice Box Hockey/);
         |                                   ^
      64 |     
      65 |     // Check title
      66 |     await expect(page).toHaveTitle(/Privacy Policy.*Ice Box Hockey/);
        at /Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts:63:35

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-7b6a4-d-have-proper-SEO-meta-tags-webkit/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-7b6a4-d-have-proper-SEO-meta-tags-webkit/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|playwright-artifacts/privacy-policy-Privacy-Pol-7b6a4-d-have-proper-SEO-meta-tags-webkit/test-failed-1.png]]

[[ATTACHMENT|playwright-artifacts/privacy-policy-Privacy-Pol-7b6a4-d-have-proper-SEO-meta-tags-webkit/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Privacy Policy Page › should be responsive on mobile" classname="privacy-policy.spec.ts" time="2.631">
<failure message="privacy-policy.spec.ts:69:3 should be responsive on mobile" type="FAILURE">
<![CDATA[  [webkit] › privacy-policy.spec.ts:69:3 › Privacy Policy Page › should be responsive on mobile ────

    Error: expect.toBeVisible: Error: strict mode violation: locator('section').filter({ hasText: 'Contact Us' }) resolved to 2 elements:
        1) <section>…</section> aka getByText('Your Privacy RightsYou have')
        2) <section>…</section> aka getByText('Contact UsIf you have any')

    Call log:
      - Expect "toBeVisible" with timeout 10000ms
      - waiting for locator('section').filter({ hasText: 'Contact Us' })


      78 |     // Check if contact section is readable on mobile
      79 |     const contactSection = page.locator('section').filter({ hasText: 'Contact Us' });
    > 80 |     await expect(contactSection).toBeVisible();
         |                                  ^
      81 |   });
      82 |
      83 |   test('should have working email link', async ({ page }) => {
        at /Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts:80:34

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-f8298-uld-be-responsive-on-mobile-webkit/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-f8298-uld-be-responsive-on-mobile-webkit/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|playwright-artifacts/privacy-policy-Privacy-Pol-f8298-uld-be-responsive-on-mobile-webkit/test-failed-1.png]]

[[ATTACHMENT|playwright-artifacts/privacy-policy-Privacy-Pol-f8298-uld-be-responsive-on-mobile-webkit/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Privacy Policy Page › should have working email link" classname="privacy-policy.spec.ts" time="2.369">
<failure message="privacy-policy.spec.ts:83:3 should have working email link" type="FAILURE">
<![CDATA[  [webkit] › privacy-policy.spec.ts:83:3 › Privacy Policy Page › should have working email link ────

    Error: expect.toBeVisible: Error: strict mode violation: locator('a[href="mailto:<EMAIL>"]') resolved to 2 elements:
        1) <a href="mailto:<EMAIL>" class="text-blue-600 hover:text-blue-800"><EMAIL></a> aka getByRole('main').getByRole('link', { name: '<EMAIL>' })
        2) <a href="mailto:<EMAIL>" class="ml-3 text-white/90 hover:text-blue-400 transition-colors"><EMAIL></a> aka locator('#contact').getByRole('link', { name: '<EMAIL>' })

    Call log:
      - Expect "toBeVisible" with timeout 10000ms
      - waiting for locator('a[href="mailto:<EMAIL>"]')


      86 |     // Check for email link in contact section
      87 |     const emailLink = page.locator('a[href="mailto:<EMAIL>"]');
    > 88 |     await expect(emailLink).toBeVisible();
         |                             ^
      89 |     await expect(emailLink).toHaveAttribute('href', 'mailto:<EMAIL>');
      90 |   });
      91 |
        at /Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts:88:29

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-c02f5-uld-have-working-email-link-webkit/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-c02f5-uld-have-working-email-link-webkit/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|playwright-artifacts/privacy-policy-Privacy-Pol-c02f5-uld-have-working-email-link-webkit/test-failed-1.png]]

[[ATTACHMENT|playwright-artifacts/privacy-policy-Privacy-Pol-c02f5-uld-have-working-email-link-webkit/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Privacy Policy Page › should display last updated date" classname="privacy-policy.spec.ts" time="2.144">
</testcase>
<testcase name="Privacy Policy Page › should have proper heading hierarchy" classname="privacy-policy.spec.ts" time="12.527">
<failure message="privacy-policy.spec.ts:103:3 should have proper heading hierarchy" type="FAILURE">
<![CDATA[  [webkit] › privacy-policy.spec.ts:103:3 › Privacy Policy Page › should have proper heading hierarchy 

    Error: Timed out 10000ms waiting for expect(locator).toHaveCount(expected)

    Locator: locator('h2')
    Expected: 8
    Received: 9
    Call log:
      - Expect "toHaveCount" with timeout 10000ms
      - waiting for locator('h2')
        13 × locator resolved to 9 elements
           - unexpected value "9"


      113 |     
      114 |     // Should have multiple h2 sections
    > 115 |     await expect(h2Elements).toHaveCount(8); // Based on our content structure
          |                              ^
      116 |     
      117 |     // Should have h3 subsections
      118 |     const h3Count = await h3Elements.count();
        at /Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts:115:30

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-10ffc-ve-proper-heading-hierarchy-webkit/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-10ffc-ve-proper-heading-hierarchy-webkit/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|playwright-artifacts/privacy-policy-Privacy-Pol-10ffc-ve-proper-heading-hierarchy-webkit/test-failed-1.png]]

[[ATTACHMENT|playwright-artifacts/privacy-policy-Privacy-Pol-10ffc-ve-proper-heading-hierarchy-webkit/error-context.md]]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="privacy-policy.spec.ts" timestamp="2025-08-02T00:27:59.086Z" hostname="Mobile Chrome" tests="8" failures="6" skipped="0" time="34.319" errors="0">
<testcase name="Privacy Policy Page › should load Privacy Policy page directly" classname="privacy-policy.spec.ts" time="3.509">
<failure message="privacy-policy.spec.ts:13:3 should load Privacy Policy page directly" type="FAILURE">
<![CDATA[  [Mobile Chrome] › privacy-policy.spec.ts:13:3 › Privacy Policy Page › should load Privacy Policy page directly 

    Error: expect.toContainText: Error: strict mode violation: locator('h2') resolved to 9 elements:
        1) <h2 class="text-2xl font-semibold text-gray-900 mb-4">Information We Collect</h2> aka getByRole('heading', { name: 'Information We Collect' })
        2) <h2 class="text-2xl font-semibold text-gray-900 mb-4">How We Use Your Information</h2> aka getByRole('heading', { name: 'How We Use Your Information' })
        3) <h2 class="text-2xl font-semibold text-gray-900 mb-4">Data Storage and Security</h2> aka getByRole('heading', { name: 'Data Storage and Security' })
        4) <h2 class="text-2xl font-semibold text-gray-900 mb-4">Third-Party Services</h2> aka getByRole('heading', { name: 'Third-Party Services' })
        5) <h2 class="text-2xl font-semibold text-gray-900 mb-4">Cookies and Tracking Technologies</h2> aka getByRole('heading', { name: 'Cookies and Tracking' })
        6) <h2 class="text-2xl font-semibold text-gray-900 mb-4">Your Privacy Rights</h2> aka getByRole('heading', { name: 'Your Privacy Rights' })
        7) <h2 class="text-2xl font-semibold text-gray-900 mb-4">Children's Privacy</h2> aka getByRole('heading', { name: 'Children\'s Privacy' })
        8) <h2 class="text-2xl font-semibold text-gray-900 mb-4">Changes to This Privacy Policy</h2> aka getByRole('heading', { name: 'Changes to This Privacy Policy' })
        9) <h2 class="text-2xl font-semibold text-gray-900 mb-4">Contact Us</h2> aka getByRole('main').getByRole('heading', { name: 'Contact Us' })

    Call log:
      - Expect "toContainText" with timeout 10000ms
      - waiting for locator('h2')


      21 |     
      22 |     // Check for key sections
    > 23 |     await expect(page.locator('h2')).toContainText('Information We Collect');
         |                                      ^
      24 |     await expect(page.locator('h2')).toContainText('How We Use Your Information');
      25 |     await expect(page.locator('h2')).toContainText('Data Storage and Security');
      26 |     await expect(page.locator('h2')).toContainText('Contact Us');
        at /Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts:23:38

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-70c35-rivacy-Policy-page-directly-Mobile-Chrome/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-70c35-rivacy-Policy-page-directly-Mobile-Chrome/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|playwright-artifacts/privacy-policy-Privacy-Pol-70c35-rivacy-Policy-page-directly-Mobile-Chrome/test-failed-1.png]]

[[ATTACHMENT|playwright-artifacts/privacy-policy-Privacy-Pol-70c35-rivacy-Policy-page-directly-Mobile-Chrome/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Privacy Policy Page › should navigate to Privacy Policy from footer link" classname="privacy-policy.spec.ts" time="5.469">
</testcase>
<testcase name="Privacy Policy Page › should display contact information" classname="privacy-policy.spec.ts" time="3.478">
<failure message="privacy-policy.spec.ts:44:3 should display contact information" type="FAILURE">
<![CDATA[  [Mobile Chrome] › privacy-policy.spec.ts:44:3 › Privacy Policy Page › should display contact information 

    Error: expect.toBeVisible: Error: strict mode violation: locator('section').filter({ hasText: 'Contact Us' }) resolved to 2 elements:
        1) <section>…</section> aka getByText('Your Privacy RightsYou have')
        2) <section>…</section> aka getByText('Contact UsIf you have any')

    Call log:
      - Expect "toBeVisible" with timeout 10000ms
      - waiting for locator('section').filter({ hasText: 'Contact Us' })


      47 |     // Check for contact section
      48 |     const contactSection = page.locator('section').filter({ hasText: 'Contact Us' });
    > 49 |     await expect(contactSection).toBeVisible();
         |                                  ^
      50 |     
      51 |     // Check for business details
      52 |     await expect(contactSection).toContainText('The Ice Box Hockey');
        at /Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts:49:34

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-464f2-display-contact-information-Mobile-Chrome/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-464f2-display-contact-information-Mobile-Chrome/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|playwright-artifacts/privacy-policy-Privacy-Pol-464f2-display-contact-information-Mobile-Chrome/test-failed-1.png]]

[[ATTACHMENT|playwright-artifacts/privacy-policy-Privacy-Pol-464f2-display-contact-information-Mobile-Chrome/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Privacy Policy Page › should have proper SEO meta tags" classname="privacy-policy.spec.ts" time="1.594">
<failure message="privacy-policy.spec.ts:58:3 should have proper SEO meta tags" type="FAILURE">
<![CDATA[  [Mobile Chrome] › privacy-policy.spec.ts:58:3 › Privacy Policy Page › should have proper SEO meta tags 

    Error: expect.toHaveAttribute: Error: strict mode violation: locator('meta[name="description"]') resolved to 2 elements:
        1) <meta name="description" content="Ice Box Hockey - Your premier destination for hockey equipment, gear, and accessories. Find top brands, expert advice, and everything you need for the game."/> aka locator('meta[name="description"]').first()
        2) <meta data-rh="true" name="description" content="Learn about how The Ice Box Hockey collects, uses, and protects your personal information. Read our comprehensive privacy policy."/> aka locator('meta[name="description"]').nth(1)

    Call log:
      - Expect "toHaveAttribute" with timeout 10000ms
      - waiting for locator('meta[name="description"]')


      61 |     // Check meta description
      62 |     const metaDescription = page.locator('meta[name="description"]');
    > 63 |     await expect(metaDescription).toHaveAttribute('content', /privacy policy.*Ice Box Hockey/);
         |                                   ^
      64 |     
      65 |     // Check title
      66 |     await expect(page).toHaveTitle(/Privacy Policy.*Ice Box Hockey/);
        at /Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts:63:35

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-7b6a4-d-have-proper-SEO-meta-tags-Mobile-Chrome/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-7b6a4-d-have-proper-SEO-meta-tags-Mobile-Chrome/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|playwright-artifacts/privacy-policy-Privacy-Pol-7b6a4-d-have-proper-SEO-meta-tags-Mobile-Chrome/test-failed-1.png]]

[[ATTACHMENT|playwright-artifacts/privacy-policy-Privacy-Pol-7b6a4-d-have-proper-SEO-meta-tags-Mobile-Chrome/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Privacy Policy Page › should be responsive on mobile" classname="privacy-policy.spec.ts" time="2.686">
<failure message="privacy-policy.spec.ts:69:3 should be responsive on mobile" type="FAILURE">
<![CDATA[  [Mobile Chrome] › privacy-policy.spec.ts:69:3 › Privacy Policy Page › should be responsive on mobile 

    Error: expect.toBeVisible: Error: strict mode violation: locator('section').filter({ hasText: 'Contact Us' }) resolved to 2 elements:
        1) <section>…</section> aka getByText('Your Privacy RightsYou have')
        2) <section>…</section> aka getByText('Contact UsIf you have any')

    Call log:
      - Expect "toBeVisible" with timeout 10000ms
      - waiting for locator('section').filter({ hasText: 'Contact Us' })


      78 |     // Check if contact section is readable on mobile
      79 |     const contactSection = page.locator('section').filter({ hasText: 'Contact Us' });
    > 80 |     await expect(contactSection).toBeVisible();
         |                                  ^
      81 |   });
      82 |
      83 |   test('should have working email link', async ({ page }) => {
        at /Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts:80:34

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-f8298-uld-be-responsive-on-mobile-Mobile-Chrome/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-f8298-uld-be-responsive-on-mobile-Mobile-Chrome/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|playwright-artifacts/privacy-policy-Privacy-Pol-f8298-uld-be-responsive-on-mobile-Mobile-Chrome/test-failed-1.png]]

[[ATTACHMENT|playwright-artifacts/privacy-policy-Privacy-Pol-f8298-uld-be-responsive-on-mobile-Mobile-Chrome/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Privacy Policy Page › should have working email link" classname="privacy-policy.spec.ts" time="1.922">
<failure message="privacy-policy.spec.ts:83:3 should have working email link" type="FAILURE">
<![CDATA[  [Mobile Chrome] › privacy-policy.spec.ts:83:3 › Privacy Policy Page › should have working email link 

    Error: expect.toHaveAttribute: Error: strict mode violation: locator('a[href="mailto:<EMAIL>"]') resolved to 2 elements:
        1) <a href="mailto:<EMAIL>" class="text-blue-600 hover:text-blue-800"><EMAIL></a> aka getByRole('main').getByRole('link', { name: '<EMAIL>' })
        2) <a href="mailto:<EMAIL>" class="ml-3 text-white/90 hover:text-blue-400 transition-colors"><EMAIL></a> aka locator('#contact').getByRole('link', { name: '<EMAIL>' })

    Call log:
      - Expect "toHaveAttribute" with timeout 10000ms
      - waiting for locator('a[href="mailto:<EMAIL>"]')


      87 |     const emailLink = page.locator('a[href="mailto:<EMAIL>"]');
      88 |     await expect(emailLink).toBeVisible();
    > 89 |     await expect(emailLink).toHaveAttribute('href', 'mailto:<EMAIL>');
         |                             ^
      90 |   });
      91 |
      92 |   test('should display last updated date', async ({ page }) => {
        at /Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts:89:29

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-c02f5-uld-have-working-email-link-Mobile-Chrome/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-c02f5-uld-have-working-email-link-Mobile-Chrome/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|playwright-artifacts/privacy-policy-Privacy-Pol-c02f5-uld-have-working-email-link-Mobile-Chrome/test-failed-1.png]]

[[ATTACHMENT|playwright-artifacts/privacy-policy-Privacy-Pol-c02f5-uld-have-working-email-link-Mobile-Chrome/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Privacy Policy Page › should display last updated date" classname="privacy-policy.spec.ts" time="3.866">
</testcase>
<testcase name="Privacy Policy Page › should have proper heading hierarchy" classname="privacy-policy.spec.ts" time="11.795">
<failure message="privacy-policy.spec.ts:103:3 should have proper heading hierarchy" type="FAILURE">
<![CDATA[  [Mobile Chrome] › privacy-policy.spec.ts:103:3 › Privacy Policy Page › should have proper heading hierarchy 

    Error: Timed out 10000ms waiting for expect(locator).toHaveCount(expected)

    Locator: locator('h2')
    Expected: 8
    Received: 9
    Call log:
      - Expect "toHaveCount" with timeout 10000ms
      - waiting for locator('h2')
        13 × locator resolved to 9 elements
           - unexpected value "9"


      113 |     
      114 |     // Should have multiple h2 sections
    > 115 |     await expect(h2Elements).toHaveCount(8); // Based on our content structure
          |                              ^
      116 |     
      117 |     // Should have h3 subsections
      118 |     const h3Count = await h3Elements.count();
        at /Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts:115:30

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-10ffc-ve-proper-heading-hierarchy-Mobile-Chrome/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-10ffc-ve-proper-heading-hierarchy-Mobile-Chrome/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|playwright-artifacts/privacy-policy-Privacy-Pol-10ffc-ve-proper-heading-hierarchy-Mobile-Chrome/test-failed-1.png]]

[[ATTACHMENT|playwright-artifacts/privacy-policy-Privacy-Pol-10ffc-ve-proper-heading-hierarchy-Mobile-Chrome/error-context.md]]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="privacy-policy.spec.ts" timestamp="2025-08-02T00:27:59.086Z" hostname="Mobile Safari" tests="8" failures="5" skipped="0" time="24.753" errors="0">
<testcase name="Privacy Policy Page › should load Privacy Policy page directly" classname="privacy-policy.spec.ts" time="2.326">
<failure message="privacy-policy.spec.ts:13:3 should load Privacy Policy page directly" type="FAILURE">
<![CDATA[  [Mobile Safari] › privacy-policy.spec.ts:13:3 › Privacy Policy Page › should load Privacy Policy page directly 

    Error: expect.toContainText: Error: strict mode violation: locator('h2') resolved to 9 elements:
        1) <h2 class="text-2xl font-semibold text-gray-900 mb-4">Information We Collect</h2> aka getByRole('heading', { name: 'Information We Collect' })
        2) <h2 class="text-2xl font-semibold text-gray-900 mb-4">How We Use Your Information</h2> aka getByRole('heading', { name: 'How We Use Your Information' })
        3) <h2 class="text-2xl font-semibold text-gray-900 mb-4">Data Storage and Security</h2> aka getByRole('heading', { name: 'Data Storage and Security' })
        4) <h2 class="text-2xl font-semibold text-gray-900 mb-4">Third-Party Services</h2> aka getByRole('heading', { name: 'Third-Party Services' })
        5) <h2 class="text-2xl font-semibold text-gray-900 mb-4">Cookies and Tracking Technologies</h2> aka getByRole('heading', { name: 'Cookies and Tracking' })
        6) <h2 class="text-2xl font-semibold text-gray-900 mb-4">Your Privacy Rights</h2> aka getByRole('heading', { name: 'Your Privacy Rights' })
        7) <h2 class="text-2xl font-semibold text-gray-900 mb-4">Children's Privacy</h2> aka getByRole('heading', { name: 'Children\'s Privacy' })
        8) <h2 class="text-2xl font-semibold text-gray-900 mb-4">Changes to This Privacy Policy</h2> aka getByRole('heading', { name: 'Changes to This Privacy Policy' })
        9) <h2 class="text-2xl font-semibold text-gray-900 mb-4">Contact Us</h2> aka getByRole('main').getByRole('heading', { name: 'Contact Us' })

    Call log:
      - Expect "toContainText" with timeout 10000ms
      - waiting for locator('h2')


      21 |     
      22 |     // Check for key sections
    > 23 |     await expect(page.locator('h2')).toContainText('Information We Collect');
         |                                      ^
      24 |     await expect(page.locator('h2')).toContainText('How We Use Your Information');
      25 |     await expect(page.locator('h2')).toContainText('Data Storage and Security');
      26 |     await expect(page.locator('h2')).toContainText('Contact Us');
        at /Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts:23:38

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-70c35-rivacy-Policy-page-directly-Mobile-Safari/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-70c35-rivacy-Policy-page-directly-Mobile-Safari/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|playwright-artifacts/privacy-policy-Privacy-Pol-70c35-rivacy-Policy-page-directly-Mobile-Safari/test-failed-1.png]]

[[ATTACHMENT|playwright-artifacts/privacy-policy-Privacy-Pol-70c35-rivacy-Policy-page-directly-Mobile-Safari/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Privacy Policy Page › should navigate to Privacy Policy from footer link" classname="privacy-policy.spec.ts" time="2.296">
</testcase>
<testcase name="Privacy Policy Page › should display contact information" classname="privacy-policy.spec.ts" time="1.926">
<failure message="privacy-policy.spec.ts:44:3 should display contact information" type="FAILURE">
<![CDATA[  [Mobile Safari] › privacy-policy.spec.ts:44:3 › Privacy Policy Page › should display contact information 

    Error: expect.toBeVisible: Error: strict mode violation: locator('section').filter({ hasText: 'Contact Us' }) resolved to 2 elements:
        1) <section>…</section> aka getByText('Your Privacy RightsYou have')
        2) <section>…</section> aka getByText('Contact UsIf you have any')

    Call log:
      - Expect "toBeVisible" with timeout 10000ms
      - waiting for locator('section').filter({ hasText: 'Contact Us' })


      47 |     // Check for contact section
      48 |     const contactSection = page.locator('section').filter({ hasText: 'Contact Us' });
    > 49 |     await expect(contactSection).toBeVisible();
         |                                  ^
      50 |     
      51 |     // Check for business details
      52 |     await expect(contactSection).toContainText('The Ice Box Hockey');
        at /Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts:49:34

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-464f2-display-contact-information-Mobile-Safari/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-464f2-display-contact-information-Mobile-Safari/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|playwright-artifacts/privacy-policy-Privacy-Pol-464f2-display-contact-information-Mobile-Safari/test-failed-1.png]]

[[ATTACHMENT|playwright-artifacts/privacy-policy-Privacy-Pol-464f2-display-contact-information-Mobile-Safari/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Privacy Policy Page › should have proper SEO meta tags" classname="privacy-policy.spec.ts" time="2.035">
<failure message="privacy-policy.spec.ts:58:3 should have proper SEO meta tags" type="FAILURE">
<![CDATA[  [Mobile Safari] › privacy-policy.spec.ts:58:3 › Privacy Policy Page › should have proper SEO meta tags 

    Error: expect.toHaveAttribute: Error: strict mode violation: locator('meta[name="description"]') resolved to 2 elements:
        1) <meta name="description" content="Ice Box Hockey - Your premier destination for hockey equipment, gear, and accessories. Find top brands, expert advice, and everything you need for the game."/> aka locator('meta[name="description"]').first()
        2) <meta data-rh="true" name="description" content="Learn about how The Ice Box Hockey collects, uses, and protects your personal information. Read our comprehensive privacy policy."/> aka locator('meta[name="description"]').nth(1)

    Call log:
      - Expect "toHaveAttribute" with timeout 10000ms
      - waiting for locator('meta[name="description"]')
        2 × locator resolved to <meta name="description" content="Ice Box Hockey - Your premier destination for hockey equipment, gear, and accessories. Find top brands, expert advice, and everything you need for the game."/>
          - unexpected value "Ice Box Hockey - Your premier destination for hockey equipment, gear, and accessories. Find top brands, expert advice, and everything you need for the game."


      61 |     // Check meta description
      62 |     const metaDescription = page.locator('meta[name="description"]');
    > 63 |     await expect(metaDescription).toHaveAttribute('content', /privacy policy.*Ice Box Hockey/);
         |                                   ^
      64 |     
      65 |     // Check title
      66 |     await expect(page).toHaveTitle(/Privacy Policy.*Ice Box Hockey/);
        at /Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts:63:35

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-7b6a4-d-have-proper-SEO-meta-tags-Mobile-Safari/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-7b6a4-d-have-proper-SEO-meta-tags-Mobile-Safari/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|playwright-artifacts/privacy-policy-Privacy-Pol-7b6a4-d-have-proper-SEO-meta-tags-Mobile-Safari/test-failed-1.png]]

[[ATTACHMENT|playwright-artifacts/privacy-policy-Privacy-Pol-7b6a4-d-have-proper-SEO-meta-tags-Mobile-Safari/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Privacy Policy Page › should be responsive on mobile" classname="privacy-policy.spec.ts" time="2.112">
<failure message="privacy-policy.spec.ts:69:3 should be responsive on mobile" type="FAILURE">
<![CDATA[  [Mobile Safari] › privacy-policy.spec.ts:69:3 › Privacy Policy Page › should be responsive on mobile 

    Error: expect.toBeVisible: Error: strict mode violation: locator('section').filter({ hasText: 'Contact Us' }) resolved to 2 elements:
        1) <section>…</section> aka getByText('Your Privacy RightsYou have')
        2) <section>…</section> aka getByText('Contact UsIf you have any')

    Call log:
      - Expect "toBeVisible" with timeout 10000ms
      - waiting for locator('section').filter({ hasText: 'Contact Us' })


      78 |     // Check if contact section is readable on mobile
      79 |     const contactSection = page.locator('section').filter({ hasText: 'Contact Us' });
    > 80 |     await expect(contactSection).toBeVisible();
         |                                  ^
      81 |   });
      82 |
      83 |   test('should have working email link', async ({ page }) => {
        at /Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts:80:34

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-f8298-uld-be-responsive-on-mobile-Mobile-Safari/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-f8298-uld-be-responsive-on-mobile-Mobile-Safari/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|playwright-artifacts/privacy-policy-Privacy-Pol-f8298-uld-be-responsive-on-mobile-Mobile-Safari/test-failed-1.png]]

[[ATTACHMENT|playwright-artifacts/privacy-policy-Privacy-Pol-f8298-uld-be-responsive-on-mobile-Mobile-Safari/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Privacy Policy Page › should have working email link" classname="privacy-policy.spec.ts" time="1.123">
</testcase>
<testcase name="Privacy Policy Page › should display last updated date" classname="privacy-policy.spec.ts" time="1.664">
</testcase>
<testcase name="Privacy Policy Page › should have proper heading hierarchy" classname="privacy-policy.spec.ts" time="11.271">
<failure message="privacy-policy.spec.ts:103:3 should have proper heading hierarchy" type="FAILURE">
<![CDATA[  [Mobile Safari] › privacy-policy.spec.ts:103:3 › Privacy Policy Page › should have proper heading hierarchy 

    Error: Timed out 10000ms waiting for expect(locator).toHaveCount(expected)

    Locator: locator('h2')
    Expected: 8
    Received: 9
    Call log:
      - Expect "toHaveCount" with timeout 10000ms
      - waiting for locator('h2')
        14 × locator resolved to 9 elements
           - unexpected value "9"


      113 |     
      114 |     // Should have multiple h2 sections
    > 115 |     await expect(h2Elements).toHaveCount(8); // Based on our content structure
          |                              ^
      116 |     
      117 |     // Should have h3 subsections
      118 |     const h3Count = await h3Elements.count();
        at /Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts:115:30

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-10ffc-ve-proper-heading-hierarchy-Mobile-Safari/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-10ffc-ve-proper-heading-hierarchy-Mobile-Safari/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|playwright-artifacts/privacy-policy-Privacy-Pol-10ffc-ve-proper-heading-hierarchy-Mobile-Safari/test-failed-1.png]]

[[ATTACHMENT|playwright-artifacts/privacy-policy-Privacy-Pol-10ffc-ve-proper-heading-hierarchy-Mobile-Safari/error-context.md]]
]]>
</system-out>
</testcase>
</testsuite>
</testsuites>