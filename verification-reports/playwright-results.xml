<testsuites id="" name="" tests="18" failures="4" skipped="0" errors="0" time="5.962585999999999">
<testsuite name="accessibility.spec.ts" timestamp="2025-08-01T14:34:18.586Z" hostname="accessibility" tests="18" failures="4" skipped="0" time="16.701" errors="0">
<testcase name="Accessibility Tests › should have proper heading hierarchy" classname="accessibility.spec.ts" time="1.477">
</testcase>
<testcase name="Accessibility Tests › should have alt text for all images" classname="accessibility.spec.ts" time="0.688">
</testcase>
<testcase name="Accessibility Tests › should have proper form labels" classname="accessibility.spec.ts" time="0.7">
</testcase>
<testcase name="Accessibility Tests › should have keyboard navigation support" classname="accessibility.spec.ts" time="0.794">
</testcase>
<testcase name="Accessibility Tests › should have proper ARIA attributes for interactive elements" classname="accessibility.spec.ts" time="0.686">
</testcase>
<testcase name="Accessibility Tests › should have proper color contrast (basic check)" classname="accessibility.spec.ts" time="0.884">
</testcase>
<testcase name="Accessibility Tests › should have skip links or proper navigation structure" classname="accessibility.spec.ts" time="0.529">
</testcase>
<testcase name="Accessibility Tests › should handle focus management for modals/dialogs" classname="accessibility.spec.ts" time="1.146">
</testcase>
<testcase name="Accessibility Tests › should have proper page title" classname="accessibility.spec.ts" time="0.701">
</testcase>
<testcase name="Accessibility Tests › should have lang attribute on html element" classname="accessibility.spec.ts" time="0.417">
</testcase>
<testcase name="Accessibility Tests › should pass axe-core WCAG 2.1 AA compliance check" classname="accessibility.spec.ts" time="1.592">
<failure message="accessibility.spec.ts:176:3 should pass axe-core WCAG 2.1 AA compliance check" type="FAILURE">
<![CDATA[  [accessibility] › accessibility.spec.ts:176:3 › Accessibility Tests › should pass axe-core WCAG 2.1 AA compliance check 

    Error: Found 3 accessibility violations. See console for details.

      209 |
      210 |       // Fail the test with detailed information
    > 211 |       throw new Error(`Found ${results.violations.length} accessibility violations. See console for details.`);
          |             ^
      212 |     }
      213 |
      214 |     // Log success
        at /Users/<USER>/Development/Ice-Box-Hockey/tests/accessibility.spec.ts:211:13

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    verification-reports/playwright-artifacts/accessibility-Accessibilit-3ce36-CAG-2-1-AA-compliance-check-accessibility/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../verification-reports/playwright-artifacts/accessibility-Accessibilit-3ce36-CAG-2-1-AA-compliance-check-accessibility/error-context.md
]]>
</failure>
<system-out>
<![CDATA[Accessibility violations found:
1. button-name: Ensure buttons have discernible text
   Impact: critical
   Help: Buttons must have discernible text
   Elements: .right-0; .hover\:bg-orange-200; .hover\:bg-zinc-950\/5
   More info: https://dequeuniversity.com/rules/axe/4.10/button-name?application=playwright

2. color-contrast: Ensure the contrast between foreground and background colors meets WCAG 2 AA minimum contrast ratio thresholds
   Impact: serious
   Help: Elements must meet minimum color contrast ratio thresholds
   Elements: .rounded-md
   More info: https://dequeuniversity.com/rules/axe/4.10/color-contrast?application=playwright

3. region: Ensure all page content is contained by landmarks
   Impact: moderate
   Help: All page content should be contained by landmarks
   Elements: .text-orange-800; .space-y-3.text-orange-700.text-sm > p; .space-y-3.text-orange-700.text-sm > .space-y-2; .pt-2
   More info: https://dequeuniversity.com/rules/axe/4.10/region?application=playwright


[[ATTACHMENT|playwright-artifacts/accessibility-Accessibilit-3ce36-CAG-2-1-AA-compliance-check-accessibility/test-failed-1.png]]

[[ATTACHMENT|playwright-artifacts/accessibility-Accessibilit-3ce36-CAG-2-1-AA-compliance-check-accessibility/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Accessibility Tests › should pass axe-core check on team sales page" classname="accessibility.spec.ts" time="1.41">
<failure message="accessibility.spec.ts:218:3 should pass axe-core check on team sales page" type="FAILURE">
<![CDATA[  [accessibility] › accessibility.spec.ts:218:3 › Accessibility Tests › should pass axe-core check on team sales page 

    Error: Found 3 accessibility violations on team sales page

      227 |       console.log(`Team Sales page violations: ${results.violations.length}`);
      228 |       results.violations.forEach(v => console.log(`- ${v.id}: ${v.description}`));
    > 229 |       throw new Error(`Found ${results.violations.length} accessibility violations on team sales page`);
          |             ^
      230 |     }
      231 |     
      232 |     console.log(`✓ Team Sales page accessibility check passed`);
        at /Users/<USER>/Development/Ice-Box-Hockey/tests/accessibility.spec.ts:229:13

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    verification-reports/playwright-artifacts/accessibility-Accessibilit-5f03e-re-check-on-team-sales-page-accessibility/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../verification-reports/playwright-artifacts/accessibility-Accessibilit-5f03e-re-check-on-team-sales-page-accessibility/error-context.md
]]>
</failure>
<system-out>
<![CDATA[Team Sales page violations: 3
- aria-command-name: Ensure every ARIA button, link and menuitem has an accessible name
- button-name: Ensure buttons have discernible text
- color-contrast: Ensure the contrast between foreground and background colors meets WCAG 2 AA minimum contrast ratio thresholds

[[ATTACHMENT|playwright-artifacts/accessibility-Accessibilit-5f03e-re-check-on-team-sales-page-accessibility/test-failed-1.png]]

[[ATTACHMENT|playwright-artifacts/accessibility-Accessibilit-5f03e-re-check-on-team-sales-page-accessibility/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Accessibility Tests › should pass axe-core check on harbor city page" classname="accessibility.spec.ts" time="1.586">
<failure message="accessibility.spec.ts:235:3 should pass axe-core check on harbor city page" type="FAILURE">
<![CDATA[  [accessibility] › accessibility.spec.ts:235:3 › Accessibility Tests › should pass axe-core check on harbor city page 

    Error: Found 3 accessibility violations on harbor city page

      244 |       console.log(`Harbor City page violations: ${results.violations.length}`);
      245 |       results.violations.forEach(v => console.log(`- ${v.id}: ${v.description}`));
    > 246 |       throw new Error(`Found ${results.violations.length} accessibility violations on harbor city page`);
          |             ^
      247 |     }
      248 |     
      249 |     console.log(`✓ Harbor City page accessibility check passed`);
        at /Users/<USER>/Development/Ice-Box-Hockey/tests/accessibility.spec.ts:246:13

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    verification-reports/playwright-artifacts/accessibility-Accessibilit-f5fd4-e-check-on-harbor-city-page-accessibility/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../verification-reports/playwright-artifacts/accessibility-Accessibilit-f5fd4-e-check-on-harbor-city-page-accessibility/error-context.md
]]>
</failure>
<system-out>
<![CDATA[Harbor City page violations: 3
- aria-command-name: Ensure every ARIA button, link and menuitem has an accessible name
- button-name: Ensure buttons have discernible text
- color-contrast: Ensure the contrast between foreground and background colors meets WCAG 2 AA minimum contrast ratio thresholds

[[ATTACHMENT|playwright-artifacts/accessibility-Accessibilit-f5fd4-e-check-on-harbor-city-page-accessibility/test-failed-1.png]]

[[ATTACHMENT|playwright-artifacts/accessibility-Accessibilit-f5fd4-e-check-on-harbor-city-page-accessibility/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Accessibility Tests › should have proper keyboard navigation flow" classname="accessibility.spec.ts" time="1.061">
<system-out>
<![CDATA[Keyboard navigation tested through 8 elements: [ [32m'A'[39m, [32m'BUTTON'[39m, [32m'A'[39m, [32m'A'[39m, [32m'A'[39m, [32m'A'[39m, [32m'A'[39m, [32m'A'[39m ]
]]>
</system-out>
</testcase>
<testcase name="Accessibility Tests › should handle Enter and Space key activation" classname="accessibility.spec.ts" time="0.963">
<failure message="accessibility.spec.ts:315:3 should handle Enter and Space key activation" type="FAILURE">
<![CDATA[  [accessibility] › accessibility.spec.ts:315:3 › Accessibility Tests › should handle Enter and Space key activation 

    Error: expect(received).toBeTruthy()

    Received: null

      342 |       });
      343 |       
    > 344 |       expect(accessibleName).toBeTruthy();
          |                              ^
      345 |
      346 |       // Note: We don't actually press Enter/Space to avoid side effects
      347 |       // but we verify the button is properly focusable and labeled
        at /Users/<USER>/Development/Ice-Box-Hockey/tests/accessibility.spec.ts:344:30

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    verification-reports/playwright-artifacts/accessibility-Accessibilit-5d6d8-er-and-Space-key-activation-accessibility/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../verification-reports/playwright-artifacts/accessibility-Accessibilit-5d6d8-er-and-Space-key-activation-accessibility/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|playwright-artifacts/accessibility-Accessibilit-5d6d8-er-and-Space-key-activation-accessibility/test-failed-1.png]]

[[ATTACHMENT|playwright-artifacts/accessibility-Accessibilit-5d6d8-er-and-Space-key-activation-accessibility/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Accessibility Tests › should have proper ARIA landmarks and structure" classname="accessibility.spec.ts" time="0.542">
</testcase>
<testcase name="Accessibility Tests › should have proper form accessibility" classname="accessibility.spec.ts" time="0.506">
<system-err>
<![CDATA[No form inputs found for form accessibility test
]]>
</system-err>
</testcase>
<testcase name="Accessibility Tests › should handle focus management for interactive components" classname="accessibility.spec.ts" time="1.019">
</testcase>
</testsuite>
</testsuites>