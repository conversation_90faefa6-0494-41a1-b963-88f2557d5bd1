{"timestamp": "2025-07-31T17:42:55.942Z", "summary": {"total": 18, "passed": 18, "failed": 0, "warnings": 0}, "results": [{"component": "Directory Structure", "status": "passed", "message": "All required directories exist"}, {"component": "Directory Structure", "status": "passed", "message": "Reports directory already exists"}, {"component": "package.json", "status": "passed", "message": "All verification scripts are present"}, {"component": "package.json", "status": "passed", "message": "CLI binary entry is configured"}, {"component": "package.json", "status": "passed", "message": "All required dependencies are present"}, {"component": "Jest", "status": "passed", "message": "Global setup and teardown files are configured"}, {"component": "Jest", "status": "passed", "message": "Jest configuration is valid"}, {"component": "Playwright", "status": "passed", "message": "Global setup and teardown files are configured"}, {"component": "Playwright", "status": "passed", "message": "Found 6 test files"}, {"component": "CI/CD", "status": "passed", "message": "GitHub workflow file exists"}, {"component": "CI/CD", "status": "passed", "message": "CI verification script is executable"}, {"component": "Verification System", "status": "passed", "message": "CLI file exists"}, {"component": "Verification System", "status": "passed", "message": "Binary executable exists"}, {"component": "Verification System", "status": "passed", "message": "Configuration file is valid JSON"}, {"component": "Verification System", "status": "passed", "message": "CLI is functional"}, {"component": "Build System", "status": "passed", "message": "Vite configuration exists"}, {"component": "Build System", "status": "passed", "message": "TypeScript configuration exists"}, {"component": "Build System", "status": "passed", "message": "Build command executes successfully"}]}