{"config": {"configFile": "/Users/<USER>/Development/Ice-Box-Hockey/playwright.config.ts", "rootDir": "/Users/<USER>/Development/Ice-Box-Hockey/tests", "forbidOnly": false, "fullyParallel": true, "globalSetup": "/Users/<USER>/Development/Ice-Box-Hockey/src/test/playwright-global-setup.ts", "globalTeardown": "/Users/<USER>/Development/Ice-Box-Hockey/src/test/playwright-global-teardown.ts", "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"actualWorkers": 5}, "preserveOutput": "always", "reporter": [["html", {"outputFolder": "playwright-report", "open": "never"}], ["json", {"outputFile": "verification-reports/playwright-results.json"}], ["junit", {"outputFile": "verification-reports/playwright-results.xml"}]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "/Users/<USER>/Development/Ice-Box-Hockey/verification-reports/playwright-artifacts", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 5}, "id": "chromium", "name": "chromium", "testDir": "/Users/<USER>/Development/Ice-Box-Hockey/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Development/Ice-Box-Hockey/verification-reports/playwright-artifacts", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 5}, "id": "firefox", "name": "firefox", "testDir": "/Users/<USER>/Development/Ice-Box-Hockey/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Development/Ice-Box-Hockey/verification-reports/playwright-artifacts", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 5}, "id": "webkit", "name": "webkit", "testDir": "/Users/<USER>/Development/Ice-Box-Hockey/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Development/Ice-Box-Hockey/verification-reports/playwright-artifacts", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 5}, "id": "Mobile Chrome", "name": "Mobile Chrome", "testDir": "/Users/<USER>/Development/Ice-Box-Hockey/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Development/Ice-Box-Hockey/verification-reports/playwright-artifacts", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 5}, "id": "Mobile Safari", "name": "Mobile Safari", "testDir": "/Users/<USER>/Development/Ice-Box-Hockey/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Development/Ice-Box-Hockey/verification-reports/playwright-artifacts", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 5}, "id": "accessibility", "name": "accessibility", "testDir": "/Users/<USER>/Development/Ice-Box-Hockey/tests", "testIgnore": [], "testMatch": ["**/accessibility.spec.ts"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Development/Ice-Box-Hockey/verification-reports/playwright-artifacts", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 5}, "id": "performance", "name": "performance", "testDir": "/Users/<USER>/Development/Ice-Box-Hockey/tests", "testIgnore": [], "testMatch": ["**/performance.spec.ts"], "timeout": 30000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.53.1", "workers": 5, "webServer": {"command": "npm run dev", "url": "http://localhost:8080", "reuseExistingServer": true, "timeout": 120000, "stdout": "pipe", "stderr": "pipe", "env": {"npm_package_bin_verify-deployment": "bin/verify-deployment.mjs", "TRAE_AI_SHELL_ID": "8", "TERM_PROGRAM": "vscode", "NODE": "/opt/homebrew/Cellar/node/24.3.0/bin/node", "INIT_CWD": "/Users/<USER>/Development/Ice-Box-Hockey", "TERM": "xterm-256color", "SHELL": "/bin/zsh", "HOMEBREW_REPOSITORY": "/opt/homebrew", "TMPDIR": "/var/folders/q2/mv0lwzts40x58hk8rlnchh6m0000gp/T/", "npm_config_global_prefix": "/opt/homebrew", "TERM_PROGRAM_VERSION": "1.100.3", "ZDOTDIR": "/Users/<USER>", "ORIGINAL_XDG_CURRENT_DESKTOP": "undefined", "MallocNanoZone": "0", "COLOR": "1", "npm_config_noproxy": "", "npm_config_local_prefix": "/Users/<USER>/Development/Ice-Box-Hockey", "USER": "nicholas", "COMMAND_MODE": "unix2003", "npm_config_globalconfig": "/opt/homebrew/etc/npmrc", "SSH_AUTH_SOCK": "/private/tmp/com.apple.launchd.lbUcIKVZCu/Listeners", "__CF_USER_TEXT_ENCODING": "0x1F6:0x0:0x0", "npm_execpath": "/opt/homebrew/lib/node_modules/npm/bin/npm-cli.js", "PAGER": "", "PATH": "/Users/<USER>/Development/Ice-Box-Hockey/node_modules/.bin:/Users/<USER>/Development/Ice-Box-Hockey/node_modules/.bin:/Users/<USER>/Development/node_modules/.bin:/Users/<USER>/node_modules/.bin:/Users/<USER>/.bin:/node_modules/.bin:/opt/homebrew/lib/node_modules/npm/node_modules/@npmcli/run-script/lib/node-gyp-bin:/Users/<USER>/.trae/sdks/workspaces/61bc7683/versions/node/current:/Users/<USER>/.trae/sdks/versions/node/current:/Users/<USER>/.rd/bin:/Users/<USER>/.codeium/windsurf/bin:/Users/<USER>/.codeium/windsurf/bin:/Applications/Trae.app/Contents/Resources/app/bin:/Users/<USER>/.trae/sdks/workspaces/61bc7683/versions/node/current:/Users/<USER>/.trae/sdks/versions/node/current:/Users/<USER>/.rd/bin:/Users/<USER>/.codeium/windsurf/bin:/Users/<USER>/.codeium/windsurf/bin:/opt/homebrew/bin:/opt/homebrew/sbin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin:/Applications/VMware Fusion.app/Contents/Public:/Users/<USER>/.local/bin:/Users/<USER>/.local/bin:/Users/<USER>/.local/bin", "npm_package_json": "/Users/<USER>/Development/Ice-Box-Hockey/package.json", "_": "/Users/<USER>/Development/Ice-Box-Hockey/node_modules/.bin/playwright", "npm_config_userconfig": "/Users/<USER>/.npmrc", "npm_config_init_module": "/Users/<USER>/.npm-init.js", "USER_ZDOTDIR": "/Users/<USER>", "__CFBundleIdentifier": "com.trae.app", "npm_command": "exec", "PWD": "/Users/<USER>/Development/Ice-Box-Hockey", "TERM_PRODUCT": "<PERSON><PERSON>", "npm_lifecycle_event": "npx", "EDITOR": "vi", "npm_package_name": "vite_react_shadcn_ts", "LANG": "C.UTF-8", "npm_config_npm_version": "11.4.2", "VSCODE_GIT_ASKPASS_EXTRA_ARGS": "", "XPC_FLAGS": "0x0", "npm_config_node_gyp": "/opt/homebrew/lib/node_modules/npm/node_modules/node-gyp/bin/node-gyp.js", "npm_package_version": "0.0.0", "XPC_SERVICE_NAME": "0", "VSCODE_INJECTION": "1", "SHLVL": "2", "HOME": "/Users/<USER>", "VSCODE_GIT_ASKPASS_MAIN": "/Applications/Trae.app/Contents/Resources/app/extensions/git/dist/askpass-main.js", "HOMEBREW_PREFIX": "/opt/homebrew", "npm_config_cache": "/Users/<USER>/.npm", "LOGNAME": "nicholas", "npm_lifecycle_script": "\"playwright\"", "VSCODE_GIT_IPC_HANDLE": "/var/folders/q2/mv0lwzts40x58hk8rlnchh6m0000gp/T/vscode-git-d52c4ceab2.sock", "npm_config_user_agent": "npm/11.4.2 node/v24.3.0 darwin arm64 workspaces/false", "VSCODE_GIT_ASKPASS_NODE": "/Applications/Trae.app/Contents/Frameworks/Trae Helper (Plugin).app/Contents/MacOS/Trae Helper (Plugin)", "GIT_ASKPASS": "/Applications/Trae.app/Contents/Resources/app/extensions/git/dist/askpass.sh", "INFOPATH": "/opt/homebrew/share/info:", "HOMEBREW_CELLAR": "/opt/homebrew/Cellar", "OSLogRateLimit": "64", "GIT_PAGER": "", "npm_node_execpath": "/opt/homebrew/Cellar/node/24.3.0/bin/node", "npm_config_prefix": "/opt/homebrew", "COLORTERM": "truecolor", "PLAYWRIGHT_WEBSERVER_PID": "62385"}}}, "suites": [{"title": "accessibility.spec.ts", "file": "accessibility.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Accessibility Tests", "file": "accessibility.spec.ts", "line": 4, "column": 6, "specs": [{"title": "should have proper heading hierarchy", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "accessibility", "projectName": "accessibility", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 1477, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-01T14:34:19.067Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "ddd864604af8a6a0198f-4b4f2f085f8a8f63c0f5", "file": "accessibility.spec.ts", "line": 9, "column": 3}, {"title": "should have alt text for all images", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "accessibility", "projectName": "accessibility", "results": [{"workerIndex": 1, "parallelIndex": 1, "status": "passed", "duration": 688, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-01T14:34:19.035Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "ddd864604af8a6a0198f-717f6b192aade2831035", "file": "accessibility.spec.ts", "line": 26, "column": 3}, {"title": "should have proper form labels", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "accessibility", "projectName": "accessibility", "results": [{"workerIndex": 2, "parallelIndex": 2, "status": "passed", "duration": 700, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-01T14:34:19.081Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "ddd864604af8a6a0198f-9523b2e432b5bd8d518b", "file": "accessibility.spec.ts", "line": 43, "column": 3}, {"title": "should have keyboard navigation support", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "accessibility", "projectName": "accessibility", "results": [{"workerIndex": 3, "parallelIndex": 3, "status": "passed", "duration": 794, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-01T14:34:19.071Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "ddd864604af8a6a0198f-0299836b9476f269a599", "file": "accessibility.spec.ts", "line": 60, "column": 3}, {"title": "should have proper ARIA attributes for interactive elements", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "accessibility", "projectName": "accessibility", "results": [{"workerIndex": 4, "parallelIndex": 4, "status": "passed", "duration": 686, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-01T14:34:19.082Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "ddd864604af8a6a0198f-2683dce108d575eec259", "file": "accessibility.spec.ts", "line": 81, "column": 3}, {"title": "should have proper color contrast (basic check)", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "accessibility", "projectName": "accessibility", "results": [{"workerIndex": 1, "parallelIndex": 1, "status": "passed", "duration": 884, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-01T14:34:19.837Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "ddd864604af8a6a0198f-5a32d275157024f524d1", "file": "accessibility.spec.ts", "line": 96, "column": 3}, {"title": "should have skip links or proper navigation structure", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "accessibility", "projectName": "accessibility", "results": [{"workerIndex": 4, "parallelIndex": 4, "status": "passed", "duration": 529, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-01T14:34:19.911Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "ddd864604af8a6a0198f-d8c9a64cd0a02c621862", "file": "accessibility.spec.ts", "line": 115, "column": 3}, {"title": "should handle focus management for modals/dialogs", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "accessibility", "projectName": "accessibility", "results": [{"workerIndex": 2, "parallelIndex": 2, "status": "passed", "duration": 1146, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-01T14:34:19.917Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "ddd864604af8a6a0198f-a6cda48e7f1dc5640c3c", "file": "accessibility.spec.ts", "line": 130, "column": 3}, {"title": "should have proper page title", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "accessibility", "projectName": "accessibility", "results": [{"workerIndex": 3, "parallelIndex": 3, "status": "passed", "duration": 701, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-01T14:34:20.009Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "ddd864604af8a6a0198f-1a34702619c69085bbf0", "file": "accessibility.spec.ts", "line": 163, "column": 3}, {"title": "should have lang attribute on html element", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "accessibility", "projectName": "accessibility", "results": [{"workerIndex": 4, "parallelIndex": 4, "status": "passed", "duration": 417, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-01T14:34:20.450Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "ddd864604af8a6a0198f-8d22262d84363ad144d0", "file": "accessibility.spec.ts", "line": 170, "column": 3}, {"title": "should pass axe-core WCAG 2.1 AA compliance check", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "accessibility", "projectName": "accessibility", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "failed", "duration": 1592, "error": {"message": "Error: Found 3 accessibility violations. See console for details.", "stack": "Error: Found 3 accessibility violations. See console for details.\n    at /Users/<USER>/Development/Ice-Box-Hockey/tests/accessibility.spec.ts:211:13", "location": {"file": "/Users/<USER>/Development/Ice-Box-Hockey/tests/accessibility.spec.ts", "column": 13, "line": 211}, "snippet": "\u001b[0m \u001b[90m 209 |\u001b[39m\n \u001b[90m 210 |\u001b[39m       \u001b[90m// Fail the test with detailed information\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 211 |\u001b[39m       \u001b[36mthrow\u001b[39m \u001b[36mnew\u001b[39m \u001b[33mError\u001b[39m(\u001b[32m`Found ${results.violations.length} accessibility violations. See console for details.`\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m             \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 212 |\u001b[39m     }\n \u001b[90m 213 |\u001b[39m\n \u001b[90m 214 |\u001b[39m     \u001b[90m// Log success\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Development/Ice-Box-Hockey/tests/accessibility.spec.ts", "column": 13, "line": 211}, "message": "Error: Found 3 accessibility violations. See console for details.\n\n\u001b[0m \u001b[90m 209 |\u001b[39m\n \u001b[90m 210 |\u001b[39m       \u001b[90m// Fail the test with detailed information\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 211 |\u001b[39m       \u001b[36mthrow\u001b[39m \u001b[36mnew\u001b[39m \u001b[33mError\u001b[39m(\u001b[32m`Found ${results.violations.length} accessibility violations. See console for details.`\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m             \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 212 |\u001b[39m     }\n \u001b[90m 213 |\u001b[39m\n \u001b[90m 214 |\u001b[39m     \u001b[90m// Log success\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Development/Ice-Box-Hockey/tests/accessibility.spec.ts:211:13\u001b[22m"}], "stdout": [{"text": "Accessibility violations found:\n"}, {"text": "1. button-name: Ensure buttons have discernible text\n"}, {"text": "   Impact: critical\n"}, {"text": "   Help: Buttons must have discernible text\n"}, {"text": "   Elements: .right-0; .hover\\:bg-orange-200; .hover\\:bg-zinc-950\\/5\n"}, {"text": "   More info: https://dequeuniversity.com/rules/axe/4.10/button-name?application=playwright\n\n"}, {"text": "2. color-contrast: Ensure the contrast between foreground and background colors meets WCAG 2 AA minimum contrast ratio thresholds\n"}, {"text": "   Impact: serious\n"}, {"text": "   Help: Elements must meet minimum color contrast ratio thresholds\n"}, {"text": "   Elements: .rounded-md\n"}, {"text": "   More info: https://dequeuniversity.com/rules/axe/4.10/color-contrast?application=playwright\n\n"}, {"text": "3. region: Ensure all page content is contained by landmarks\n"}, {"text": "   Impact: moderate\n"}, {"text": "   Help: All page content should be contained by landmarks\n"}, {"text": "   Elements: .text-orange-800; .space-y-3.text-orange-700.text-sm > p; .space-y-3.text-orange-700.text-sm > .space-y-2; .pt-2\n"}, {"text": "   More info: https://dequeuniversity.com/rules/axe/4.10/region?application=playwright\n\n"}], "stderr": [], "retry": 0, "startTime": "2025-08-01T14:34:20.700Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Development/Ice-Box-Hockey/verification-reports/playwright-artifacts/accessibility-Accessibilit-3ce36-CAG-2-1-AA-compliance-check-accessibility/test-failed-1.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Development/Ice-Box-Hockey/verification-reports/playwright-artifacts/accessibility-Accessibilit-3ce36-CAG-2-1-AA-compliance-check-accessibility/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Development/Ice-Box-Hockey/tests/accessibility.spec.ts", "column": 13, "line": 211}}], "status": "unexpected"}], "id": "ddd864604af8a6a0198f-8cbb78d8477b1cab0d94", "file": "accessibility.spec.ts", "line": 176, "column": 3}, {"title": "should pass axe-core check on team sales page", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "accessibility", "projectName": "accessibility", "results": [{"workerIndex": 3, "parallelIndex": 3, "status": "failed", "duration": 1410, "error": {"message": "Error: Found 3 accessibility violations on team sales page", "stack": "Error: Found 3 accessibility violations on team sales page\n    at /Users/<USER>/Development/Ice-Box-Hockey/tests/accessibility.spec.ts:229:13", "location": {"file": "/Users/<USER>/Development/Ice-Box-Hockey/tests/accessibility.spec.ts", "column": 13, "line": 229}, "snippet": "\u001b[0m \u001b[90m 227 |\u001b[39m       console\u001b[33m.\u001b[39mlog(\u001b[32m`Team Sales page violations: ${results.violations.length}`\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 228 |\u001b[39m       results\u001b[33m.\u001b[39mviolations\u001b[33m.\u001b[39mforEach(v \u001b[33m=>\u001b[39m console\u001b[33m.\u001b[39mlog(\u001b[32m`- ${v.id}: ${v.description}`\u001b[39m))\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 229 |\u001b[39m       \u001b[36mthrow\u001b[39m \u001b[36mnew\u001b[39m \u001b[33mError\u001b[39m(\u001b[32m`Found ${results.violations.length} accessibility violations on team sales page`\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m             \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 230 |\u001b[39m     }\n \u001b[90m 231 |\u001b[39m     \n \u001b[90m 232 |\u001b[39m     console\u001b[33m.\u001b[39mlog(\u001b[32m`✓ Team Sales page accessibility check passed`\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Development/Ice-Box-Hockey/tests/accessibility.spec.ts", "column": 13, "line": 229}, "message": "Error: Found 3 accessibility violations on team sales page\n\n\u001b[0m \u001b[90m 227 |\u001b[39m       console\u001b[33m.\u001b[39mlog(\u001b[32m`Team Sales page violations: ${results.violations.length}`\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 228 |\u001b[39m       results\u001b[33m.\u001b[39mviolations\u001b[33m.\u001b[39mforEach(v \u001b[33m=>\u001b[39m console\u001b[33m.\u001b[39mlog(\u001b[32m`- ${v.id}: ${v.description}`\u001b[39m))\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 229 |\u001b[39m       \u001b[36mthrow\u001b[39m \u001b[36mnew\u001b[39m \u001b[33mError\u001b[39m(\u001b[32m`Found ${results.violations.length} accessibility violations on team sales page`\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m             \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 230 |\u001b[39m     }\n \u001b[90m 231 |\u001b[39m     \n \u001b[90m 232 |\u001b[39m     console\u001b[33m.\u001b[39mlog(\u001b[32m`✓ Team Sales page accessibility check passed`\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Development/Ice-Box-Hockey/tests/accessibility.spec.ts:229:13\u001b[22m"}], "stdout": [{"text": "Team Sales page violations: 3\n"}, {"text": "- aria-command-name: Ensure every ARIA button, link and menuitem has an accessible name\n"}, {"text": "- button-name: Ensure buttons have discernible text\n"}, {"text": "- color-contrast: Ensure the contrast between foreground and background colors meets WCAG 2 AA minimum contrast ratio thresholds\n"}], "stderr": [], "retry": 0, "startTime": "2025-08-01T14:34:20.716Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Development/Ice-Box-Hockey/verification-reports/playwright-artifacts/accessibility-Accessibilit-5f03e-re-check-on-team-sales-page-accessibility/test-failed-1.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Development/Ice-Box-Hockey/verification-reports/playwright-artifacts/accessibility-Accessibilit-5f03e-re-check-on-team-sales-page-accessibility/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Development/Ice-Box-Hockey/tests/accessibility.spec.ts", "column": 13, "line": 229}}], "status": "unexpected"}], "id": "ddd864604af8a6a0198f-2906ecb048ea709776dc", "file": "accessibility.spec.ts", "line": 218, "column": 3}, {"title": "should pass axe-core check on harbor city page", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "accessibility", "projectName": "accessibility", "results": [{"workerIndex": 1, "parallelIndex": 1, "status": "failed", "duration": 1586, "error": {"message": "Error: Found 3 accessibility violations on harbor city page", "stack": "Error: Found 3 accessibility violations on harbor city page\n    at /Users/<USER>/Development/Ice-Box-Hockey/tests/accessibility.spec.ts:246:13", "location": {"file": "/Users/<USER>/Development/Ice-Box-Hockey/tests/accessibility.spec.ts", "column": 13, "line": 246}, "snippet": "\u001b[0m \u001b[90m 244 |\u001b[39m       console\u001b[33m.\u001b[39mlog(\u001b[32m`Harbor City page violations: ${results.violations.length}`\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 245 |\u001b[39m       results\u001b[33m.\u001b[39mviolations\u001b[33m.\u001b[39mforEach(v \u001b[33m=>\u001b[39m console\u001b[33m.\u001b[39mlog(\u001b[32m`- ${v.id}: ${v.description}`\u001b[39m))\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 246 |\u001b[39m       \u001b[36mthrow\u001b[39m \u001b[36mnew\u001b[39m \u001b[33mError\u001b[39m(\u001b[32m`Found ${results.violations.length} accessibility violations on harbor city page`\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m             \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 247 |\u001b[39m     }\n \u001b[90m 248 |\u001b[39m     \n \u001b[90m 249 |\u001b[39m     console\u001b[33m.\u001b[39mlog(\u001b[32m`✓ Harbor City page accessibility check passed`\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Development/Ice-Box-Hockey/tests/accessibility.spec.ts", "column": 13, "line": 246}, "message": "Error: Found 3 accessibility violations on harbor city page\n\n\u001b[0m \u001b[90m 244 |\u001b[39m       console\u001b[33m.\u001b[39mlog(\u001b[32m`Harbor City page violations: ${results.violations.length}`\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 245 |\u001b[39m       results\u001b[33m.\u001b[39mviolations\u001b[33m.\u001b[39mforEach(v \u001b[33m=>\u001b[39m console\u001b[33m.\u001b[39mlog(\u001b[32m`- ${v.id}: ${v.description}`\u001b[39m))\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 246 |\u001b[39m       \u001b[36mthrow\u001b[39m \u001b[36mnew\u001b[39m \u001b[33mError\u001b[39m(\u001b[32m`Found ${results.violations.length} accessibility violations on harbor city page`\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m             \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 247 |\u001b[39m     }\n \u001b[90m 248 |\u001b[39m     \n \u001b[90m 249 |\u001b[39m     console\u001b[33m.\u001b[39mlog(\u001b[32m`✓ Harbor City page accessibility check passed`\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Development/Ice-Box-Hockey/tests/accessibility.spec.ts:246:13\u001b[22m"}], "stdout": [{"text": "Harbor City page violations: 3\n"}, {"text": "- aria-command-name: Ensure every ARIA button, link and menuitem has an accessible name\n"}, {"text": "- button-name: Ensure buttons have discernible text\n"}, {"text": "- color-contrast: Ensure the contrast between foreground and background colors meets WCAG 2 AA minimum contrast ratio thresholds\n"}], "stderr": [], "retry": 0, "startTime": "2025-08-01T14:34:20.731Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Development/Ice-Box-Hockey/verification-reports/playwright-artifacts/accessibility-Accessibilit-f5fd4-e-check-on-harbor-city-page-accessibility/test-failed-1.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Development/Ice-Box-Hockey/verification-reports/playwright-artifacts/accessibility-Accessibilit-f5fd4-e-check-on-harbor-city-page-accessibility/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Development/Ice-Box-Hockey/tests/accessibility.spec.ts", "column": 13, "line": 246}}], "status": "unexpected"}], "id": "ddd864604af8a6a0198f-9eef0255ce3683e36399", "file": "accessibility.spec.ts", "line": 235, "column": 3}, {"title": "should have proper keyboard navigation flow", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "accessibility", "projectName": "accessibility", "results": [{"workerIndex": 4, "parallelIndex": 4, "status": "passed", "duration": 1061, "errors": [], "stdout": [{"text": "Keyboard navigation tested through 8 elements: [ \u001b[32m'A'\u001b[39m, \u001b[32m'BUTTON'\u001b[39m, \u001b[32m'A'\u001b[39m, \u001b[32m'A'\u001b[39m, \u001b[32m'A'\u001b[39m, \u001b[32m'A'\u001b[39m, \u001b[32m'A'\u001b[39m, \u001b[32m'A'\u001b[39m ]\n"}], "stderr": [], "retry": 0, "startTime": "2025-08-01T14:34:20.878Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "ddd864604af8a6a0198f-d4305d4f65a275f245ee", "file": "accessibility.spec.ts", "line": 252, "column": 3}, {"title": "should handle Enter and Space key activation", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "accessibility", "projectName": "accessibility", "results": [{"workerIndex": 2, "parallelIndex": 2, "status": "failed", "duration": 963, "error": {"message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBeTruthy\u001b[2m()\u001b[22m\n\nReceived: \u001b[31mnull\u001b[39m", "stack": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBeTruthy\u001b[2m()\u001b[22m\n\nReceived: \u001b[31mnull\u001b[39m\n    at /Users/<USER>/Development/Ice-Box-Hockey/tests/accessibility.spec.ts:344:30", "location": {"file": "/Users/<USER>/Development/Ice-Box-Hockey/tests/accessibility.spec.ts", "column": 30, "line": 344}, "snippet": "\u001b[0m \u001b[90m 342 |\u001b[39m       })\u001b[33m;\u001b[39m\n \u001b[90m 343 |\u001b[39m       \n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 344 |\u001b[39m       expect(accessibleName)\u001b[33m.\u001b[39mtoBeTruthy()\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 345 |\u001b[39m\n \u001b[90m 346 |\u001b[39m       \u001b[90m// Note: We don't actually press Enter/Space to avoid side effects\u001b[39m\n \u001b[90m 347 |\u001b[39m       \u001b[90m// but we verify the button is properly focusable and labeled\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Development/Ice-Box-Hockey/tests/accessibility.spec.ts", "column": 30, "line": 344}, "message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBeTruthy\u001b[2m()\u001b[22m\n\nReceived: \u001b[31mnull\u001b[39m\n\n\u001b[0m \u001b[90m 342 |\u001b[39m       })\u001b[33m;\u001b[39m\n \u001b[90m 343 |\u001b[39m       \n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 344 |\u001b[39m       expect(accessibleName)\u001b[33m.\u001b[39mtoBeTruthy()\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 345 |\u001b[39m\n \u001b[90m 346 |\u001b[39m       \u001b[90m// Note: We don't actually press Enter/Space to avoid side effects\u001b[39m\n \u001b[90m 347 |\u001b[39m       \u001b[90m// but we verify the button is properly focusable and labeled\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Development/Ice-Box-Hockey/tests/accessibility.spec.ts:344:30\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-01T14:34:21.068Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Development/Ice-Box-Hockey/verification-reports/playwright-artifacts/accessibility-Accessibilit-5d6d8-er-and-Space-key-activation-accessibility/test-failed-1.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Development/Ice-Box-Hockey/verification-reports/playwright-artifacts/accessibility-Accessibilit-5d6d8-er-and-Space-key-activation-accessibility/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Development/Ice-Box-Hockey/tests/accessibility.spec.ts", "column": 30, "line": 344}}], "status": "unexpected"}], "id": "ddd864604af8a6a0198f-1bb2942a3f68f0d161a1", "file": "accessibility.spec.ts", "line": 315, "column": 3}, {"title": "should have proper ARIA landmarks and structure", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "accessibility", "projectName": "accessibility", "results": [{"workerIndex": 4, "parallelIndex": 4, "status": "passed", "duration": 542, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-01T14:34:21.946Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "ddd864604af8a6a0198f-68170ef82bba2340a35d", "file": "accessibility.spec.ts", "line": 351, "column": 3}, {"title": "should have proper form accessibility", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "accessibility", "projectName": "accessibility", "results": [{"workerIndex": 5, "parallelIndex": 2, "status": "passed", "duration": 506, "errors": [], "stdout": [], "stderr": [{"text": "No form inputs found for form accessibility test\n"}], "retry": 0, "startTime": "2025-08-01T14:34:22.668Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "ddd864604af8a6a0198f-f9904458edb041a92b6c", "file": "accessibility.spec.ts", "line": 399, "column": 3}, {"title": "should handle focus management for interactive components", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "accessibility", "projectName": "accessibility", "results": [{"workerIndex": 6, "parallelIndex": 3, "status": "passed", "duration": 1019, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-01T14:34:22.691Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "ddd864604af8a6a0198f-8b894b36010b485e18aa", "file": "accessibility.spec.ts", "line": 445, "column": 3}]}]}], "errors": [], "stats": {"startTime": "2025-08-01T14:34:17.866Z", "duration": 5962.585999999999, "expected": 14, "skipped": 0, "unexpected": 4, "flaky": 0}}