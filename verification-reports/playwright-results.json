{"config": {"configFile": "/Users/<USER>/Development/Ice-Box-Hockey/playwright.config.ts", "rootDir": "/Users/<USER>/Development/Ice-Box-Hockey/tests", "forbidOnly": false, "fullyParallel": true, "globalSetup": "/Users/<USER>/Development/Ice-Box-Hockey/src/test/playwright-global-setup.ts", "globalTeardown": "/Users/<USER>/Development/Ice-Box-Hockey/src/test/playwright-global-teardown.ts", "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"actualWorkers": 5}, "preserveOutput": "always", "reporter": [["html", {"outputFolder": "playwright-report", "open": "never"}], ["json", {"outputFile": "verification-reports/playwright-results.json"}], ["junit", {"outputFile": "verification-reports/playwright-results.xml"}]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "/Users/<USER>/Development/Ice-Box-Hockey/verification-reports/playwright-artifacts", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 5}, "id": "chromium", "name": "chromium", "testDir": "/Users/<USER>/Development/Ice-Box-Hockey/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Development/Ice-Box-Hockey/verification-reports/playwright-artifacts", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 5}, "id": "firefox", "name": "firefox", "testDir": "/Users/<USER>/Development/Ice-Box-Hockey/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Development/Ice-Box-Hockey/verification-reports/playwright-artifacts", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 5}, "id": "webkit", "name": "webkit", "testDir": "/Users/<USER>/Development/Ice-Box-Hockey/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Development/Ice-Box-Hockey/verification-reports/playwright-artifacts", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 5}, "id": "Mobile Chrome", "name": "Mobile Chrome", "testDir": "/Users/<USER>/Development/Ice-Box-Hockey/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Development/Ice-Box-Hockey/verification-reports/playwright-artifacts", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 5}, "id": "Mobile Safari", "name": "Mobile Safari", "testDir": "/Users/<USER>/Development/Ice-Box-Hockey/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Development/Ice-Box-Hockey/verification-reports/playwright-artifacts", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 5}, "id": "accessibility", "name": "accessibility", "testDir": "/Users/<USER>/Development/Ice-Box-Hockey/tests", "testIgnore": [], "testMatch": ["**/accessibility.spec.ts"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Development/Ice-Box-Hockey/verification-reports/playwright-artifacts", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 5}, "id": "performance", "name": "performance", "testDir": "/Users/<USER>/Development/Ice-Box-Hockey/tests", "testIgnore": [], "testMatch": ["**/performance.spec.ts"], "timeout": 30000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.53.1", "workers": 5, "webServer": {"command": "npm run dev", "url": "http://localhost:8080", "reuseExistingServer": true, "timeout": 120000, "stdout": "pipe", "stderr": "pipe", "env": {"npm_package_bin_verify-deployment": "bin/verify-deployment.mjs", "TRAE_AI_SHELL_ID": "10", "TERM_PROGRAM": "vscode", "NODE": "/opt/homebrew/Cellar/node/24.3.0/bin/node", "INIT_CWD": "/Users/<USER>/Development/Ice-Box-Hockey", "TERM": "xterm-256color", "SHELL": "/bin/zsh", "HOMEBREW_REPOSITORY": "/opt/homebrew", "TMPDIR": "/var/folders/q2/mv0lwzts40x58hk8rlnchh6m0000gp/T/", "npm_config_global_prefix": "/opt/homebrew", "TERM_PROGRAM_VERSION": "1.100.3", "ZDOTDIR": "/Users/<USER>", "ORIGINAL_XDG_CURRENT_DESKTOP": "undefined", "MallocNanoZone": "0", "COLOR": "1", "npm_config_noproxy": "", "npm_config_local_prefix": "/Users/<USER>/Development/Ice-Box-Hockey", "USER": "nicholas", "COMMAND_MODE": "unix2003", "npm_config_globalconfig": "/opt/homebrew/etc/npmrc", "SSH_AUTH_SOCK": "/private/tmp/com.apple.launchd.lbUcIKVZCu/Listeners", "__CF_USER_TEXT_ENCODING": "0x1F6:0x0:0x0", "npm_execpath": "/opt/homebrew/lib/node_modules/npm/bin/npm-cli.js", "PAGER": "", "PATH": "/Users/<USER>/Development/Ice-Box-Hockey/node_modules/.bin:/Users/<USER>/Development/Ice-Box-Hockey/node_modules/.bin:/Users/<USER>/Development/node_modules/.bin:/Users/<USER>/node_modules/.bin:/Users/<USER>/.bin:/node_modules/.bin:/opt/homebrew/lib/node_modules/npm/node_modules/@npmcli/run-script/lib/node-gyp-bin:/Users/<USER>/.trae/sdks/workspaces/61bc7683/versions/node/current:/Users/<USER>/.trae/sdks/versions/node/current:/Users/<USER>/.rd/bin:/Users/<USER>/.codeium/windsurf/bin:/Users/<USER>/.codeium/windsurf/bin:/Applications/Trae.app/Contents/Resources/app/bin:/Users/<USER>/.trae/sdks/workspaces/61bc7683/versions/node/current:/Users/<USER>/.trae/sdks/versions/node/current:/Users/<USER>/.rd/bin:/Users/<USER>/.codeium/windsurf/bin:/Users/<USER>/.codeium/windsurf/bin:/opt/homebrew/bin:/opt/homebrew/sbin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin:/Applications/VMware Fusion.app/Contents/Public:/Users/<USER>/.local/bin:/Users/<USER>/.local/bin:/Users/<USER>/.local/bin", "npm_package_json": "/Users/<USER>/Development/Ice-Box-Hockey/package.json", "_": "/Users/<USER>/Development/Ice-Box-Hockey/node_modules/.bin/playwright", "npm_config_userconfig": "/Users/<USER>/.npmrc", "npm_config_init_module": "/Users/<USER>/.npm-init.js", "USER_ZDOTDIR": "/Users/<USER>", "__CFBundleIdentifier": "com.trae.app", "npm_command": "exec", "PWD": "/Users/<USER>/Development/Ice-Box-Hockey", "TERM_PRODUCT": "<PERSON><PERSON>", "npm_lifecycle_event": "npx", "EDITOR": "vi", "npm_package_name": "vite_react_shadcn_ts", "LANG": "C.UTF-8", "npm_config_npm_version": "11.4.2", "VSCODE_GIT_ASKPASS_EXTRA_ARGS": "", "XPC_FLAGS": "0x0", "npm_config_node_gyp": "/opt/homebrew/lib/node_modules/npm/node_modules/node-gyp/bin/node-gyp.js", "npm_package_version": "0.0.0", "XPC_SERVICE_NAME": "0", "VSCODE_INJECTION": "1", "SHLVL": "2", "HOME": "/Users/<USER>", "VSCODE_GIT_ASKPASS_MAIN": "/Applications/Trae.app/Contents/Resources/app/extensions/git/dist/askpass-main.js", "HOMEBREW_PREFIX": "/opt/homebrew", "npm_config_cache": "/Users/<USER>/.npm", "LOGNAME": "nicholas", "npm_lifecycle_script": "\"playwright\"", "VSCODE_GIT_IPC_HANDLE": "/var/folders/q2/mv0lwzts40x58hk8rlnchh6m0000gp/T/vscode-git-d52c4ceab2.sock", "npm_config_user_agent": "npm/11.4.2 node/v24.3.0 darwin arm64 workspaces/false", "VSCODE_GIT_ASKPASS_NODE": "/Applications/Trae.app/Contents/Frameworks/Trae Helper (Plugin).app/Contents/MacOS/Trae Helper (Plugin)", "GIT_ASKPASS": "/Applications/Trae.app/Contents/Resources/app/extensions/git/dist/askpass.sh", "INFOPATH": "/opt/homebrew/share/info:", "HOMEBREW_CELLAR": "/opt/homebrew/Cellar", "OSLogRateLimit": "64", "GIT_PAGER": "", "npm_node_execpath": "/opt/homebrew/Cellar/node/24.3.0/bin/node", "npm_config_prefix": "/opt/homebrew", "COLORTERM": "truecolor", "PLAYWRIGHT_WEBSERVER_PID": "66257"}}}, "suites": [{"title": "privacy-policy.spec.ts", "file": "privacy-policy.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Privacy Policy Page", "file": "privacy-policy.spec.ts", "line": 12, "column": 6, "specs": [{"title": "should load Privacy Policy page directly", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "failed", "duration": 7543, "error": {"message": "Error: expect.toContainText: Error: strict mode violation: locator('h2') resolved to 9 elements:\n    1) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Information We Collect</h2> aka getByRole('heading', { name: 'Information We Collect' })\n    2) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">How We Use Your Information</h2> aka getByRole('heading', { name: 'How We Use Your Information' })\n    3) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Data Storage and Security</h2> aka getByRole('heading', { name: 'Data Storage and Security' })\n    4) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Third-Party Services</h2> aka getByRole('heading', { name: 'Third-Party Services' })\n    5) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Cookies and Tracking Technologies</h2> aka getByRole('heading', { name: 'Cookies and Tracking' })\n    6) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Your Privacy Rights</h2> aka getByRole('heading', { name: 'Your Privacy Rights' })\n    7) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Children's Privacy</h2> aka getByRole('heading', { name: 'Children\\'s Privacy' })\n    8) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Changes to This Privacy Policy</h2> aka getByRole('heading', { name: 'Changes to This Privacy Policy' })\n    9) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Contact Us</h2> aka getByRole('main').getByRole('heading', { name: 'Contact Us' })\n\nCall log:\n\u001b[2m  - Expect \"toContainText\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('h2')\u001b[22m\n", "stack": "Error: expect.toContainText: Error: strict mode violation: locator('h2') resolved to 9 elements:\n    1) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Information We Collect</h2> aka getByRole('heading', { name: 'Information We Collect' })\n    2) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">How We Use Your Information</h2> aka getByRole('heading', { name: 'How We Use Your Information' })\n    3) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Data Storage and Security</h2> aka getByRole('heading', { name: 'Data Storage and Security' })\n    4) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Third-Party Services</h2> aka getByRole('heading', { name: 'Third-Party Services' })\n    5) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Cookies and Tracking Technologies</h2> aka getByRole('heading', { name: 'Cookies and Tracking' })\n    6) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Your Privacy Rights</h2> aka getByRole('heading', { name: 'Your Privacy Rights' })\n    7) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Children's Privacy</h2> aka getByRole('heading', { name: 'Children\\'s Privacy' })\n    8) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Changes to This Privacy Policy</h2> aka getByRole('heading', { name: 'Changes to This Privacy Policy' })\n    9) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Contact Us</h2> aka getByRole('main').getByRole('heading', { name: 'Contact Us' })\n\nCall log:\n\u001b[2m  - Expect \"toContainText\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('h2')\u001b[22m\n\n    at /Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts:23:38", "location": {"file": "/Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts", "column": 38, "line": 23}, "snippet": "\u001b[0m \u001b[90m 21 |\u001b[39m     \n \u001b[90m 22 |\u001b[39m     \u001b[90m// Check for key sections\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 23 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'h2'\u001b[39m))\u001b[33m.\u001b[39mtoContainText(\u001b[32m'Information We Collect'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                      \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 24 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'h2'\u001b[39m))\u001b[33m.\u001b[39mtoContainText(\u001b[32m'How We Use Your Information'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 25 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'h2'\u001b[39m))\u001b[33m.\u001b[39mtoContainText(\u001b[32m'Data Storage and Security'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 26 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'h2'\u001b[39m))\u001b[33m.\u001b[39mtoContainText(\u001b[32m'Contact Us'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts", "column": 38, "line": 23}, "message": "Error: expect.toContainText: Error: strict mode violation: locator('h2') resolved to 9 elements:\n    1) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Information We Collect</h2> aka getByRole('heading', { name: 'Information We Collect' })\n    2) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">How We Use Your Information</h2> aka getByRole('heading', { name: 'How We Use Your Information' })\n    3) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Data Storage and Security</h2> aka getByRole('heading', { name: 'Data Storage and Security' })\n    4) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Third-Party Services</h2> aka getByRole('heading', { name: 'Third-Party Services' })\n    5) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Cookies and Tracking Technologies</h2> aka getByRole('heading', { name: 'Cookies and Tracking' })\n    6) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Your Privacy Rights</h2> aka getByRole('heading', { name: 'Your Privacy Rights' })\n    7) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Children's Privacy</h2> aka getByRole('heading', { name: 'Children\\'s Privacy' })\n    8) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Changes to This Privacy Policy</h2> aka getByRole('heading', { name: 'Changes to This Privacy Policy' })\n    9) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Contact Us</h2> aka getByRole('main').getByRole('heading', { name: 'Contact Us' })\n\nCall log:\n\u001b[2m  - Expect \"toContainText\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('h2')\u001b[22m\n\n\n\u001b[0m \u001b[90m 21 |\u001b[39m     \n \u001b[90m 22 |\u001b[39m     \u001b[90m// Check for key sections\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 23 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'h2'\u001b[39m))\u001b[33m.\u001b[39mtoContainText(\u001b[32m'Information We Collect'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                      \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 24 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'h2'\u001b[39m))\u001b[33m.\u001b[39mtoContainText(\u001b[32m'How We Use Your Information'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 25 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'h2'\u001b[39m))\u001b[33m.\u001b[39mtoContainText(\u001b[32m'Data Storage and Security'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 26 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'h2'\u001b[39m))\u001b[33m.\u001b[39mtoContainText(\u001b[32m'Contact Us'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts:23:38\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-02T00:27:59.705Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Development/Ice-Box-Hockey/verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-70c35-rivacy-Policy-page-directly-chromium/test-failed-1.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Development/Ice-Box-Hockey/verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-70c35-rivacy-Policy-page-directly-chromium/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts", "column": 38, "line": 23}}], "status": "unexpected"}], "id": "dc566187105446b158ed-6e09e02f446aa7f11a2b", "file": "privacy-policy.spec.ts", "line": 13, "column": 3}, {"title": "should navigate to Privacy Policy from footer link", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 1, "parallelIndex": 1, "status": "passed", "duration": 8089, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-02T00:27:59.711Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "dc566187105446b158ed-bf3755ace661af71387c", "file": "privacy-policy.spec.ts", "line": 29, "column": 3}, {"title": "should display contact information", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 2, "parallelIndex": 2, "status": "failed", "duration": 7585, "error": {"message": "Error: expect.toBeVisible: Error: strict mode violation: locator('section').filter({ hasText: 'Contact Us' }) resolved to 2 elements:\n    1) <section>…</section> aka getByText('Your Privacy RightsYou have')\n    2) <section>…</section> aka getByText('Contact UsIf you have any')\n\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('section').filter({ hasText: 'Contact Us' })\u001b[22m\n", "stack": "Error: expect.toBeVisible: Error: strict mode violation: locator('section').filter({ hasText: 'Contact Us' }) resolved to 2 elements:\n    1) <section>…</section> aka getByText('Your Privacy RightsYou have')\n    2) <section>…</section> aka getByText('Contact UsIf you have any')\n\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('section').filter({ hasText: 'Contact Us' })\u001b[22m\n\n    at /Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts:49:34", "location": {"file": "/Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts", "column": 34, "line": 49}, "snippet": "\u001b[0m \u001b[90m 47 |\u001b[39m     \u001b[90m// Check for contact section\u001b[39m\n \u001b[90m 48 |\u001b[39m     \u001b[36mconst\u001b[39m contactSection \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'section'\u001b[39m)\u001b[33m.\u001b[39mfilter({ hasText\u001b[33m:\u001b[39m \u001b[32m'Contact Us'\u001b[39m })\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 49 |\u001b[39m     \u001b[36mawait\u001b[39m expect(contactSection)\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 50 |\u001b[39m     \n \u001b[90m 51 |\u001b[39m     \u001b[90m// Check for business details\u001b[39m\n \u001b[90m 52 |\u001b[39m     \u001b[36mawait\u001b[39m expect(contactSection)\u001b[33m.\u001b[39mtoContainText(\u001b[32m'The Ice Box Hockey'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts", "column": 34, "line": 49}, "message": "Error: expect.toBeVisible: Error: strict mode violation: locator('section').filter({ hasText: 'Contact Us' }) resolved to 2 elements:\n    1) <section>…</section> aka getByText('Your Privacy RightsYou have')\n    2) <section>…</section> aka getByText('Contact UsIf you have any')\n\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('section').filter({ hasText: 'Contact Us' })\u001b[22m\n\n\n\u001b[0m \u001b[90m 47 |\u001b[39m     \u001b[90m// Check for contact section\u001b[39m\n \u001b[90m 48 |\u001b[39m     \u001b[36mconst\u001b[39m contactSection \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'section'\u001b[39m)\u001b[33m.\u001b[39mfilter({ hasText\u001b[33m:\u001b[39m \u001b[32m'Contact Us'\u001b[39m })\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 49 |\u001b[39m     \u001b[36mawait\u001b[39m expect(contactSection)\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 50 |\u001b[39m     \n \u001b[90m 51 |\u001b[39m     \u001b[90m// Check for business details\u001b[39m\n \u001b[90m 52 |\u001b[39m     \u001b[36mawait\u001b[39m expect(contactSection)\u001b[33m.\u001b[39mtoContainText(\u001b[32m'The Ice Box Hockey'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts:49:34\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-02T00:27:59.706Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Development/Ice-Box-Hockey/verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-464f2-display-contact-information-chromium/test-failed-1.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Development/Ice-Box-Hockey/verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-464f2-display-contact-information-chromium/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts", "column": 34, "line": 49}}], "status": "unexpected"}], "id": "dc566187105446b158ed-a9c2648ee95e2e7066c0", "file": "privacy-policy.spec.ts", "line": 44, "column": 3}, {"title": "should have proper SEO meta tags", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 3, "parallelIndex": 3, "status": "failed", "duration": 7887, "error": {"message": "Error: expect.toHaveAttribute: Error: strict mode violation: locator('meta[name=\"description\"]') resolved to 2 elements:\n    1) <meta name=\"description\" content=\"Ice Box Hockey - Your premier destination for hockey equipment, gear, and accessories. Find top brands, expert advice, and everything you need for the game.\"/> aka locator('meta[name=\"description\"]').first()\n    2) <meta data-rh=\"true\" name=\"description\" content=\"Learn about how The Ice Box Hockey collects, uses, and protects your personal information. Read our comprehensive privacy policy.\"/> aka locator('meta[name=\"description\"]').nth(1)\n\nCall log:\n\u001b[2m  - Expect \"toHaveAttribute\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('meta[name=\"description\"]')\u001b[22m\n\u001b[2m    3 × locator resolved to <meta name=\"description\" content=\"Ice Box Hockey - Your premier destination for hockey equipment, gear, and accessories. Find top brands, expert advice, and everything you need for the game.\"/>\u001b[22m\n\u001b[2m      - unexpected value \"Ice Box Hockey - Your premier destination for hockey equipment, gear, and accessories. Find top brands, expert advice, and everything you need for the game.\"\u001b[22m\n", "stack": "Error: expect.toHaveAttribute: Error: strict mode violation: locator('meta[name=\"description\"]') resolved to 2 elements:\n    1) <meta name=\"description\" content=\"Ice Box Hockey - Your premier destination for hockey equipment, gear, and accessories. Find top brands, expert advice, and everything you need for the game.\"/> aka locator('meta[name=\"description\"]').first()\n    2) <meta data-rh=\"true\" name=\"description\" content=\"Learn about how The Ice Box Hockey collects, uses, and protects your personal information. Read our comprehensive privacy policy.\"/> aka locator('meta[name=\"description\"]').nth(1)\n\nCall log:\n\u001b[2m  - Expect \"toHaveAttribute\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('meta[name=\"description\"]')\u001b[22m\n\u001b[2m    3 × locator resolved to <meta name=\"description\" content=\"Ice Box Hockey - Your premier destination for hockey equipment, gear, and accessories. Find top brands, expert advice, and everything you need for the game.\"/>\u001b[22m\n\u001b[2m      - unexpected value \"Ice Box Hockey - Your premier destination for hockey equipment, gear, and accessories. Find top brands, expert advice, and everything you need for the game.\"\u001b[22m\n\n    at /Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts:63:35", "location": {"file": "/Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts", "column": 35, "line": 63}, "snippet": "\u001b[0m \u001b[90m 61 |\u001b[39m     \u001b[90m// Check meta description\u001b[39m\n \u001b[90m 62 |\u001b[39m     \u001b[36mconst\u001b[39m metaDescription \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'meta[name=\"description\"]'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 63 |\u001b[39m     \u001b[36mawait\u001b[39m expect(metaDescription)\u001b[33m.\u001b[39mtoHaveAttribute(\u001b[32m'content'\u001b[39m\u001b[33m,\u001b[39m \u001b[35m/privacy policy.*Ice Box Hockey/\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                   \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 64 |\u001b[39m     \n \u001b[90m 65 |\u001b[39m     \u001b[90m// Check title\u001b[39m\n \u001b[90m 66 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page)\u001b[33m.\u001b[39mtoHaveTitle(\u001b[35m/Privacy Policy.*Ice Box Hockey/\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts", "column": 35, "line": 63}, "message": "Error: expect.toHaveAttribute: Error: strict mode violation: locator('meta[name=\"description\"]') resolved to 2 elements:\n    1) <meta name=\"description\" content=\"Ice Box Hockey - Your premier destination for hockey equipment, gear, and accessories. Find top brands, expert advice, and everything you need for the game.\"/> aka locator('meta[name=\"description\"]').first()\n    2) <meta data-rh=\"true\" name=\"description\" content=\"Learn about how The Ice Box Hockey collects, uses, and protects your personal information. Read our comprehensive privacy policy.\"/> aka locator('meta[name=\"description\"]').nth(1)\n\nCall log:\n\u001b[2m  - Expect \"toHaveAttribute\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('meta[name=\"description\"]')\u001b[22m\n\u001b[2m    3 × locator resolved to <meta name=\"description\" content=\"Ice Box Hockey - Your premier destination for hockey equipment, gear, and accessories. Find top brands, expert advice, and everything you need for the game.\"/>\u001b[22m\n\u001b[2m      - unexpected value \"Ice Box Hockey - Your premier destination for hockey equipment, gear, and accessories. Find top brands, expert advice, and everything you need for the game.\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 61 |\u001b[39m     \u001b[90m// Check meta description\u001b[39m\n \u001b[90m 62 |\u001b[39m     \u001b[36mconst\u001b[39m metaDescription \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'meta[name=\"description\"]'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 63 |\u001b[39m     \u001b[36mawait\u001b[39m expect(metaDescription)\u001b[33m.\u001b[39mtoHaveAttribute(\u001b[32m'content'\u001b[39m\u001b[33m,\u001b[39m \u001b[35m/privacy policy.*Ice Box Hockey/\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                   \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 64 |\u001b[39m     \n \u001b[90m 65 |\u001b[39m     \u001b[90m// Check title\u001b[39m\n \u001b[90m 66 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page)\u001b[33m.\u001b[39mtoHaveTitle(\u001b[35m/Privacy Policy.*Ice Box Hockey/\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts:63:35\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-02T00:27:59.712Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Development/Ice-Box-Hockey/verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-7b6a4-d-have-proper-SEO-meta-tags-chromium/test-failed-1.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Development/Ice-Box-Hockey/verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-7b6a4-d-have-proper-SEO-meta-tags-chromium/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts", "column": 35, "line": 63}}], "status": "unexpected"}], "id": "dc566187105446b158ed-d9d69e83cc99b890cc39", "file": "privacy-policy.spec.ts", "line": 58, "column": 3}, {"title": "should be responsive on mobile", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 4, "parallelIndex": 4, "status": "failed", "duration": 7773, "error": {"message": "Error: expect.toBeVisible: Error: strict mode violation: locator('section').filter({ hasText: 'Contact Us' }) resolved to 2 elements:\n    1) <section>…</section> aka getByText('Your Privacy RightsYou have')\n    2) <section>…</section> aka getByText('Contact UsIf you have any')\n\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('section').filter({ hasText: 'Contact Us' })\u001b[22m\n", "stack": "Error: expect.toBeVisible: Error: strict mode violation: locator('section').filter({ hasText: 'Contact Us' }) resolved to 2 elements:\n    1) <section>…</section> aka getByText('Your Privacy RightsYou have')\n    2) <section>…</section> aka getByText('Contact UsIf you have any')\n\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('section').filter({ hasText: 'Contact Us' })\u001b[22m\n\n    at /Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts:80:34", "location": {"file": "/Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts", "column": 34, "line": 80}, "snippet": "\u001b[0m \u001b[90m 78 |\u001b[39m     \u001b[90m// Check if contact section is readable on mobile\u001b[39m\n \u001b[90m 79 |\u001b[39m     \u001b[36mconst\u001b[39m contactSection \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'section'\u001b[39m)\u001b[33m.\u001b[39mfilter({ hasText\u001b[33m:\u001b[39m \u001b[32m'Contact Us'\u001b[39m })\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 80 |\u001b[39m     \u001b[36mawait\u001b[39m expect(contactSection)\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 81 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 82 |\u001b[39m\n \u001b[90m 83 |\u001b[39m   test(\u001b[32m'should have working email link'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts", "column": 34, "line": 80}, "message": "Error: expect.toBeVisible: Error: strict mode violation: locator('section').filter({ hasText: 'Contact Us' }) resolved to 2 elements:\n    1) <section>…</section> aka getByText('Your Privacy RightsYou have')\n    2) <section>…</section> aka getByText('Contact UsIf you have any')\n\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('section').filter({ hasText: 'Contact Us' })\u001b[22m\n\n\n\u001b[0m \u001b[90m 78 |\u001b[39m     \u001b[90m// Check if contact section is readable on mobile\u001b[39m\n \u001b[90m 79 |\u001b[39m     \u001b[36mconst\u001b[39m contactSection \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'section'\u001b[39m)\u001b[33m.\u001b[39mfilter({ hasText\u001b[33m:\u001b[39m \u001b[32m'Contact Us'\u001b[39m })\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 80 |\u001b[39m     \u001b[36mawait\u001b[39m expect(contactSection)\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 81 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 82 |\u001b[39m\n \u001b[90m 83 |\u001b[39m   test(\u001b[32m'should have working email link'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\u001b[0m\n\u001b[2m    at /Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts:80:34\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-02T00:27:59.698Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Development/Ice-Box-Hockey/verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-f8298-uld-be-responsive-on-mobile-chromium/test-failed-1.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Development/Ice-Box-Hockey/verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-f8298-uld-be-responsive-on-mobile-chromium/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts", "column": 34, "line": 80}}], "status": "unexpected"}], "id": "dc566187105446b158ed-cc90fbebcd7e56f146f8", "file": "privacy-policy.spec.ts", "line": 69, "column": 3}, {"title": "should have working email link", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 5, "parallelIndex": 0, "status": "failed", "duration": 3107, "error": {"message": "Error: expect.toBeVisible: Error: strict mode violation: locator('a[href=\"mailto:<EMAIL>\"]') resolved to 2 elements:\n    1) <a href=\"mailto:<EMAIL>\" class=\"text-blue-600 hover:text-blue-800\"><EMAIL></a> aka getByRole('main').getByRole('link', { name: '<EMAIL>' })\n    2) <a href=\"mailto:<EMAIL>\" class=\"ml-3 text-white/90 hover:text-blue-400 transition-colors\"><EMAIL></a> aka locator('#contact').getByRole('link', { name: '<EMAIL>' })\n\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('a[href=\"mailto:<EMAIL>\"]')\u001b[22m\n", "stack": "Error: expect.toBeVisible: Error: strict mode violation: locator('a[href=\"mailto:<EMAIL>\"]') resolved to 2 elements:\n    1) <a href=\"mailto:<EMAIL>\" class=\"text-blue-600 hover:text-blue-800\"><EMAIL></a> aka getByRole('main').getByRole('link', { name: '<EMAIL>' })\n    2) <a href=\"mailto:<EMAIL>\" class=\"ml-3 text-white/90 hover:text-blue-400 transition-colors\"><EMAIL></a> aka locator('#contact').getByRole('link', { name: '<EMAIL>' })\n\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('a[href=\"mailto:<EMAIL>\"]')\u001b[22m\n\n    at /Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts:88:29", "location": {"file": "/Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts", "column": 29, "line": 88}, "snippet": "\u001b[0m \u001b[90m 86 |\u001b[39m     \u001b[90m// Check for email link in contact section\u001b[39m\n \u001b[90m 87 |\u001b[39m     \u001b[36mconst\u001b[39m emailLink \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'a[href=\"mailto:<EMAIL>\"]'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 88 |\u001b[39m     \u001b[36mawait\u001b[39m expect(emailLink)\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                             \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 89 |\u001b[39m     \u001b[36mawait\u001b[39m expect(emailLink)\u001b[33m.\u001b[39mtoHaveAttribute(\u001b[32m'href'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'mailto:<EMAIL>'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 90 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 91 |\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts", "column": 29, "line": 88}, "message": "Error: expect.toBeVisible: Error: strict mode violation: locator('a[href=\"mailto:<EMAIL>\"]') resolved to 2 elements:\n    1) <a href=\"mailto:<EMAIL>\" class=\"text-blue-600 hover:text-blue-800\"><EMAIL></a> aka getByRole('main').getByRole('link', { name: '<EMAIL>' })\n    2) <a href=\"mailto:<EMAIL>\" class=\"ml-3 text-white/90 hover:text-blue-400 transition-colors\"><EMAIL></a> aka locator('#contact').getByRole('link', { name: '<EMAIL>' })\n\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('a[href=\"mailto:<EMAIL>\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 86 |\u001b[39m     \u001b[90m// Check for email link in contact section\u001b[39m\n \u001b[90m 87 |\u001b[39m     \u001b[36mconst\u001b[39m emailLink \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'a[href=\"mailto:<EMAIL>\"]'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 88 |\u001b[39m     \u001b[36mawait\u001b[39m expect(emailLink)\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                             \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 89 |\u001b[39m     \u001b[36mawait\u001b[39m expect(emailLink)\u001b[33m.\u001b[39mtoHaveAttribute(\u001b[32m'href'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'mailto:<EMAIL>'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 90 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 91 |\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts:88:29\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-02T00:28:09.873Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Development/Ice-Box-Hockey/verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-c02f5-uld-have-working-email-link-chromium/test-failed-1.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Development/Ice-Box-Hockey/verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-c02f5-uld-have-working-email-link-chromium/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts", "column": 29, "line": 88}}], "status": "unexpected"}], "id": "dc566187105446b158ed-2f3c12a9f73da04a0588", "file": "privacy-policy.spec.ts", "line": 83, "column": 3}, {"title": "should display last updated date", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 6, "parallelIndex": 2, "status": "passed", "duration": 3260, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-02T00:28:09.966Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "dc566187105446b158ed-32eed715188dd459dac4", "file": "privacy-policy.spec.ts", "line": 92, "column": 3}, {"title": "should have proper heading hierarchy", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 7, "parallelIndex": 3, "status": "failed", "duration": 12445, "error": {"message": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveCount\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('h2')\nExpected: \u001b[32m8\u001b[39m\nReceived: \u001b[31m9\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveCount\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('h2')\u001b[22m\n\u001b[2m    13 × locator resolved to 9 elements\u001b[22m\n\u001b[2m       - unexpected value \"9\"\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveCount\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('h2')\nExpected: \u001b[32m8\u001b[39m\nReceived: \u001b[31m9\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveCount\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('h2')\u001b[22m\n\u001b[2m    13 × locator resolved to 9 elements\u001b[22m\n\u001b[2m       - unexpected value \"9\"\u001b[22m\n\n    at /Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts:115:30", "location": {"file": "/Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts", "column": 30, "line": 115}, "snippet": "\u001b[0m \u001b[90m 113 |\u001b[39m     \n \u001b[90m 114 |\u001b[39m     \u001b[90m// Should have multiple h2 sections\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 115 |\u001b[39m     \u001b[36mawait\u001b[39m expect(h2Elements)\u001b[33m.\u001b[39mtoHaveCount(\u001b[35m8\u001b[39m)\u001b[33m;\u001b[39m \u001b[90m// Based on our content structure\u001b[39m\n \u001b[90m     |\u001b[39m                              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 116 |\u001b[39m     \n \u001b[90m 117 |\u001b[39m     \u001b[90m// Should have h3 subsections\u001b[39m\n \u001b[90m 118 |\u001b[39m     \u001b[36mconst\u001b[39m h3Count \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m h3Elements\u001b[33m.\u001b[39mcount()\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts", "column": 30, "line": 115}, "message": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveCount\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('h2')\nExpected: \u001b[32m8\u001b[39m\nReceived: \u001b[31m9\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveCount\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('h2')\u001b[22m\n\u001b[2m    13 × locator resolved to 9 elements\u001b[22m\n\u001b[2m       - unexpected value \"9\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 113 |\u001b[39m     \n \u001b[90m 114 |\u001b[39m     \u001b[90m// Should have multiple h2 sections\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 115 |\u001b[39m     \u001b[36mawait\u001b[39m expect(h2Elements)\u001b[33m.\u001b[39mtoHaveCount(\u001b[35m8\u001b[39m)\u001b[33m;\u001b[39m \u001b[90m// Based on our content structure\u001b[39m\n \u001b[90m     |\u001b[39m                              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 116 |\u001b[39m     \n \u001b[90m 117 |\u001b[39m     \u001b[90m// Should have h3 subsections\u001b[39m\n \u001b[90m 118 |\u001b[39m     \u001b[36mconst\u001b[39m h3Count \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m h3Elements\u001b[33m.\u001b[39mcount()\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts:115:30\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-02T00:28:10.070Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Development/Ice-Box-Hockey/verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-10ffc-ve-proper-heading-hierarchy-chromium/test-failed-1.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Development/Ice-Box-Hockey/verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-10ffc-ve-proper-heading-hierarchy-chromium/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts", "column": 30, "line": 115}}], "status": "unexpected"}], "id": "dc566187105446b158ed-5e66aa32973340ce22d8", "file": "privacy-policy.spec.ts", "line": 103, "column": 3}, {"title": "should load Privacy Policy page directly", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 8, "parallelIndex": 4, "status": "failed", "duration": 6020, "error": {"message": "Error: expect.toContainText: Error: strict mode violation: locator('h2') resolved to 9 elements:\n    1) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Information We Collect</h2> aka getByRole('heading', { name: 'Information We Collect' })\n    2) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">How We Use Your Information</h2> aka getByRole('heading', { name: 'How We Use Your Information' })\n    3) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Data Storage and Security</h2> aka getByRole('heading', { name: 'Data Storage and Security' })\n    4) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Third-Party Services</h2> aka getByRole('heading', { name: 'Third-Party Services' })\n    5) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Cookies and Tracking Technologies</h2> aka getByRole('heading', { name: 'Cookies and Tracking' })\n    6) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Your Privacy Rights</h2> aka getByRole('heading', { name: 'Your Privacy Rights' })\n    7) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Children's Privacy</h2> aka getByRole('heading', { name: 'Children\\'s Privacy' })\n    8) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Changes to This Privacy Policy</h2> aka getByRole('heading', { name: 'Changes to This Privacy Policy' })\n    9) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Contact Us</h2> aka getByRole('main').getByRole('heading', { name: 'Contact Us' })\n\nCall log:\n\u001b[2m  - Expect \"toContainText\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('h2')\u001b[22m\n", "stack": "Error: expect.toContainText: Error: strict mode violation: locator('h2') resolved to 9 elements:\n    1) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Information We Collect</h2> aka getByRole('heading', { name: 'Information We Collect' })\n    2) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">How We Use Your Information</h2> aka getByRole('heading', { name: 'How We Use Your Information' })\n    3) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Data Storage and Security</h2> aka getByRole('heading', { name: 'Data Storage and Security' })\n    4) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Third-Party Services</h2> aka getByRole('heading', { name: 'Third-Party Services' })\n    5) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Cookies and Tracking Technologies</h2> aka getByRole('heading', { name: 'Cookies and Tracking' })\n    6) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Your Privacy Rights</h2> aka getByRole('heading', { name: 'Your Privacy Rights' })\n    7) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Children's Privacy</h2> aka getByRole('heading', { name: 'Children\\'s Privacy' })\n    8) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Changes to This Privacy Policy</h2> aka getByRole('heading', { name: 'Changes to This Privacy Policy' })\n    9) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Contact Us</h2> aka getByRole('main').getByRole('heading', { name: 'Contact Us' })\n\nCall log:\n\u001b[2m  - Expect \"toContainText\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('h2')\u001b[22m\n\n    at /Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts:23:38", "location": {"file": "/Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts", "column": 38, "line": 23}, "snippet": "\u001b[0m \u001b[90m 21 |\u001b[39m     \n \u001b[90m 22 |\u001b[39m     \u001b[90m// Check for key sections\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 23 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'h2'\u001b[39m))\u001b[33m.\u001b[39mtoContainText(\u001b[32m'Information We Collect'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                      \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 24 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'h2'\u001b[39m))\u001b[33m.\u001b[39mtoContainText(\u001b[32m'How We Use Your Information'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 25 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'h2'\u001b[39m))\u001b[33m.\u001b[39mtoContainText(\u001b[32m'Data Storage and Security'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 26 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'h2'\u001b[39m))\u001b[33m.\u001b[39mtoContainText(\u001b[32m'Contact Us'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts", "column": 38, "line": 23}, "message": "Error: expect.toContainText: Error: strict mode violation: locator('h2') resolved to 9 elements:\n    1) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Information We Collect</h2> aka getByRole('heading', { name: 'Information We Collect' })\n    2) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">How We Use Your Information</h2> aka getByRole('heading', { name: 'How We Use Your Information' })\n    3) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Data Storage and Security</h2> aka getByRole('heading', { name: 'Data Storage and Security' })\n    4) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Third-Party Services</h2> aka getByRole('heading', { name: 'Third-Party Services' })\n    5) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Cookies and Tracking Technologies</h2> aka getByRole('heading', { name: 'Cookies and Tracking' })\n    6) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Your Privacy Rights</h2> aka getByRole('heading', { name: 'Your Privacy Rights' })\n    7) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Children's Privacy</h2> aka getByRole('heading', { name: 'Children\\'s Privacy' })\n    8) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Changes to This Privacy Policy</h2> aka getByRole('heading', { name: 'Changes to This Privacy Policy' })\n    9) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Contact Us</h2> aka getByRole('main').getByRole('heading', { name: 'Contact Us' })\n\nCall log:\n\u001b[2m  - Expect \"toContainText\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('h2')\u001b[22m\n\n\n\u001b[0m \u001b[90m 21 |\u001b[39m     \n \u001b[90m 22 |\u001b[39m     \u001b[90m// Check for key sections\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 23 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'h2'\u001b[39m))\u001b[33m.\u001b[39mtoContainText(\u001b[32m'Information We Collect'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                      \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 24 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'h2'\u001b[39m))\u001b[33m.\u001b[39mtoContainText(\u001b[32m'How We Use Your Information'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 25 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'h2'\u001b[39m))\u001b[33m.\u001b[39mtoContainText(\u001b[32m'Data Storage and Security'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 26 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'h2'\u001b[39m))\u001b[33m.\u001b[39mtoContainText(\u001b[32m'Contact Us'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts:23:38\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-02T00:28:10.121Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Development/Ice-Box-Hockey/verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-70c35-rivacy-Policy-page-directly-firefox/test-failed-1.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Development/Ice-Box-Hockey/verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-70c35-rivacy-Policy-page-directly-firefox/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts", "column": 38, "line": 23}}], "status": "unexpected"}], "id": "dc566187105446b158ed-d92b4607ff983c83f382", "file": "privacy-policy.spec.ts", "line": 13, "column": 3}, {"title": "should navigate to Privacy Policy from footer link", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 9, "parallelIndex": 1, "status": "failed", "duration": 5885, "error": {"message": "Error: expect.toContainText: Error: strict mode violation: locator('h1') resolved to 2 elements:\n    1) <h1 class=\"sr-only\">The Ice Box - Premier Hockey Equipment Store</h1> aka getByText('The Ice Box - Premier Hockey')\n    2) <h1 class=\"text-4xl md:text-5xl font-bold text-white mb-6 text-6xl md:text-7xl leading-tight mb-6\">The Ice Box</h1> aka getByText('The Ice Box', { exact: true })\n\nCall log:\n\u001b[2m  - Expect \"toContainText\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('h1')\u001b[22m\n", "stack": "Error: expect.toContainText: Error: strict mode violation: locator('h1') resolved to 2 elements:\n    1) <h1 class=\"sr-only\">The Ice Box - Premier Hockey Equipment Store</h1> aka getByText('The Ice Box - Premier Hockey')\n    2) <h1 class=\"text-4xl md:text-5xl font-bold text-white mb-6 text-6xl md:text-7xl leading-tight mb-6\">The Ice Box</h1> aka getByText('The Ice Box', { exact: true })\n\nCall log:\n\u001b[2m  - Expect \"toContainText\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('h1')\u001b[22m\n\n    at /Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts:41:38", "location": {"file": "/Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts", "column": 38, "line": 41}, "snippet": "\u001b[0m \u001b[90m 39 |\u001b[39m     \u001b[90m// Verify navigation to Privacy Policy page\u001b[39m\n \u001b[90m 40 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page)\u001b[33m.\u001b[39mtoHaveURL(\u001b[32m'/privacy-policy'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 41 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'h1'\u001b[39m))\u001b[33m.\u001b[39mtoContainText(\u001b[32m'Privacy Policy'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                      \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 42 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 43 |\u001b[39m\n \u001b[90m 44 |\u001b[39m   test(\u001b[32m'should display contact information'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts", "column": 38, "line": 41}, "message": "Error: expect.toContainText: Error: strict mode violation: locator('h1') resolved to 2 elements:\n    1) <h1 class=\"sr-only\">The Ice Box - Premier Hockey Equipment Store</h1> aka getByText('The Ice Box - Premier Hockey')\n    2) <h1 class=\"text-4xl md:text-5xl font-bold text-white mb-6 text-6xl md:text-7xl leading-tight mb-6\">The Ice Box</h1> aka getByText('The Ice Box', { exact: true })\n\nCall log:\n\u001b[2m  - Expect \"toContainText\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('h1')\u001b[22m\n\n\n\u001b[0m \u001b[90m 39 |\u001b[39m     \u001b[90m// Verify navigation to Privacy Policy page\u001b[39m\n \u001b[90m 40 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page)\u001b[33m.\u001b[39mtoHaveURL(\u001b[32m'/privacy-policy'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 41 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'h1'\u001b[39m))\u001b[33m.\u001b[39mtoContainText(\u001b[32m'Privacy Policy'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                      \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 42 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 43 |\u001b[39m\n \u001b[90m 44 |\u001b[39m   test(\u001b[32m'should display contact information'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\u001b[0m\n\u001b[2m    at /Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts:41:38\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-02T00:28:10.345Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Development/Ice-Box-Hockey/verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-5025e-acy-Policy-from-footer-link-firefox/test-failed-1.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Development/Ice-Box-Hockey/verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-5025e-acy-Policy-from-footer-link-firefox/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts", "column": 38, "line": 41}}], "status": "unexpected"}], "id": "dc566187105446b158ed-6e981aec310a566ebd95", "file": "privacy-policy.spec.ts", "line": 29, "column": 3}, {"title": "should display contact information", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 11, "parallelIndex": 2, "status": "failed", "duration": 5063, "error": {"message": "Error: expect.toBeVisible: Error: strict mode violation: locator('section').filter({ hasText: 'Contact Us' }) resolved to 2 elements:\n    1) <section>…</section> aka getByText('Your Privacy RightsYou have')\n    2) <section>…</section> aka getByText('Contact UsIf you have any')\n\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('section').filter({ hasText: 'Contact Us' })\u001b[22m\n", "stack": "Error: expect.toBeVisible: Error: strict mode violation: locator('section').filter({ hasText: 'Contact Us' }) resolved to 2 elements:\n    1) <section>…</section> aka getByText('Your Privacy RightsYou have')\n    2) <section>…</section> aka getByText('Contact UsIf you have any')\n\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('section').filter({ hasText: 'Contact Us' })\u001b[22m\n\n    at /Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts:49:34", "location": {"file": "/Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts", "column": 34, "line": 49}, "snippet": "\u001b[0m \u001b[90m 47 |\u001b[39m     \u001b[90m// Check for contact section\u001b[39m\n \u001b[90m 48 |\u001b[39m     \u001b[36mconst\u001b[39m contactSection \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'section'\u001b[39m)\u001b[33m.\u001b[39mfilter({ hasText\u001b[33m:\u001b[39m \u001b[32m'Contact Us'\u001b[39m })\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 49 |\u001b[39m     \u001b[36mawait\u001b[39m expect(contactSection)\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 50 |\u001b[39m     \n \u001b[90m 51 |\u001b[39m     \u001b[90m// Check for business details\u001b[39m\n \u001b[90m 52 |\u001b[39m     \u001b[36mawait\u001b[39m expect(contactSection)\u001b[33m.\u001b[39mtoContainText(\u001b[32m'The Ice Box Hockey'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts", "column": 34, "line": 49}, "message": "Error: expect.toBeVisible: Error: strict mode violation: locator('section').filter({ hasText: 'Contact Us' }) resolved to 2 elements:\n    1) <section>…</section> aka getByText('Your Privacy RightsYou have')\n    2) <section>…</section> aka getByText('Contact UsIf you have any')\n\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('section').filter({ hasText: 'Contact Us' })\u001b[22m\n\n\n\u001b[0m \u001b[90m 47 |\u001b[39m     \u001b[90m// Check for contact section\u001b[39m\n \u001b[90m 48 |\u001b[39m     \u001b[36mconst\u001b[39m contactSection \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'section'\u001b[39m)\u001b[33m.\u001b[39mfilter({ hasText\u001b[33m:\u001b[39m \u001b[32m'Contact Us'\u001b[39m })\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 49 |\u001b[39m     \u001b[36mawait\u001b[39m expect(contactSection)\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 50 |\u001b[39m     \n \u001b[90m 51 |\u001b[39m     \u001b[90m// Check for business details\u001b[39m\n \u001b[90m 52 |\u001b[39m     \u001b[36mawait\u001b[39m expect(contactSection)\u001b[33m.\u001b[39mtoContainText(\u001b[32m'The Ice Box Hockey'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts:49:34\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-02T00:28:15.180Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Development/Ice-Box-Hockey/verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-464f2-display-contact-information-firefox/test-failed-1.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Development/Ice-Box-Hockey/verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-464f2-display-contact-information-firefox/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts", "column": 34, "line": 49}}], "status": "unexpected"}], "id": "dc566187105446b158ed-1009ba6716b2365eaaf2", "file": "privacy-policy.spec.ts", "line": 44, "column": 3}, {"title": "should have proper SEO meta tags", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 10, "parallelIndex": 0, "status": "failed", "duration": 6209, "error": {"message": "Error: expect.toHaveAttribute: Error: strict mode violation: locator('meta[name=\"description\"]') resolved to 2 elements:\n    1) <meta name=\"description\" content=\"Ice Box Hockey - Your premier destination for hockey equipment, gear, and accessories. Find top brands, expert advice, and everything you need for the game.\"/> aka locator('meta[name=\"description\"]').first()\n    2) <meta data-rh=\"true\" name=\"description\" content=\"Learn about how The Ice Box Hockey collects, uses, and protects your personal information. Read our comprehensive privacy policy.\"/> aka locator('meta[name=\"description\"]').nth(1)\n\nCall log:\n\u001b[2m  - Expect \"toHaveAttribute\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('meta[name=\"description\"]')\u001b[22m\n", "stack": "Error: expect.toHaveAttribute: Error: strict mode violation: locator('meta[name=\"description\"]') resolved to 2 elements:\n    1) <meta name=\"description\" content=\"Ice Box Hockey - Your premier destination for hockey equipment, gear, and accessories. Find top brands, expert advice, and everything you need for the game.\"/> aka locator('meta[name=\"description\"]').first()\n    2) <meta data-rh=\"true\" name=\"description\" content=\"Learn about how The Ice Box Hockey collects, uses, and protects your personal information. Read our comprehensive privacy policy.\"/> aka locator('meta[name=\"description\"]').nth(1)\n\nCall log:\n\u001b[2m  - Expect \"toHaveAttribute\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('meta[name=\"description\"]')\u001b[22m\n\n    at /Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts:63:35", "location": {"file": "/Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts", "column": 35, "line": 63}, "snippet": "\u001b[0m \u001b[90m 61 |\u001b[39m     \u001b[90m// Check meta description\u001b[39m\n \u001b[90m 62 |\u001b[39m     \u001b[36mconst\u001b[39m metaDescription \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'meta[name=\"description\"]'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 63 |\u001b[39m     \u001b[36mawait\u001b[39m expect(metaDescription)\u001b[33m.\u001b[39mtoHaveAttribute(\u001b[32m'content'\u001b[39m\u001b[33m,\u001b[39m \u001b[35m/privacy policy.*Ice Box Hockey/\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                   \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 64 |\u001b[39m     \n \u001b[90m 65 |\u001b[39m     \u001b[90m// Check title\u001b[39m\n \u001b[90m 66 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page)\u001b[33m.\u001b[39mtoHaveTitle(\u001b[35m/Privacy Policy.*Ice Box Hockey/\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts", "column": 35, "line": 63}, "message": "Error: expect.toHaveAttribute: Error: strict mode violation: locator('meta[name=\"description\"]') resolved to 2 elements:\n    1) <meta name=\"description\" content=\"Ice Box Hockey - Your premier destination for hockey equipment, gear, and accessories. Find top brands, expert advice, and everything you need for the game.\"/> aka locator('meta[name=\"description\"]').first()\n    2) <meta data-rh=\"true\" name=\"description\" content=\"Learn about how The Ice Box Hockey collects, uses, and protects your personal information. Read our comprehensive privacy policy.\"/> aka locator('meta[name=\"description\"]').nth(1)\n\nCall log:\n\u001b[2m  - Expect \"toHaveAttribute\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('meta[name=\"description\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 61 |\u001b[39m     \u001b[90m// Check meta description\u001b[39m\n \u001b[90m 62 |\u001b[39m     \u001b[36mconst\u001b[39m metaDescription \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'meta[name=\"description\"]'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 63 |\u001b[39m     \u001b[36mawait\u001b[39m expect(metaDescription)\u001b[33m.\u001b[39mtoHaveAttribute(\u001b[32m'content'\u001b[39m\u001b[33m,\u001b[39m \u001b[35m/privacy policy.*Ice Box Hockey/\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                   \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 64 |\u001b[39m     \n \u001b[90m 65 |\u001b[39m     \u001b[90m// Check title\u001b[39m\n \u001b[90m 66 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page)\u001b[33m.\u001b[39mtoHaveTitle(\u001b[35m/Privacy Policy.*Ice Box Hockey/\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts:63:35\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-02T00:28:14.769Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Development/Ice-Box-Hockey/verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-7b6a4-d-have-proper-SEO-meta-tags-firefox/test-failed-1.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Development/Ice-Box-Hockey/verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-7b6a4-d-have-proper-SEO-meta-tags-firefox/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts", "column": 35, "line": 63}}], "status": "unexpected"}], "id": "dc566187105446b158ed-a096507228e421732ee8", "file": "privacy-policy.spec.ts", "line": 58, "column": 3}, {"title": "should be responsive on mobile", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 12, "parallelIndex": 4, "status": "failed", "duration": 5856, "error": {"message": "Error: expect.toBeVisible: Error: strict mode violation: locator('section').filter({ hasText: 'Contact Us' }) resolved to 2 elements:\n    1) <section>…</section> aka getByText('Your Privacy RightsYou have')\n    2) <section>…</section> aka getByText('Contact UsIf you have any')\n\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('section').filter({ hasText: 'Contact Us' })\u001b[22m\n", "stack": "Error: expect.toBeVisible: Error: strict mode violation: locator('section').filter({ hasText: 'Contact Us' }) resolved to 2 elements:\n    1) <section>…</section> aka getByText('Your Privacy RightsYou have')\n    2) <section>…</section> aka getByText('Contact UsIf you have any')\n\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('section').filter({ hasText: 'Contact Us' })\u001b[22m\n\n    at /Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts:80:34", "location": {"file": "/Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts", "column": 34, "line": 80}, "snippet": "\u001b[0m \u001b[90m 78 |\u001b[39m     \u001b[90m// Check if contact section is readable on mobile\u001b[39m\n \u001b[90m 79 |\u001b[39m     \u001b[36mconst\u001b[39m contactSection \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'section'\u001b[39m)\u001b[33m.\u001b[39mfilter({ hasText\u001b[33m:\u001b[39m \u001b[32m'Contact Us'\u001b[39m })\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 80 |\u001b[39m     \u001b[36mawait\u001b[39m expect(contactSection)\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 81 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 82 |\u001b[39m\n \u001b[90m 83 |\u001b[39m   test(\u001b[32m'should have working email link'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts", "column": 34, "line": 80}, "message": "Error: expect.toBeVisible: Error: strict mode violation: locator('section').filter({ hasText: 'Contact Us' }) resolved to 2 elements:\n    1) <section>…</section> aka getByText('Your Privacy RightsYou have')\n    2) <section>…</section> aka getByText('Contact UsIf you have any')\n\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('section').filter({ hasText: 'Contact Us' })\u001b[22m\n\n\n\u001b[0m \u001b[90m 78 |\u001b[39m     \u001b[90m// Check if contact section is readable on mobile\u001b[39m\n \u001b[90m 79 |\u001b[39m     \u001b[36mconst\u001b[39m contactSection \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'section'\u001b[39m)\u001b[33m.\u001b[39mfilter({ hasText\u001b[33m:\u001b[39m \u001b[32m'Contact Us'\u001b[39m })\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 80 |\u001b[39m     \u001b[36mawait\u001b[39m expect(contactSection)\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 81 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 82 |\u001b[39m\n \u001b[90m 83 |\u001b[39m   test(\u001b[32m'should have working email link'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\u001b[0m\n\u001b[2m    at /Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts:80:34\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-02T00:28:21.794Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Development/Ice-Box-Hockey/verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-f8298-uld-be-responsive-on-mobile-firefox/test-failed-1.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Development/Ice-Box-Hockey/verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-f8298-uld-be-responsive-on-mobile-firefox/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts", "column": 34, "line": 80}}], "status": "unexpected"}], "id": "dc566187105446b158ed-045c5f02e9f9f6b12642", "file": "privacy-policy.spec.ts", "line": 69, "column": 3}, {"title": "should have working email link", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 13, "parallelIndex": 1, "status": "failed", "duration": 7194, "error": {"message": "Error: expect.toBeVisible: Error: strict mode violation: locator('a[href=\"mailto:<EMAIL>\"]') resolved to 2 elements:\n    1) <a href=\"mailto:<EMAIL>\" class=\"text-blue-600 hover:text-blue-800\"><EMAIL></a> aka getByRole('main').getByRole('link', { name: '<EMAIL>' })\n    2) <a href=\"mailto:<EMAIL>\" class=\"ml-3 text-white/90 hover:text-blue-400 transition-colors\"><EMAIL></a> aka locator('#contact').getByRole('link', { name: '<EMAIL>' })\n\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('a[href=\"mailto:<EMAIL>\"]')\u001b[22m\n", "stack": "Error: expect.toBeVisible: Error: strict mode violation: locator('a[href=\"mailto:<EMAIL>\"]') resolved to 2 elements:\n    1) <a href=\"mailto:<EMAIL>\" class=\"text-blue-600 hover:text-blue-800\"><EMAIL></a> aka getByRole('main').getByRole('link', { name: '<EMAIL>' })\n    2) <a href=\"mailto:<EMAIL>\" class=\"ml-3 text-white/90 hover:text-blue-400 transition-colors\"><EMAIL></a> aka locator('#contact').getByRole('link', { name: '<EMAIL>' })\n\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('a[href=\"mailto:<EMAIL>\"]')\u001b[22m\n\n    at /Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts:88:29", "location": {"file": "/Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts", "column": 29, "line": 88}, "snippet": "\u001b[0m \u001b[90m 86 |\u001b[39m     \u001b[90m// Check for email link in contact section\u001b[39m\n \u001b[90m 87 |\u001b[39m     \u001b[36mconst\u001b[39m emailLink \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'a[href=\"mailto:<EMAIL>\"]'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 88 |\u001b[39m     \u001b[36mawait\u001b[39m expect(emailLink)\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                             \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 89 |\u001b[39m     \u001b[36mawait\u001b[39m expect(emailLink)\u001b[33m.\u001b[39mtoHaveAttribute(\u001b[32m'href'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'mailto:<EMAIL>'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 90 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 91 |\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts", "column": 29, "line": 88}, "message": "Error: expect.toBeVisible: Error: strict mode violation: locator('a[href=\"mailto:<EMAIL>\"]') resolved to 2 elements:\n    1) <a href=\"mailto:<EMAIL>\" class=\"text-blue-600 hover:text-blue-800\"><EMAIL></a> aka getByRole('main').getByRole('link', { name: '<EMAIL>' })\n    2) <a href=\"mailto:<EMAIL>\" class=\"ml-3 text-white/90 hover:text-blue-400 transition-colors\"><EMAIL></a> aka locator('#contact').getByRole('link', { name: '<EMAIL>' })\n\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('a[href=\"mailto:<EMAIL>\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 86 |\u001b[39m     \u001b[90m// Check for email link in contact section\u001b[39m\n \u001b[90m 87 |\u001b[39m     \u001b[36mconst\u001b[39m emailLink \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'a[href=\"mailto:<EMAIL>\"]'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 88 |\u001b[39m     \u001b[36mawait\u001b[39m expect(emailLink)\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                             \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 89 |\u001b[39m     \u001b[36mawait\u001b[39m expect(emailLink)\u001b[33m.\u001b[39mtoHaveAttribute(\u001b[32m'href'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'mailto:<EMAIL>'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 90 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 91 |\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts:88:29\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-02T00:28:21.889Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Development/Ice-Box-Hockey/verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-c02f5-uld-have-working-email-link-firefox/test-failed-1.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Development/Ice-Box-Hockey/verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-c02f5-uld-have-working-email-link-firefox/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts", "column": 29, "line": 88}}], "status": "unexpected"}], "id": "dc566187105446b158ed-fb8caaaa8814f7601251", "file": "privacy-policy.spec.ts", "line": 83, "column": 3}, {"title": "should display last updated date", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 14, "parallelIndex": 2, "status": "passed", "duration": 6297, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-02T00:28:23.623Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "dc566187105446b158ed-c518d0b2aa2e6594c3d6", "file": "privacy-policy.spec.ts", "line": 92, "column": 3}, {"title": "should have proper heading hierarchy", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 15, "parallelIndex": 0, "status": "failed", "duration": 15890, "error": {"message": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveCount\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('h2')\nExpected: \u001b[32m8\u001b[39m\nReceived: \u001b[31m9\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveCount\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('h2')\u001b[22m\n\u001b[2m    13 × locator resolved to 9 elements\u001b[22m\n\u001b[2m       - unexpected value \"9\"\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveCount\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('h2')\nExpected: \u001b[32m8\u001b[39m\nReceived: \u001b[31m9\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveCount\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('h2')\u001b[22m\n\u001b[2m    13 × locator resolved to 9 elements\u001b[22m\n\u001b[2m       - unexpected value \"9\"\u001b[22m\n\n    at /Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts:115:30", "location": {"file": "/Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts", "column": 30, "line": 115}, "snippet": "\u001b[0m \u001b[90m 113 |\u001b[39m     \n \u001b[90m 114 |\u001b[39m     \u001b[90m// Should have multiple h2 sections\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 115 |\u001b[39m     \u001b[36mawait\u001b[39m expect(h2Elements)\u001b[33m.\u001b[39mtoHaveCount(\u001b[35m8\u001b[39m)\u001b[33m;\u001b[39m \u001b[90m// Based on our content structure\u001b[39m\n \u001b[90m     |\u001b[39m                              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 116 |\u001b[39m     \n \u001b[90m 117 |\u001b[39m     \u001b[90m// Should have h3 subsections\u001b[39m\n \u001b[90m 118 |\u001b[39m     \u001b[36mconst\u001b[39m h3Count \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m h3Elements\u001b[33m.\u001b[39mcount()\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts", "column": 30, "line": 115}, "message": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveCount\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('h2')\nExpected: \u001b[32m8\u001b[39m\nReceived: \u001b[31m9\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveCount\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('h2')\u001b[22m\n\u001b[2m    13 × locator resolved to 9 elements\u001b[22m\n\u001b[2m       - unexpected value \"9\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 113 |\u001b[39m     \n \u001b[90m 114 |\u001b[39m     \u001b[90m// Should have multiple h2 sections\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 115 |\u001b[39m     \u001b[36mawait\u001b[39m expect(h2Elements)\u001b[33m.\u001b[39mtoHaveCount(\u001b[35m8\u001b[39m)\u001b[33m;\u001b[39m \u001b[90m// Based on our content structure\u001b[39m\n \u001b[90m     |\u001b[39m                              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 116 |\u001b[39m     \n \u001b[90m 117 |\u001b[39m     \u001b[90m// Should have h3 subsections\u001b[39m\n \u001b[90m 118 |\u001b[39m     \u001b[36mconst\u001b[39m h3Count \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m h3Elements\u001b[33m.\u001b[39mcount()\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts:115:30\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-02T00:28:23.676Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Development/Ice-Box-Hockey/verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-10ffc-ve-proper-heading-hierarchy-firefox/test-failed-1.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Development/Ice-Box-Hockey/verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-10ffc-ve-proper-heading-hierarchy-firefox/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts", "column": 30, "line": 115}}], "status": "unexpected"}], "id": "dc566187105446b158ed-7772d3ce6d361d9ae208", "file": "privacy-policy.spec.ts", "line": 103, "column": 3}, {"title": "should load Privacy Policy page directly", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 16, "parallelIndex": 3, "status": "failed", "duration": 3866, "error": {"message": "Error: expect.toContainText: Error: strict mode violation: locator('h2') resolved to 9 elements:\n    1) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Information We Collect</h2> aka getByRole('heading', { name: 'Information We Collect' })\n    2) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">How We Use Your Information</h2> aka getByRole('heading', { name: 'How We Use Your Information' })\n    3) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Data Storage and Security</h2> aka getByRole('heading', { name: 'Data Storage and Security' })\n    4) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Third-Party Services</h2> aka getByRole('heading', { name: 'Third-Party Services' })\n    5) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Cookies and Tracking Technologies</h2> aka getByRole('heading', { name: 'Cookies and Tracking' })\n    6) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Your Privacy Rights</h2> aka getByRole('heading', { name: 'Your Privacy Rights' })\n    7) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Children's Privacy</h2> aka getByRole('heading', { name: 'Children\\'s Privacy' })\n    8) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Changes to This Privacy Policy</h2> aka getByRole('heading', { name: 'Changes to This Privacy Policy' })\n    9) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Contact Us</h2> aka getByRole('main').getByRole('heading', { name: 'Contact Us' })\n\nCall log:\n\u001b[2m  - Expect \"toContainText\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('h2')\u001b[22m\n", "stack": "Error: expect.toContainText: Error: strict mode violation: locator('h2') resolved to 9 elements:\n    1) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Information We Collect</h2> aka getByRole('heading', { name: 'Information We Collect' })\n    2) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">How We Use Your Information</h2> aka getByRole('heading', { name: 'How We Use Your Information' })\n    3) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Data Storage and Security</h2> aka getByRole('heading', { name: 'Data Storage and Security' })\n    4) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Third-Party Services</h2> aka getByRole('heading', { name: 'Third-Party Services' })\n    5) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Cookies and Tracking Technologies</h2> aka getByRole('heading', { name: 'Cookies and Tracking' })\n    6) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Your Privacy Rights</h2> aka getByRole('heading', { name: 'Your Privacy Rights' })\n    7) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Children's Privacy</h2> aka getByRole('heading', { name: 'Children\\'s Privacy' })\n    8) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Changes to This Privacy Policy</h2> aka getByRole('heading', { name: 'Changes to This Privacy Policy' })\n    9) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Contact Us</h2> aka getByRole('main').getByRole('heading', { name: 'Contact Us' })\n\nCall log:\n\u001b[2m  - Expect \"toContainText\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('h2')\u001b[22m\n\n    at /Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts:23:38", "location": {"file": "/Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts", "column": 38, "line": 23}, "snippet": "\u001b[0m \u001b[90m 21 |\u001b[39m     \n \u001b[90m 22 |\u001b[39m     \u001b[90m// Check for key sections\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 23 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'h2'\u001b[39m))\u001b[33m.\u001b[39mtoContainText(\u001b[32m'Information We Collect'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                      \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 24 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'h2'\u001b[39m))\u001b[33m.\u001b[39mtoContainText(\u001b[32m'How We Use Your Information'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 25 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'h2'\u001b[39m))\u001b[33m.\u001b[39mtoContainText(\u001b[32m'Data Storage and Security'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 26 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'h2'\u001b[39m))\u001b[33m.\u001b[39mtoContainText(\u001b[32m'Contact Us'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts", "column": 38, "line": 23}, "message": "Error: expect.toContainText: Error: strict mode violation: locator('h2') resolved to 9 elements:\n    1) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Information We Collect</h2> aka getByRole('heading', { name: 'Information We Collect' })\n    2) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">How We Use Your Information</h2> aka getByRole('heading', { name: 'How We Use Your Information' })\n    3) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Data Storage and Security</h2> aka getByRole('heading', { name: 'Data Storage and Security' })\n    4) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Third-Party Services</h2> aka getByRole('heading', { name: 'Third-Party Services' })\n    5) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Cookies and Tracking Technologies</h2> aka getByRole('heading', { name: 'Cookies and Tracking' })\n    6) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Your Privacy Rights</h2> aka getByRole('heading', { name: 'Your Privacy Rights' })\n    7) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Children's Privacy</h2> aka getByRole('heading', { name: 'Children\\'s Privacy' })\n    8) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Changes to This Privacy Policy</h2> aka getByRole('heading', { name: 'Changes to This Privacy Policy' })\n    9) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Contact Us</h2> aka getByRole('main').getByRole('heading', { name: 'Contact Us' })\n\nCall log:\n\u001b[2m  - Expect \"toContainText\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('h2')\u001b[22m\n\n\n\u001b[0m \u001b[90m 21 |\u001b[39m     \n \u001b[90m 22 |\u001b[39m     \u001b[90m// Check for key sections\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 23 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'h2'\u001b[39m))\u001b[33m.\u001b[39mtoContainText(\u001b[32m'Information We Collect'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                      \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 24 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'h2'\u001b[39m))\u001b[33m.\u001b[39mtoContainText(\u001b[32m'How We Use Your Information'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 25 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'h2'\u001b[39m))\u001b[33m.\u001b[39mtoContainText(\u001b[32m'Data Storage and Security'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 26 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'h2'\u001b[39m))\u001b[33m.\u001b[39mtoContainText(\u001b[32m'Contact Us'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts:23:38\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-02T00:28:24.946Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Development/Ice-Box-Hockey/verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-70c35-rivacy-Policy-page-directly-webkit/test-failed-1.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Development/Ice-Box-Hockey/verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-70c35-rivacy-Policy-page-directly-webkit/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts", "column": 38, "line": 23}}], "status": "unexpected"}], "id": "dc566187105446b158ed-8155c9df82098edc62ba", "file": "privacy-policy.spec.ts", "line": 13, "column": 3}, {"title": "should navigate to Privacy Policy from footer link", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 17, "parallelIndex": 4, "status": "failed", "duration": 2876, "error": {"message": "Error: expect.toContainText: Error: strict mode violation: locator('h1') resolved to 2 elements:\n    1) <h1 class=\"sr-only\">The Ice Box - Premier Hockey Equipment Store</h1> aka getByText('The Ice Box - Premier Hockey')\n    2) <h1 class=\"text-4xl md:text-5xl font-bold text-white mb-6 text-6xl md:text-7xl leading-tight mb-6\">The Ice Box</h1> aka getByText('The Ice Box', { exact: true })\n\nCall log:\n\u001b[2m  - Expect \"toContainText\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('h1')\u001b[22m\n", "stack": "Error: expect.toContainText: Error: strict mode violation: locator('h1') resolved to 2 elements:\n    1) <h1 class=\"sr-only\">The Ice Box - Premier Hockey Equipment Store</h1> aka getByText('The Ice Box - Premier Hockey')\n    2) <h1 class=\"text-4xl md:text-5xl font-bold text-white mb-6 text-6xl md:text-7xl leading-tight mb-6\">The Ice Box</h1> aka getByText('The Ice Box', { exact: true })\n\nCall log:\n\u001b[2m  - Expect \"toContainText\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('h1')\u001b[22m\n\n    at /Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts:41:38", "location": {"file": "/Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts", "column": 38, "line": 41}, "snippet": "\u001b[0m \u001b[90m 39 |\u001b[39m     \u001b[90m// Verify navigation to Privacy Policy page\u001b[39m\n \u001b[90m 40 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page)\u001b[33m.\u001b[39mtoHaveURL(\u001b[32m'/privacy-policy'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 41 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'h1'\u001b[39m))\u001b[33m.\u001b[39mtoContainText(\u001b[32m'Privacy Policy'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                      \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 42 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 43 |\u001b[39m\n \u001b[90m 44 |\u001b[39m   test(\u001b[32m'should display contact information'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts", "column": 38, "line": 41}, "message": "Error: expect.toContainText: Error: strict mode violation: locator('h1') resolved to 2 elements:\n    1) <h1 class=\"sr-only\">The Ice Box - Premier Hockey Equipment Store</h1> aka getByText('The Ice Box - Premier Hockey')\n    2) <h1 class=\"text-4xl md:text-5xl font-bold text-white mb-6 text-6xl md:text-7xl leading-tight mb-6\">The Ice Box</h1> aka getByText('The Ice Box', { exact: true })\n\nCall log:\n\u001b[2m  - Expect \"toContainText\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('h1')\u001b[22m\n\n\n\u001b[0m \u001b[90m 39 |\u001b[39m     \u001b[90m// Verify navigation to Privacy Policy page\u001b[39m\n \u001b[90m 40 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page)\u001b[33m.\u001b[39mtoHaveURL(\u001b[32m'/privacy-policy'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 41 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'h1'\u001b[39m))\u001b[33m.\u001b[39mtoContainText(\u001b[32m'Privacy Policy'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                      \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 42 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 43 |\u001b[39m\n \u001b[90m 44 |\u001b[39m   test(\u001b[32m'should display contact information'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\u001b[0m\n\u001b[2m    at /Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts:41:38\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-02T00:28:32.121Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Development/Ice-Box-Hockey/verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-5025e-acy-Policy-from-footer-link-webkit/test-failed-1.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Development/Ice-Box-Hockey/verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-5025e-acy-Policy-from-footer-link-webkit/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts", "column": 38, "line": 41}}], "status": "unexpected"}], "id": "dc566187105446b158ed-03339a9f2033514697e7", "file": "privacy-policy.spec.ts", "line": 29, "column": 3}, {"title": "should display contact information", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 18, "parallelIndex": 3, "status": "failed", "duration": 2373, "error": {"message": "Error: expect.toBeVisible: Error: strict mode violation: locator('section').filter({ hasText: 'Contact Us' }) resolved to 2 elements:\n    1) <section>…</section> aka getByText('Your Privacy RightsYou have')\n    2) <section>…</section> aka getByText('Contact UsIf you have any')\n\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('section').filter({ hasText: 'Contact Us' })\u001b[22m\n", "stack": "Error: expect.toBeVisible: Error: strict mode violation: locator('section').filter({ hasText: 'Contact Us' }) resolved to 2 elements:\n    1) <section>…</section> aka getByText('Your Privacy RightsYou have')\n    2) <section>…</section> aka getByText('Contact UsIf you have any')\n\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('section').filter({ hasText: 'Contact Us' })\u001b[22m\n\n    at /Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts:49:34", "location": {"file": "/Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts", "column": 34, "line": 49}, "snippet": "\u001b[0m \u001b[90m 47 |\u001b[39m     \u001b[90m// Check for contact section\u001b[39m\n \u001b[90m 48 |\u001b[39m     \u001b[36mconst\u001b[39m contactSection \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'section'\u001b[39m)\u001b[33m.\u001b[39mfilter({ hasText\u001b[33m:\u001b[39m \u001b[32m'Contact Us'\u001b[39m })\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 49 |\u001b[39m     \u001b[36mawait\u001b[39m expect(contactSection)\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 50 |\u001b[39m     \n \u001b[90m 51 |\u001b[39m     \u001b[90m// Check for business details\u001b[39m\n \u001b[90m 52 |\u001b[39m     \u001b[36mawait\u001b[39m expect(contactSection)\u001b[33m.\u001b[39mtoContainText(\u001b[32m'The Ice Box Hockey'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts", "column": 34, "line": 49}, "message": "Error: expect.toBeVisible: Error: strict mode violation: locator('section').filter({ hasText: 'Contact Us' }) resolved to 2 elements:\n    1) <section>…</section> aka getByText('Your Privacy RightsYou have')\n    2) <section>…</section> aka getByText('Contact UsIf you have any')\n\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('section').filter({ hasText: 'Contact Us' })\u001b[22m\n\n\n\u001b[0m \u001b[90m 47 |\u001b[39m     \u001b[90m// Check for contact section\u001b[39m\n \u001b[90m 48 |\u001b[39m     \u001b[36mconst\u001b[39m contactSection \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'section'\u001b[39m)\u001b[33m.\u001b[39mfilter({ hasText\u001b[33m:\u001b[39m \u001b[32m'Contact Us'\u001b[39m })\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 49 |\u001b[39m     \u001b[36mawait\u001b[39m expect(contactSection)\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 50 |\u001b[39m     \n \u001b[90m 51 |\u001b[39m     \u001b[90m// Check for business details\u001b[39m\n \u001b[90m 52 |\u001b[39m     \u001b[36mawait\u001b[39m expect(contactSection)\u001b[33m.\u001b[39mtoContainText(\u001b[32m'The Ice Box Hockey'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts:49:34\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-02T00:28:32.169Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Development/Ice-Box-Hockey/verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-464f2-display-contact-information-webkit/test-failed-1.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Development/Ice-Box-Hockey/verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-464f2-display-contact-information-webkit/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts", "column": 34, "line": 49}}], "status": "unexpected"}], "id": "dc566187105446b158ed-d4c66cad9d26b9b05670", "file": "privacy-policy.spec.ts", "line": 44, "column": 3}, {"title": "should have proper SEO meta tags", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 20, "parallelIndex": 2, "status": "failed", "duration": 2601, "error": {"message": "Error: expect.toHaveAttribute: Error: strict mode violation: locator('meta[name=\"description\"]') resolved to 2 elements:\n    1) <meta name=\"description\" content=\"Ice Box Hockey - Your premier destination for hockey equipment, gear, and accessories. Find top brands, expert advice, and everything you need for the game.\"/> aka locator('meta[name=\"description\"]').first()\n    2) <meta data-rh=\"true\" name=\"description\" content=\"Learn about how The Ice Box Hockey collects, uses, and protects your personal information. Read our comprehensive privacy policy.\"/> aka locator('meta[name=\"description\"]').nth(1)\n\nCall log:\n\u001b[2m  - Expect \"toHaveAttribute\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('meta[name=\"description\"]')\u001b[22m\n\u001b[2m    3 × locator resolved to <meta name=\"description\" content=\"Ice Box Hockey - Your premier destination for hockey equipment, gear, and accessories. Find top brands, expert advice, and everything you need for the game.\"/>\u001b[22m\n\u001b[2m      - unexpected value \"Ice Box Hockey - Your premier destination for hockey equipment, gear, and accessories. Find top brands, expert advice, and everything you need for the game.\"\u001b[22m\n", "stack": "Error: expect.toHaveAttribute: Error: strict mode violation: locator('meta[name=\"description\"]') resolved to 2 elements:\n    1) <meta name=\"description\" content=\"Ice Box Hockey - Your premier destination for hockey equipment, gear, and accessories. Find top brands, expert advice, and everything you need for the game.\"/> aka locator('meta[name=\"description\"]').first()\n    2) <meta data-rh=\"true\" name=\"description\" content=\"Learn about how The Ice Box Hockey collects, uses, and protects your personal information. Read our comprehensive privacy policy.\"/> aka locator('meta[name=\"description\"]').nth(1)\n\nCall log:\n\u001b[2m  - Expect \"toHaveAttribute\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('meta[name=\"description\"]')\u001b[22m\n\u001b[2m    3 × locator resolved to <meta name=\"description\" content=\"Ice Box Hockey - Your premier destination for hockey equipment, gear, and accessories. Find top brands, expert advice, and everything you need for the game.\"/>\u001b[22m\n\u001b[2m      - unexpected value \"Ice Box Hockey - Your premier destination for hockey equipment, gear, and accessories. Find top brands, expert advice, and everything you need for the game.\"\u001b[22m\n\n    at /Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts:63:35", "location": {"file": "/Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts", "column": 35, "line": 63}, "snippet": "\u001b[0m \u001b[90m 61 |\u001b[39m     \u001b[90m// Check meta description\u001b[39m\n \u001b[90m 62 |\u001b[39m     \u001b[36mconst\u001b[39m metaDescription \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'meta[name=\"description\"]'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 63 |\u001b[39m     \u001b[36mawait\u001b[39m expect(metaDescription)\u001b[33m.\u001b[39mtoHaveAttribute(\u001b[32m'content'\u001b[39m\u001b[33m,\u001b[39m \u001b[35m/privacy policy.*Ice Box Hockey/\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                   \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 64 |\u001b[39m     \n \u001b[90m 65 |\u001b[39m     \u001b[90m// Check title\u001b[39m\n \u001b[90m 66 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page)\u001b[33m.\u001b[39mtoHaveTitle(\u001b[35m/Privacy Policy.*Ice Box Hockey/\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts", "column": 35, "line": 63}, "message": "Error: expect.toHaveAttribute: Error: strict mode violation: locator('meta[name=\"description\"]') resolved to 2 elements:\n    1) <meta name=\"description\" content=\"Ice Box Hockey - Your premier destination for hockey equipment, gear, and accessories. Find top brands, expert advice, and everything you need for the game.\"/> aka locator('meta[name=\"description\"]').first()\n    2) <meta data-rh=\"true\" name=\"description\" content=\"Learn about how The Ice Box Hockey collects, uses, and protects your personal information. Read our comprehensive privacy policy.\"/> aka locator('meta[name=\"description\"]').nth(1)\n\nCall log:\n\u001b[2m  - Expect \"toHaveAttribute\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('meta[name=\"description\"]')\u001b[22m\n\u001b[2m    3 × locator resolved to <meta name=\"description\" content=\"Ice Box Hockey - Your premier destination for hockey equipment, gear, and accessories. Find top brands, expert advice, and everything you need for the game.\"/>\u001b[22m\n\u001b[2m      - unexpected value \"Ice Box Hockey - Your premier destination for hockey equipment, gear, and accessories. Find top brands, expert advice, and everything you need for the game.\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 61 |\u001b[39m     \u001b[90m// Check meta description\u001b[39m\n \u001b[90m 62 |\u001b[39m     \u001b[36mconst\u001b[39m metaDescription \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'meta[name=\"description\"]'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 63 |\u001b[39m     \u001b[36mawait\u001b[39m expect(metaDescription)\u001b[33m.\u001b[39mtoHaveAttribute(\u001b[32m'content'\u001b[39m\u001b[33m,\u001b[39m \u001b[35m/privacy policy.*Ice Box Hockey/\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                   \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 64 |\u001b[39m     \n \u001b[90m 65 |\u001b[39m     \u001b[90m// Check title\u001b[39m\n \u001b[90m 66 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page)\u001b[33m.\u001b[39mtoHaveTitle(\u001b[35m/Privacy Policy.*Ice Box Hockey/\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts:63:35\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-02T00:28:33.633Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Development/Ice-Box-Hockey/verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-7b6a4-d-have-proper-SEO-meta-tags-webkit/test-failed-1.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Development/Ice-Box-Hockey/verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-7b6a4-d-have-proper-SEO-meta-tags-webkit/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts", "column": 35, "line": 63}}], "status": "unexpected"}], "id": "dc566187105446b158ed-31af153b973be82f2b5a", "file": "privacy-policy.spec.ts", "line": 58, "column": 3}, {"title": "should be responsive on mobile", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 19, "parallelIndex": 1, "status": "failed", "duration": 2631, "error": {"message": "Error: expect.toBeVisible: Error: strict mode violation: locator('section').filter({ hasText: 'Contact Us' }) resolved to 2 elements:\n    1) <section>…</section> aka getByText('Your Privacy RightsYou have')\n    2) <section>…</section> aka getByText('Contact UsIf you have any')\n\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('section').filter({ hasText: 'Contact Us' })\u001b[22m\n", "stack": "Error: expect.toBeVisible: Error: strict mode violation: locator('section').filter({ hasText: 'Contact Us' }) resolved to 2 elements:\n    1) <section>…</section> aka getByText('Your Privacy RightsYou have')\n    2) <section>…</section> aka getByText('Contact UsIf you have any')\n\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('section').filter({ hasText: 'Contact Us' })\u001b[22m\n\n    at /Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts:80:34", "location": {"file": "/Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts", "column": 34, "line": 80}, "snippet": "\u001b[0m \u001b[90m 78 |\u001b[39m     \u001b[90m// Check if contact section is readable on mobile\u001b[39m\n \u001b[90m 79 |\u001b[39m     \u001b[36mconst\u001b[39m contactSection \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'section'\u001b[39m)\u001b[33m.\u001b[39mfilter({ hasText\u001b[33m:\u001b[39m \u001b[32m'Contact Us'\u001b[39m })\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 80 |\u001b[39m     \u001b[36mawait\u001b[39m expect(contactSection)\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 81 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 82 |\u001b[39m\n \u001b[90m 83 |\u001b[39m   test(\u001b[32m'should have working email link'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts", "column": 34, "line": 80}, "message": "Error: expect.toBeVisible: Error: strict mode violation: locator('section').filter({ hasText: 'Contact Us' }) resolved to 2 elements:\n    1) <section>…</section> aka getByText('Your Privacy RightsYou have')\n    2) <section>…</section> aka getByText('Contact UsIf you have any')\n\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('section').filter({ hasText: 'Contact Us' })\u001b[22m\n\n\n\u001b[0m \u001b[90m 78 |\u001b[39m     \u001b[90m// Check if contact section is readable on mobile\u001b[39m\n \u001b[90m 79 |\u001b[39m     \u001b[36mconst\u001b[39m contactSection \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'section'\u001b[39m)\u001b[33m.\u001b[39mfilter({ hasText\u001b[33m:\u001b[39m \u001b[32m'Contact Us'\u001b[39m })\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 80 |\u001b[39m     \u001b[36mawait\u001b[39m expect(contactSection)\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 81 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 82 |\u001b[39m\n \u001b[90m 83 |\u001b[39m   test(\u001b[32m'should have working email link'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\u001b[0m\n\u001b[2m    at /Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts:80:34\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-02T00:28:33.371Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Development/Ice-Box-Hockey/verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-f8298-uld-be-responsive-on-mobile-webkit/test-failed-1.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Development/Ice-Box-Hockey/verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-f8298-uld-be-responsive-on-mobile-webkit/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts", "column": 34, "line": 80}}], "status": "unexpected"}], "id": "dc566187105446b158ed-8f9d003dd34b9b2f1902", "file": "privacy-policy.spec.ts", "line": 69, "column": 3}, {"title": "should have working email link", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 21, "parallelIndex": 3, "status": "failed", "duration": 2369, "error": {"message": "Error: expect.toBeVisible: Error: strict mode violation: locator('a[href=\"mailto:<EMAIL>\"]') resolved to 2 elements:\n    1) <a href=\"mailto:<EMAIL>\" class=\"text-blue-600 hover:text-blue-800\"><EMAIL></a> aka getByRole('main').getByRole('link', { name: '<EMAIL>' })\n    2) <a href=\"mailto:<EMAIL>\" class=\"ml-3 text-white/90 hover:text-blue-400 transition-colors\"><EMAIL></a> aka locator('#contact').getByRole('link', { name: '<EMAIL>' })\n\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('a[href=\"mailto:<EMAIL>\"]')\u001b[22m\n", "stack": "Error: expect.toBeVisible: Error: strict mode violation: locator('a[href=\"mailto:<EMAIL>\"]') resolved to 2 elements:\n    1) <a href=\"mailto:<EMAIL>\" class=\"text-blue-600 hover:text-blue-800\"><EMAIL></a> aka getByRole('main').getByRole('link', { name: '<EMAIL>' })\n    2) <a href=\"mailto:<EMAIL>\" class=\"ml-3 text-white/90 hover:text-blue-400 transition-colors\"><EMAIL></a> aka locator('#contact').getByRole('link', { name: '<EMAIL>' })\n\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('a[href=\"mailto:<EMAIL>\"]')\u001b[22m\n\n    at /Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts:88:29", "location": {"file": "/Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts", "column": 29, "line": 88}, "snippet": "\u001b[0m \u001b[90m 86 |\u001b[39m     \u001b[90m// Check for email link in contact section\u001b[39m\n \u001b[90m 87 |\u001b[39m     \u001b[36mconst\u001b[39m emailLink \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'a[href=\"mailto:<EMAIL>\"]'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 88 |\u001b[39m     \u001b[36mawait\u001b[39m expect(emailLink)\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                             \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 89 |\u001b[39m     \u001b[36mawait\u001b[39m expect(emailLink)\u001b[33m.\u001b[39mtoHaveAttribute(\u001b[32m'href'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'mailto:<EMAIL>'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 90 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 91 |\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts", "column": 29, "line": 88}, "message": "Error: expect.toBeVisible: Error: strict mode violation: locator('a[href=\"mailto:<EMAIL>\"]') resolved to 2 elements:\n    1) <a href=\"mailto:<EMAIL>\" class=\"text-blue-600 hover:text-blue-800\"><EMAIL></a> aka getByRole('main').getByRole('link', { name: '<EMAIL>' })\n    2) <a href=\"mailto:<EMAIL>\" class=\"ml-3 text-white/90 hover:text-blue-400 transition-colors\"><EMAIL></a> aka locator('#contact').getByRole('link', { name: '<EMAIL>' })\n\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('a[href=\"mailto:<EMAIL>\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 86 |\u001b[39m     \u001b[90m// Check for email link in contact section\u001b[39m\n \u001b[90m 87 |\u001b[39m     \u001b[36mconst\u001b[39m emailLink \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'a[href=\"mailto:<EMAIL>\"]'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 88 |\u001b[39m     \u001b[36mawait\u001b[39m expect(emailLink)\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                             \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 89 |\u001b[39m     \u001b[36mawait\u001b[39m expect(emailLink)\u001b[33m.\u001b[39mtoHaveAttribute(\u001b[32m'href'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'mailto:<EMAIL>'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 90 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 91 |\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts:88:29\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-02T00:28:38.109Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Development/Ice-Box-Hockey/verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-c02f5-uld-have-working-email-link-webkit/test-failed-1.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Development/Ice-Box-Hockey/verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-c02f5-uld-have-working-email-link-webkit/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts", "column": 29, "line": 88}}], "status": "unexpected"}], "id": "dc566187105446b158ed-8fcb027d66eec68ac95c", "file": "privacy-policy.spec.ts", "line": 83, "column": 3}, {"title": "should display last updated date", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 22, "parallelIndex": 4, "status": "passed", "duration": 2144, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-02T00:28:38.215Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "dc566187105446b158ed-5c7e1122f4ca10e58c9d", "file": "privacy-policy.spec.ts", "line": 92, "column": 3}, {"title": "should have proper heading hierarchy", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 23, "parallelIndex": 1, "status": "failed", "duration": 12527, "error": {"message": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveCount\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('h2')\nExpected: \u001b[32m8\u001b[39m\nReceived: \u001b[31m9\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveCount\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('h2')\u001b[22m\n\u001b[2m    13 × locator resolved to 9 elements\u001b[22m\n\u001b[2m       - unexpected value \"9\"\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveCount\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('h2')\nExpected: \u001b[32m8\u001b[39m\nReceived: \u001b[31m9\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveCount\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('h2')\u001b[22m\n\u001b[2m    13 × locator resolved to 9 elements\u001b[22m\n\u001b[2m       - unexpected value \"9\"\u001b[22m\n\n    at /Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts:115:30", "location": {"file": "/Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts", "column": 30, "line": 115}, "snippet": "\u001b[0m \u001b[90m 113 |\u001b[39m     \n \u001b[90m 114 |\u001b[39m     \u001b[90m// Should have multiple h2 sections\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 115 |\u001b[39m     \u001b[36mawait\u001b[39m expect(h2Elements)\u001b[33m.\u001b[39mtoHaveCount(\u001b[35m8\u001b[39m)\u001b[33m;\u001b[39m \u001b[90m// Based on our content structure\u001b[39m\n \u001b[90m     |\u001b[39m                              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 116 |\u001b[39m     \n \u001b[90m 117 |\u001b[39m     \u001b[90m// Should have h3 subsections\u001b[39m\n \u001b[90m 118 |\u001b[39m     \u001b[36mconst\u001b[39m h3Count \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m h3Elements\u001b[33m.\u001b[39mcount()\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts", "column": 30, "line": 115}, "message": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveCount\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('h2')\nExpected: \u001b[32m8\u001b[39m\nReceived: \u001b[31m9\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveCount\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('h2')\u001b[22m\n\u001b[2m    13 × locator resolved to 9 elements\u001b[22m\n\u001b[2m       - unexpected value \"9\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 113 |\u001b[39m     \n \u001b[90m 114 |\u001b[39m     \u001b[90m// Should have multiple h2 sections\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 115 |\u001b[39m     \u001b[36mawait\u001b[39m expect(h2Elements)\u001b[33m.\u001b[39mtoHaveCount(\u001b[35m8\u001b[39m)\u001b[33m;\u001b[39m \u001b[90m// Based on our content structure\u001b[39m\n \u001b[90m     |\u001b[39m                              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 116 |\u001b[39m     \n \u001b[90m 117 |\u001b[39m     \u001b[90m// Should have h3 subsections\u001b[39m\n \u001b[90m 118 |\u001b[39m     \u001b[36mconst\u001b[39m h3Count \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m h3Elements\u001b[33m.\u001b[39mcount()\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts:115:30\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-02T00:28:38.791Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Development/Ice-Box-Hockey/verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-10ffc-ve-proper-heading-hierarchy-webkit/test-failed-1.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Development/Ice-Box-Hockey/verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-10ffc-ve-proper-heading-hierarchy-webkit/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts", "column": 30, "line": 115}}], "status": "unexpected"}], "id": "dc566187105446b158ed-ddcd8ca79a928e0b300d", "file": "privacy-policy.spec.ts", "line": 103, "column": 3}, {"title": "should load Privacy Policy page directly", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 24, "parallelIndex": 2, "status": "failed", "duration": 3509, "error": {"message": "Error: expect.toContainText: Error: strict mode violation: locator('h2') resolved to 9 elements:\n    1) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Information We Collect</h2> aka getByRole('heading', { name: 'Information We Collect' })\n    2) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">How We Use Your Information</h2> aka getByRole('heading', { name: 'How We Use Your Information' })\n    3) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Data Storage and Security</h2> aka getByRole('heading', { name: 'Data Storage and Security' })\n    4) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Third-Party Services</h2> aka getByRole('heading', { name: 'Third-Party Services' })\n    5) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Cookies and Tracking Technologies</h2> aka getByRole('heading', { name: 'Cookies and Tracking' })\n    6) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Your Privacy Rights</h2> aka getByRole('heading', { name: 'Your Privacy Rights' })\n    7) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Children's Privacy</h2> aka getByRole('heading', { name: 'Children\\'s Privacy' })\n    8) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Changes to This Privacy Policy</h2> aka getByRole('heading', { name: 'Changes to This Privacy Policy' })\n    9) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Contact Us</h2> aka getByRole('main').getByRole('heading', { name: 'Contact Us' })\n\nCall log:\n\u001b[2m  - Expect \"toContainText\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('h2')\u001b[22m\n", "stack": "Error: expect.toContainText: Error: strict mode violation: locator('h2') resolved to 9 elements:\n    1) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Information We Collect</h2> aka getByRole('heading', { name: 'Information We Collect' })\n    2) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">How We Use Your Information</h2> aka getByRole('heading', { name: 'How We Use Your Information' })\n    3) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Data Storage and Security</h2> aka getByRole('heading', { name: 'Data Storage and Security' })\n    4) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Third-Party Services</h2> aka getByRole('heading', { name: 'Third-Party Services' })\n    5) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Cookies and Tracking Technologies</h2> aka getByRole('heading', { name: 'Cookies and Tracking' })\n    6) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Your Privacy Rights</h2> aka getByRole('heading', { name: 'Your Privacy Rights' })\n    7) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Children's Privacy</h2> aka getByRole('heading', { name: 'Children\\'s Privacy' })\n    8) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Changes to This Privacy Policy</h2> aka getByRole('heading', { name: 'Changes to This Privacy Policy' })\n    9) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Contact Us</h2> aka getByRole('main').getByRole('heading', { name: 'Contact Us' })\n\nCall log:\n\u001b[2m  - Expect \"toContainText\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('h2')\u001b[22m\n\n    at /Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts:23:38", "location": {"file": "/Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts", "column": 38, "line": 23}, "snippet": "\u001b[0m \u001b[90m 21 |\u001b[39m     \n \u001b[90m 22 |\u001b[39m     \u001b[90m// Check for key sections\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 23 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'h2'\u001b[39m))\u001b[33m.\u001b[39mtoContainText(\u001b[32m'Information We Collect'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                      \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 24 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'h2'\u001b[39m))\u001b[33m.\u001b[39mtoContainText(\u001b[32m'How We Use Your Information'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 25 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'h2'\u001b[39m))\u001b[33m.\u001b[39mtoContainText(\u001b[32m'Data Storage and Security'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 26 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'h2'\u001b[39m))\u001b[33m.\u001b[39mtoContainText(\u001b[32m'Contact Us'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts", "column": 38, "line": 23}, "message": "Error: expect.toContainText: Error: strict mode violation: locator('h2') resolved to 9 elements:\n    1) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Information We Collect</h2> aka getByRole('heading', { name: 'Information We Collect' })\n    2) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">How We Use Your Information</h2> aka getByRole('heading', { name: 'How We Use Your Information' })\n    3) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Data Storage and Security</h2> aka getByRole('heading', { name: 'Data Storage and Security' })\n    4) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Third-Party Services</h2> aka getByRole('heading', { name: 'Third-Party Services' })\n    5) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Cookies and Tracking Technologies</h2> aka getByRole('heading', { name: 'Cookies and Tracking' })\n    6) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Your Privacy Rights</h2> aka getByRole('heading', { name: 'Your Privacy Rights' })\n    7) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Children's Privacy</h2> aka getByRole('heading', { name: 'Children\\'s Privacy' })\n    8) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Changes to This Privacy Policy</h2> aka getByRole('heading', { name: 'Changes to This Privacy Policy' })\n    9) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Contact Us</h2> aka getByRole('main').getByRole('heading', { name: 'Contact Us' })\n\nCall log:\n\u001b[2m  - Expect \"toContainText\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('h2')\u001b[22m\n\n\n\u001b[0m \u001b[90m 21 |\u001b[39m     \n \u001b[90m 22 |\u001b[39m     \u001b[90m// Check for key sections\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 23 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'h2'\u001b[39m))\u001b[33m.\u001b[39mtoContainText(\u001b[32m'Information We Collect'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                      \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 24 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'h2'\u001b[39m))\u001b[33m.\u001b[39mtoContainText(\u001b[32m'How We Use Your Information'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 25 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'h2'\u001b[39m))\u001b[33m.\u001b[39mtoContainText(\u001b[32m'Data Storage and Security'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 26 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'h2'\u001b[39m))\u001b[33m.\u001b[39mtoContainText(\u001b[32m'Contact Us'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts:23:38\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-02T00:28:39.163Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Development/Ice-Box-Hockey/verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-70c35-rivacy-Policy-page-directly-Mobile-Chrome/test-failed-1.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Development/Ice-Box-Hockey/verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-70c35-rivacy-Policy-page-directly-Mobile-Chrome/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts", "column": 38, "line": 23}}], "status": "unexpected"}], "id": "dc566187105446b158ed-a78cf4020539577808cd", "file": "privacy-policy.spec.ts", "line": 13, "column": 3}, {"title": "should navigate to Privacy Policy from footer link", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 25, "parallelIndex": 4, "status": "passed", "duration": 5469, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-02T00:28:43.207Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "dc566187105446b158ed-f6a5f25adfca35381660", "file": "privacy-policy.spec.ts", "line": 29, "column": 3}, {"title": "should display contact information", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 26, "parallelIndex": 3, "status": "failed", "duration": 3478, "error": {"message": "Error: expect.toBeVisible: Error: strict mode violation: locator('section').filter({ hasText: 'Contact Us' }) resolved to 2 elements:\n    1) <section>…</section> aka getByText('Your Privacy RightsYou have')\n    2) <section>…</section> aka getByText('Contact UsIf you have any')\n\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('section').filter({ hasText: 'Contact Us' })\u001b[22m\n", "stack": "Error: expect.toBeVisible: Error: strict mode violation: locator('section').filter({ hasText: 'Contact Us' }) resolved to 2 elements:\n    1) <section>…</section> aka getByText('Your Privacy RightsYou have')\n    2) <section>…</section> aka getByText('Contact UsIf you have any')\n\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('section').filter({ hasText: 'Contact Us' })\u001b[22m\n\n    at /Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts:49:34", "location": {"file": "/Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts", "column": 34, "line": 49}, "snippet": "\u001b[0m \u001b[90m 47 |\u001b[39m     \u001b[90m// Check for contact section\u001b[39m\n \u001b[90m 48 |\u001b[39m     \u001b[36mconst\u001b[39m contactSection \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'section'\u001b[39m)\u001b[33m.\u001b[39mfilter({ hasText\u001b[33m:\u001b[39m \u001b[32m'Contact Us'\u001b[39m })\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 49 |\u001b[39m     \u001b[36mawait\u001b[39m expect(contactSection)\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 50 |\u001b[39m     \n \u001b[90m 51 |\u001b[39m     \u001b[90m// Check for business details\u001b[39m\n \u001b[90m 52 |\u001b[39m     \u001b[36mawait\u001b[39m expect(contactSection)\u001b[33m.\u001b[39mtoContainText(\u001b[32m'The Ice Box Hockey'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts", "column": 34, "line": 49}, "message": "Error: expect.toBeVisible: Error: strict mode violation: locator('section').filter({ hasText: 'Contact Us' }) resolved to 2 elements:\n    1) <section>…</section> aka getByText('Your Privacy RightsYou have')\n    2) <section>…</section> aka getByText('Contact UsIf you have any')\n\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('section').filter({ hasText: 'Contact Us' })\u001b[22m\n\n\n\u001b[0m \u001b[90m 47 |\u001b[39m     \u001b[90m// Check for contact section\u001b[39m\n \u001b[90m 48 |\u001b[39m     \u001b[36mconst\u001b[39m contactSection \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'section'\u001b[39m)\u001b[33m.\u001b[39mfilter({ hasText\u001b[33m:\u001b[39m \u001b[32m'Contact Us'\u001b[39m })\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 49 |\u001b[39m     \u001b[36mawait\u001b[39m expect(contactSection)\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 50 |\u001b[39m     \n \u001b[90m 51 |\u001b[39m     \u001b[90m// Check for business details\u001b[39m\n \u001b[90m 52 |\u001b[39m     \u001b[36mawait\u001b[39m expect(contactSection)\u001b[33m.\u001b[39mtoContainText(\u001b[32m'The Ice Box Hockey'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts:49:34\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-02T00:28:43.188Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Development/Ice-Box-Hockey/verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-464f2-display-contact-information-Mobile-Chrome/test-failed-1.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Development/Ice-Box-Hockey/verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-464f2-display-contact-information-Mobile-Chrome/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts", "column": 34, "line": 49}}], "status": "unexpected"}], "id": "dc566187105446b158ed-b384c508e2aa5f31609b", "file": "privacy-policy.spec.ts", "line": 44, "column": 3}, {"title": "should have proper SEO meta tags", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 27, "parallelIndex": 0, "status": "failed", "duration": 1594, "error": {"message": "Error: expect.toHaveAttribute: Error: strict mode violation: locator('meta[name=\"description\"]') resolved to 2 elements:\n    1) <meta name=\"description\" content=\"Ice Box Hockey - Your premier destination for hockey equipment, gear, and accessories. Find top brands, expert advice, and everything you need for the game.\"/> aka locator('meta[name=\"description\"]').first()\n    2) <meta data-rh=\"true\" name=\"description\" content=\"Learn about how The Ice Box Hockey collects, uses, and protects your personal information. Read our comprehensive privacy policy.\"/> aka locator('meta[name=\"description\"]').nth(1)\n\nCall log:\n\u001b[2m  - Expect \"toHaveAttribute\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('meta[name=\"description\"]')\u001b[22m\n", "stack": "Error: expect.toHaveAttribute: Error: strict mode violation: locator('meta[name=\"description\"]') resolved to 2 elements:\n    1) <meta name=\"description\" content=\"Ice Box Hockey - Your premier destination for hockey equipment, gear, and accessories. Find top brands, expert advice, and everything you need for the game.\"/> aka locator('meta[name=\"description\"]').first()\n    2) <meta data-rh=\"true\" name=\"description\" content=\"Learn about how The Ice Box Hockey collects, uses, and protects your personal information. Read our comprehensive privacy policy.\"/> aka locator('meta[name=\"description\"]').nth(1)\n\nCall log:\n\u001b[2m  - Expect \"toHaveAttribute\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('meta[name=\"description\"]')\u001b[22m\n\n    at /Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts:63:35", "location": {"file": "/Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts", "column": 35, "line": 63}, "snippet": "\u001b[0m \u001b[90m 61 |\u001b[39m     \u001b[90m// Check meta description\u001b[39m\n \u001b[90m 62 |\u001b[39m     \u001b[36mconst\u001b[39m metaDescription \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'meta[name=\"description\"]'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 63 |\u001b[39m     \u001b[36mawait\u001b[39m expect(metaDescription)\u001b[33m.\u001b[39mtoHaveAttribute(\u001b[32m'content'\u001b[39m\u001b[33m,\u001b[39m \u001b[35m/privacy policy.*Ice Box Hockey/\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                   \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 64 |\u001b[39m     \n \u001b[90m 65 |\u001b[39m     \u001b[90m// Check title\u001b[39m\n \u001b[90m 66 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page)\u001b[33m.\u001b[39mtoHaveTitle(\u001b[35m/Privacy Policy.*Ice Box Hockey/\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts", "column": 35, "line": 63}, "message": "Error: expect.toHaveAttribute: Error: strict mode violation: locator('meta[name=\"description\"]') resolved to 2 elements:\n    1) <meta name=\"description\" content=\"Ice Box Hockey - Your premier destination for hockey equipment, gear, and accessories. Find top brands, expert advice, and everything you need for the game.\"/> aka locator('meta[name=\"description\"]').first()\n    2) <meta data-rh=\"true\" name=\"description\" content=\"Learn about how The Ice Box Hockey collects, uses, and protects your personal information. Read our comprehensive privacy policy.\"/> aka locator('meta[name=\"description\"]').nth(1)\n\nCall log:\n\u001b[2m  - Expect \"toHaveAttribute\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('meta[name=\"description\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 61 |\u001b[39m     \u001b[90m// Check meta description\u001b[39m\n \u001b[90m 62 |\u001b[39m     \u001b[36mconst\u001b[39m metaDescription \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'meta[name=\"description\"]'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 63 |\u001b[39m     \u001b[36mawait\u001b[39m expect(metaDescription)\u001b[33m.\u001b[39mtoHaveAttribute(\u001b[32m'content'\u001b[39m\u001b[33m,\u001b[39m \u001b[35m/privacy policy.*Ice Box Hockey/\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                   \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 64 |\u001b[39m     \n \u001b[90m 65 |\u001b[39m     \u001b[90m// Check title\u001b[39m\n \u001b[90m 66 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page)\u001b[33m.\u001b[39mtoHaveTitle(\u001b[35m/Privacy Policy.*Ice Box Hockey/\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts:63:35\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-02T00:28:44.818Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Development/Ice-Box-Hockey/verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-7b6a4-d-have-proper-SEO-meta-tags-Mobile-Chrome/test-failed-1.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Development/Ice-Box-Hockey/verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-7b6a4-d-have-proper-SEO-meta-tags-Mobile-Chrome/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts", "column": 35, "line": 63}}], "status": "unexpected"}], "id": "dc566187105446b158ed-b27e6527f9df04645dc3", "file": "privacy-policy.spec.ts", "line": 58, "column": 3}, {"title": "should be responsive on mobile", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 28, "parallelIndex": 2, "status": "failed", "duration": 2686, "error": {"message": "Error: expect.toBeVisible: Error: strict mode violation: locator('section').filter({ hasText: 'Contact Us' }) resolved to 2 elements:\n    1) <section>…</section> aka getByText('Your Privacy RightsYou have')\n    2) <section>…</section> aka getByText('Contact UsIf you have any')\n\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('section').filter({ hasText: 'Contact Us' })\u001b[22m\n", "stack": "Error: expect.toBeVisible: Error: strict mode violation: locator('section').filter({ hasText: 'Contact Us' }) resolved to 2 elements:\n    1) <section>…</section> aka getByText('Your Privacy RightsYou have')\n    2) <section>…</section> aka getByText('Contact UsIf you have any')\n\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('section').filter({ hasText: 'Contact Us' })\u001b[22m\n\n    at /Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts:80:34", "location": {"file": "/Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts", "column": 34, "line": 80}, "snippet": "\u001b[0m \u001b[90m 78 |\u001b[39m     \u001b[90m// Check if contact section is readable on mobile\u001b[39m\n \u001b[90m 79 |\u001b[39m     \u001b[36mconst\u001b[39m contactSection \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'section'\u001b[39m)\u001b[33m.\u001b[39mfilter({ hasText\u001b[33m:\u001b[39m \u001b[32m'Contact Us'\u001b[39m })\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 80 |\u001b[39m     \u001b[36mawait\u001b[39m expect(contactSection)\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 81 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 82 |\u001b[39m\n \u001b[90m 83 |\u001b[39m   test(\u001b[32m'should have working email link'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts", "column": 34, "line": 80}, "message": "Error: expect.toBeVisible: Error: strict mode violation: locator('section').filter({ hasText: 'Contact Us' }) resolved to 2 elements:\n    1) <section>…</section> aka getByText('Your Privacy RightsYou have')\n    2) <section>…</section> aka getByText('Contact UsIf you have any')\n\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('section').filter({ hasText: 'Contact Us' })\u001b[22m\n\n\n\u001b[0m \u001b[90m 78 |\u001b[39m     \u001b[90m// Check if contact section is readable on mobile\u001b[39m\n \u001b[90m 79 |\u001b[39m     \u001b[36mconst\u001b[39m contactSection \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'section'\u001b[39m)\u001b[33m.\u001b[39mfilter({ hasText\u001b[33m:\u001b[39m \u001b[32m'Contact Us'\u001b[39m })\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 80 |\u001b[39m     \u001b[36mawait\u001b[39m expect(contactSection)\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 81 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 82 |\u001b[39m\n \u001b[90m 83 |\u001b[39m   test(\u001b[32m'should have working email link'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\u001b[0m\n\u001b[2m    at /Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts:80:34\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-02T00:28:46.956Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Development/Ice-Box-Hockey/verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-f8298-uld-be-responsive-on-mobile-Mobile-Chrome/test-failed-1.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Development/Ice-Box-Hockey/verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-f8298-uld-be-responsive-on-mobile-Mobile-Chrome/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts", "column": 34, "line": 80}}], "status": "unexpected"}], "id": "dc566187105446b158ed-744e26dbf55202c7d422", "file": "privacy-policy.spec.ts", "line": 69, "column": 3}, {"title": "should have working email link", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 29, "parallelIndex": 0, "status": "failed", "duration": 1922, "error": {"message": "Error: expect.toHaveAttribute: Error: strict mode violation: locator('a[href=\"mailto:<EMAIL>\"]') resolved to 2 elements:\n    1) <a href=\"mailto:<EMAIL>\" class=\"text-blue-600 hover:text-blue-800\"><EMAIL></a> aka getByRole('main').getByRole('link', { name: '<EMAIL>' })\n    2) <a href=\"mailto:<EMAIL>\" class=\"ml-3 text-white/90 hover:text-blue-400 transition-colors\"><EMAIL></a> aka locator('#contact').getByRole('link', { name: '<EMAIL>' })\n\nCall log:\n\u001b[2m  - Expect \"toHaveAttribute\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('a[href=\"mailto:<EMAIL>\"]')\u001b[22m\n", "stack": "Error: expect.toHaveAttribute: Error: strict mode violation: locator('a[href=\"mailto:<EMAIL>\"]') resolved to 2 elements:\n    1) <a href=\"mailto:<EMAIL>\" class=\"text-blue-600 hover:text-blue-800\"><EMAIL></a> aka getByRole('main').getByRole('link', { name: '<EMAIL>' })\n    2) <a href=\"mailto:<EMAIL>\" class=\"ml-3 text-white/90 hover:text-blue-400 transition-colors\"><EMAIL></a> aka locator('#contact').getByRole('link', { name: '<EMAIL>' })\n\nCall log:\n\u001b[2m  - Expect \"toHaveAttribute\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('a[href=\"mailto:<EMAIL>\"]')\u001b[22m\n\n    at /Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts:89:29", "location": {"file": "/Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts", "column": 29, "line": 89}, "snippet": "\u001b[0m \u001b[90m 87 |\u001b[39m     \u001b[36mconst\u001b[39m emailLink \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'a[href=\"mailto:<EMAIL>\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 88 |\u001b[39m     \u001b[36mawait\u001b[39m expect(emailLink)\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 89 |\u001b[39m     \u001b[36mawait\u001b[39m expect(emailLink)\u001b[33m.\u001b[39mtoHaveAttribute(\u001b[32m'href'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'mailto:<EMAIL>'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                             \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 90 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 91 |\u001b[39m\n \u001b[90m 92 |\u001b[39m   test(\u001b[32m'should display last updated date'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts", "column": 29, "line": 89}, "message": "Error: expect.toHaveAttribute: Error: strict mode violation: locator('a[href=\"mailto:<EMAIL>\"]') resolved to 2 elements:\n    1) <a href=\"mailto:<EMAIL>\" class=\"text-blue-600 hover:text-blue-800\"><EMAIL></a> aka getByRole('main').getByRole('link', { name: '<EMAIL>' })\n    2) <a href=\"mailto:<EMAIL>\" class=\"ml-3 text-white/90 hover:text-blue-400 transition-colors\"><EMAIL></a> aka locator('#contact').getByRole('link', { name: '<EMAIL>' })\n\nCall log:\n\u001b[2m  - Expect \"toHaveAttribute\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('a[href=\"mailto:<EMAIL>\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 87 |\u001b[39m     \u001b[36mconst\u001b[39m emailLink \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'a[href=\"mailto:<EMAIL>\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 88 |\u001b[39m     \u001b[36mawait\u001b[39m expect(emailLink)\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 89 |\u001b[39m     \u001b[36mawait\u001b[39m expect(emailLink)\u001b[33m.\u001b[39mtoHaveAttribute(\u001b[32m'href'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'mailto:<EMAIL>'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                             \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 90 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 91 |\u001b[39m\n \u001b[90m 92 |\u001b[39m   test(\u001b[32m'should display last updated date'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\u001b[0m\n\u001b[2m    at /Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts:89:29\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-02T00:28:49.069Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Development/Ice-Box-Hockey/verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-c02f5-uld-have-working-email-link-Mobile-Chrome/test-failed-1.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Development/Ice-Box-Hockey/verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-c02f5-uld-have-working-email-link-Mobile-Chrome/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts", "column": 29, "line": 89}}], "status": "unexpected"}], "id": "dc566187105446b158ed-4dc015f0985bc3ec9d06", "file": "privacy-policy.spec.ts", "line": 83, "column": 3}, {"title": "should display last updated date", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 30, "parallelIndex": 3, "status": "passed", "duration": 3866, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-02T00:28:49.071Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "dc566187105446b158ed-0e423817377ca296475c", "file": "privacy-policy.spec.ts", "line": 92, "column": 3}, {"title": "should have proper heading hierarchy", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 25, "parallelIndex": 4, "status": "failed", "duration": 11795, "error": {"message": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveCount\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('h2')\nExpected: \u001b[32m8\u001b[39m\nReceived: \u001b[31m9\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveCount\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('h2')\u001b[22m\n\u001b[2m    13 × locator resolved to 9 elements\u001b[22m\n\u001b[2m       - unexpected value \"9\"\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveCount\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('h2')\nExpected: \u001b[32m8\u001b[39m\nReceived: \u001b[31m9\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveCount\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('h2')\u001b[22m\n\u001b[2m    13 × locator resolved to 9 elements\u001b[22m\n\u001b[2m       - unexpected value \"9\"\u001b[22m\n\n    at /Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts:115:30", "location": {"file": "/Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts", "column": 30, "line": 115}, "snippet": "\u001b[0m \u001b[90m 113 |\u001b[39m     \n \u001b[90m 114 |\u001b[39m     \u001b[90m// Should have multiple h2 sections\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 115 |\u001b[39m     \u001b[36mawait\u001b[39m expect(h2Elements)\u001b[33m.\u001b[39mtoHaveCount(\u001b[35m8\u001b[39m)\u001b[33m;\u001b[39m \u001b[90m// Based on our content structure\u001b[39m\n \u001b[90m     |\u001b[39m                              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 116 |\u001b[39m     \n \u001b[90m 117 |\u001b[39m     \u001b[90m// Should have h3 subsections\u001b[39m\n \u001b[90m 118 |\u001b[39m     \u001b[36mconst\u001b[39m h3Count \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m h3Elements\u001b[33m.\u001b[39mcount()\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts", "column": 30, "line": 115}, "message": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveCount\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('h2')\nExpected: \u001b[32m8\u001b[39m\nReceived: \u001b[31m9\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveCount\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('h2')\u001b[22m\n\u001b[2m    13 × locator resolved to 9 elements\u001b[22m\n\u001b[2m       - unexpected value \"9\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 113 |\u001b[39m     \n \u001b[90m 114 |\u001b[39m     \u001b[90m// Should have multiple h2 sections\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 115 |\u001b[39m     \u001b[36mawait\u001b[39m expect(h2Elements)\u001b[33m.\u001b[39mtoHaveCount(\u001b[35m8\u001b[39m)\u001b[33m;\u001b[39m \u001b[90m// Based on our content structure\u001b[39m\n \u001b[90m     |\u001b[39m                              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 116 |\u001b[39m     \n \u001b[90m 117 |\u001b[39m     \u001b[90m// Should have h3 subsections\u001b[39m\n \u001b[90m 118 |\u001b[39m     \u001b[36mconst\u001b[39m h3Count \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m h3Elements\u001b[33m.\u001b[39mcount()\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts:115:30\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-02T00:28:49.264Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Development/Ice-Box-Hockey/verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-10ffc-ve-proper-heading-hierarchy-Mobile-Chrome/test-failed-1.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Development/Ice-Box-Hockey/verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-10ffc-ve-proper-heading-hierarchy-Mobile-Chrome/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts", "column": 30, "line": 115}}], "status": "unexpected"}], "id": "dc566187105446b158ed-d9ef5b597a5e0be1a7de", "file": "privacy-policy.spec.ts", "line": 103, "column": 3}, {"title": "should load Privacy Policy page directly", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 31, "parallelIndex": 2, "status": "failed", "duration": 2326, "error": {"message": "Error: expect.toContainText: Error: strict mode violation: locator('h2') resolved to 9 elements:\n    1) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Information We Collect</h2> aka getByRole('heading', { name: 'Information We Collect' })\n    2) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">How We Use Your Information</h2> aka getByRole('heading', { name: 'How We Use Your Information' })\n    3) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Data Storage and Security</h2> aka getByRole('heading', { name: 'Data Storage and Security' })\n    4) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Third-Party Services</h2> aka getByRole('heading', { name: 'Third-Party Services' })\n    5) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Cookies and Tracking Technologies</h2> aka getByRole('heading', { name: 'Cookies and Tracking' })\n    6) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Your Privacy Rights</h2> aka getByRole('heading', { name: 'Your Privacy Rights' })\n    7) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Children's Privacy</h2> aka getByRole('heading', { name: 'Children\\'s Privacy' })\n    8) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Changes to This Privacy Policy</h2> aka getByRole('heading', { name: 'Changes to This Privacy Policy' })\n    9) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Contact Us</h2> aka getByRole('main').getByRole('heading', { name: 'Contact Us' })\n\nCall log:\n\u001b[2m  - Expect \"toContainText\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('h2')\u001b[22m\n", "stack": "Error: expect.toContainText: Error: strict mode violation: locator('h2') resolved to 9 elements:\n    1) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Information We Collect</h2> aka getByRole('heading', { name: 'Information We Collect' })\n    2) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">How We Use Your Information</h2> aka getByRole('heading', { name: 'How We Use Your Information' })\n    3) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Data Storage and Security</h2> aka getByRole('heading', { name: 'Data Storage and Security' })\n    4) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Third-Party Services</h2> aka getByRole('heading', { name: 'Third-Party Services' })\n    5) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Cookies and Tracking Technologies</h2> aka getByRole('heading', { name: 'Cookies and Tracking' })\n    6) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Your Privacy Rights</h2> aka getByRole('heading', { name: 'Your Privacy Rights' })\n    7) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Children's Privacy</h2> aka getByRole('heading', { name: 'Children\\'s Privacy' })\n    8) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Changes to This Privacy Policy</h2> aka getByRole('heading', { name: 'Changes to This Privacy Policy' })\n    9) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Contact Us</h2> aka getByRole('main').getByRole('heading', { name: 'Contact Us' })\n\nCall log:\n\u001b[2m  - Expect \"toContainText\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('h2')\u001b[22m\n\n    at /Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts:23:38", "location": {"file": "/Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts", "column": 38, "line": 23}, "snippet": "\u001b[0m \u001b[90m 21 |\u001b[39m     \n \u001b[90m 22 |\u001b[39m     \u001b[90m// Check for key sections\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 23 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'h2'\u001b[39m))\u001b[33m.\u001b[39mtoContainText(\u001b[32m'Information We Collect'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                      \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 24 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'h2'\u001b[39m))\u001b[33m.\u001b[39mtoContainText(\u001b[32m'How We Use Your Information'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 25 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'h2'\u001b[39m))\u001b[33m.\u001b[39mtoContainText(\u001b[32m'Data Storage and Security'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 26 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'h2'\u001b[39m))\u001b[33m.\u001b[39mtoContainText(\u001b[32m'Contact Us'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts", "column": 38, "line": 23}, "message": "Error: expect.toContainText: Error: strict mode violation: locator('h2') resolved to 9 elements:\n    1) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Information We Collect</h2> aka getByRole('heading', { name: 'Information We Collect' })\n    2) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">How We Use Your Information</h2> aka getByRole('heading', { name: 'How We Use Your Information' })\n    3) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Data Storage and Security</h2> aka getByRole('heading', { name: 'Data Storage and Security' })\n    4) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Third-Party Services</h2> aka getByRole('heading', { name: 'Third-Party Services' })\n    5) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Cookies and Tracking Technologies</h2> aka getByRole('heading', { name: 'Cookies and Tracking' })\n    6) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Your Privacy Rights</h2> aka getByRole('heading', { name: 'Your Privacy Rights' })\n    7) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Children's Privacy</h2> aka getByRole('heading', { name: 'Children\\'s Privacy' })\n    8) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Changes to This Privacy Policy</h2> aka getByRole('heading', { name: 'Changes to This Privacy Policy' })\n    9) <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">Contact Us</h2> aka getByRole('main').getByRole('heading', { name: 'Contact Us' })\n\nCall log:\n\u001b[2m  - Expect \"toContainText\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('h2')\u001b[22m\n\n\n\u001b[0m \u001b[90m 21 |\u001b[39m     \n \u001b[90m 22 |\u001b[39m     \u001b[90m// Check for key sections\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 23 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'h2'\u001b[39m))\u001b[33m.\u001b[39mtoContainText(\u001b[32m'Information We Collect'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                      \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 24 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'h2'\u001b[39m))\u001b[33m.\u001b[39mtoContainText(\u001b[32m'How We Use Your Information'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 25 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'h2'\u001b[39m))\u001b[33m.\u001b[39mtoContainText(\u001b[32m'Data Storage and Security'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 26 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'h2'\u001b[39m))\u001b[33m.\u001b[39mtoContainText(\u001b[32m'Contact Us'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts:23:38\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-02T00:28:52.433Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Development/Ice-Box-Hockey/verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-70c35-rivacy-Policy-page-directly-Mobile-Safari/test-failed-1.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Development/Ice-Box-Hockey/verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-70c35-rivacy-Policy-page-directly-Mobile-Safari/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts", "column": 38, "line": 23}}], "status": "unexpected"}], "id": "dc566187105446b158ed-83588b30fec7b2d64cc4", "file": "privacy-policy.spec.ts", "line": 13, "column": 3}, {"title": "should navigate to Privacy Policy from footer link", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 32, "parallelIndex": 0, "status": "passed", "duration": 2296, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-02T00:28:52.922Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "dc566187105446b158ed-ada36102d8a44b17327c", "file": "privacy-policy.spec.ts", "line": 29, "column": 3}, {"title": "should display contact information", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 33, "parallelIndex": 1, "status": "failed", "duration": 1926, "error": {"message": "Error: expect.toBeVisible: Error: strict mode violation: locator('section').filter({ hasText: 'Contact Us' }) resolved to 2 elements:\n    1) <section>…</section> aka getByText('Your Privacy RightsYou have')\n    2) <section>…</section> aka getByText('Contact UsIf you have any')\n\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('section').filter({ hasText: 'Contact Us' })\u001b[22m\n", "stack": "Error: expect.toBeVisible: Error: strict mode violation: locator('section').filter({ hasText: 'Contact Us' }) resolved to 2 elements:\n    1) <section>…</section> aka getByText('Your Privacy RightsYou have')\n    2) <section>…</section> aka getByText('Contact UsIf you have any')\n\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('section').filter({ hasText: 'Contact Us' })\u001b[22m\n\n    at /Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts:49:34", "location": {"file": "/Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts", "column": 34, "line": 49}, "snippet": "\u001b[0m \u001b[90m 47 |\u001b[39m     \u001b[90m// Check for contact section\u001b[39m\n \u001b[90m 48 |\u001b[39m     \u001b[36mconst\u001b[39m contactSection \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'section'\u001b[39m)\u001b[33m.\u001b[39mfilter({ hasText\u001b[33m:\u001b[39m \u001b[32m'Contact Us'\u001b[39m })\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 49 |\u001b[39m     \u001b[36mawait\u001b[39m expect(contactSection)\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 50 |\u001b[39m     \n \u001b[90m 51 |\u001b[39m     \u001b[90m// Check for business details\u001b[39m\n \u001b[90m 52 |\u001b[39m     \u001b[36mawait\u001b[39m expect(contactSection)\u001b[33m.\u001b[39mtoContainText(\u001b[32m'The Ice Box Hockey'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts", "column": 34, "line": 49}, "message": "Error: expect.toBeVisible: Error: strict mode violation: locator('section').filter({ hasText: 'Contact Us' }) resolved to 2 elements:\n    1) <section>…</section> aka getByText('Your Privacy RightsYou have')\n    2) <section>…</section> aka getByText('Contact UsIf you have any')\n\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('section').filter({ hasText: 'Contact Us' })\u001b[22m\n\n\n\u001b[0m \u001b[90m 47 |\u001b[39m     \u001b[90m// Check for contact section\u001b[39m\n \u001b[90m 48 |\u001b[39m     \u001b[36mconst\u001b[39m contactSection \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'section'\u001b[39m)\u001b[33m.\u001b[39mfilter({ hasText\u001b[33m:\u001b[39m \u001b[32m'Contact Us'\u001b[39m })\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 49 |\u001b[39m     \u001b[36mawait\u001b[39m expect(contactSection)\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 50 |\u001b[39m     \n \u001b[90m 51 |\u001b[39m     \u001b[90m// Check for business details\u001b[39m\n \u001b[90m 52 |\u001b[39m     \u001b[36mawait\u001b[39m expect(contactSection)\u001b[33m.\u001b[39mtoContainText(\u001b[32m'The Ice Box Hockey'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts:49:34\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-02T00:28:53.213Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Development/Ice-Box-Hockey/verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-464f2-display-contact-information-Mobile-Safari/test-failed-1.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Development/Ice-Box-Hockey/verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-464f2-display-contact-information-Mobile-Safari/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts", "column": 34, "line": 49}}], "status": "unexpected"}], "id": "dc566187105446b158ed-f55400c41137f0149703", "file": "privacy-policy.spec.ts", "line": 44, "column": 3}, {"title": "should have proper SEO meta tags", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 34, "parallelIndex": 3, "status": "failed", "duration": 2035, "error": {"message": "Error: expect.toHaveAttribute: Error: strict mode violation: locator('meta[name=\"description\"]') resolved to 2 elements:\n    1) <meta name=\"description\" content=\"Ice Box Hockey - Your premier destination for hockey equipment, gear, and accessories. Find top brands, expert advice, and everything you need for the game.\"/> aka locator('meta[name=\"description\"]').first()\n    2) <meta data-rh=\"true\" name=\"description\" content=\"Learn about how The Ice Box Hockey collects, uses, and protects your personal information. Read our comprehensive privacy policy.\"/> aka locator('meta[name=\"description\"]').nth(1)\n\nCall log:\n\u001b[2m  - Expect \"toHaveAttribute\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('meta[name=\"description\"]')\u001b[22m\n\u001b[2m    2 × locator resolved to <meta name=\"description\" content=\"Ice Box Hockey - Your premier destination for hockey equipment, gear, and accessories. Find top brands, expert advice, and everything you need for the game.\"/>\u001b[22m\n\u001b[2m      - unexpected value \"Ice Box Hockey - Your premier destination for hockey equipment, gear, and accessories. Find top brands, expert advice, and everything you need for the game.\"\u001b[22m\n", "stack": "Error: expect.toHaveAttribute: Error: strict mode violation: locator('meta[name=\"description\"]') resolved to 2 elements:\n    1) <meta name=\"description\" content=\"Ice Box Hockey - Your premier destination for hockey equipment, gear, and accessories. Find top brands, expert advice, and everything you need for the game.\"/> aka locator('meta[name=\"description\"]').first()\n    2) <meta data-rh=\"true\" name=\"description\" content=\"Learn about how The Ice Box Hockey collects, uses, and protects your personal information. Read our comprehensive privacy policy.\"/> aka locator('meta[name=\"description\"]').nth(1)\n\nCall log:\n\u001b[2m  - Expect \"toHaveAttribute\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('meta[name=\"description\"]')\u001b[22m\n\u001b[2m    2 × locator resolved to <meta name=\"description\" content=\"Ice Box Hockey - Your premier destination for hockey equipment, gear, and accessories. Find top brands, expert advice, and everything you need for the game.\"/>\u001b[22m\n\u001b[2m      - unexpected value \"Ice Box Hockey - Your premier destination for hockey equipment, gear, and accessories. Find top brands, expert advice, and everything you need for the game.\"\u001b[22m\n\n    at /Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts:63:35", "location": {"file": "/Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts", "column": 35, "line": 63}, "snippet": "\u001b[0m \u001b[90m 61 |\u001b[39m     \u001b[90m// Check meta description\u001b[39m\n \u001b[90m 62 |\u001b[39m     \u001b[36mconst\u001b[39m metaDescription \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'meta[name=\"description\"]'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 63 |\u001b[39m     \u001b[36mawait\u001b[39m expect(metaDescription)\u001b[33m.\u001b[39mtoHaveAttribute(\u001b[32m'content'\u001b[39m\u001b[33m,\u001b[39m \u001b[35m/privacy policy.*Ice Box Hockey/\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                   \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 64 |\u001b[39m     \n \u001b[90m 65 |\u001b[39m     \u001b[90m// Check title\u001b[39m\n \u001b[90m 66 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page)\u001b[33m.\u001b[39mtoHaveTitle(\u001b[35m/Privacy Policy.*Ice Box Hockey/\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts", "column": 35, "line": 63}, "message": "Error: expect.toHaveAttribute: Error: strict mode violation: locator('meta[name=\"description\"]') resolved to 2 elements:\n    1) <meta name=\"description\" content=\"Ice Box Hockey - Your premier destination for hockey equipment, gear, and accessories. Find top brands, expert advice, and everything you need for the game.\"/> aka locator('meta[name=\"description\"]').first()\n    2) <meta data-rh=\"true\" name=\"description\" content=\"Learn about how The Ice Box Hockey collects, uses, and protects your personal information. Read our comprehensive privacy policy.\"/> aka locator('meta[name=\"description\"]').nth(1)\n\nCall log:\n\u001b[2m  - Expect \"toHaveAttribute\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('meta[name=\"description\"]')\u001b[22m\n\u001b[2m    2 × locator resolved to <meta name=\"description\" content=\"Ice Box Hockey - Your premier destination for hockey equipment, gear, and accessories. Find top brands, expert advice, and everything you need for the game.\"/>\u001b[22m\n\u001b[2m      - unexpected value \"Ice Box Hockey - Your premier destination for hockey equipment, gear, and accessories. Find top brands, expert advice, and everything you need for the game.\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 61 |\u001b[39m     \u001b[90m// Check meta description\u001b[39m\n \u001b[90m 62 |\u001b[39m     \u001b[36mconst\u001b[39m metaDescription \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'meta[name=\"description\"]'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 63 |\u001b[39m     \u001b[36mawait\u001b[39m expect(metaDescription)\u001b[33m.\u001b[39mtoHaveAttribute(\u001b[32m'content'\u001b[39m\u001b[33m,\u001b[39m \u001b[35m/privacy policy.*Ice Box Hockey/\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                   \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 64 |\u001b[39m     \n \u001b[90m 65 |\u001b[39m     \u001b[90m// Check title\u001b[39m\n \u001b[90m 66 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page)\u001b[33m.\u001b[39mtoHaveTitle(\u001b[35m/Privacy Policy.*Ice Box Hockey/\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts:63:35\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-02T00:28:56.330Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Development/Ice-Box-Hockey/verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-7b6a4-d-have-proper-SEO-meta-tags-Mobile-Safari/test-failed-1.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Development/Ice-Box-Hockey/verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-7b6a4-d-have-proper-SEO-meta-tags-Mobile-Safari/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts", "column": 35, "line": 63}}], "status": "unexpected"}], "id": "dc566187105446b158ed-38f826c16d6d0a2f8f88", "file": "privacy-policy.spec.ts", "line": 58, "column": 3}, {"title": "should be responsive on mobile", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 35, "parallelIndex": 2, "status": "failed", "duration": 2112, "error": {"message": "Error: expect.toBeVisible: Error: strict mode violation: locator('section').filter({ hasText: 'Contact Us' }) resolved to 2 elements:\n    1) <section>…</section> aka getByText('Your Privacy RightsYou have')\n    2) <section>…</section> aka getByText('Contact UsIf you have any')\n\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('section').filter({ hasText: 'Contact Us' })\u001b[22m\n", "stack": "Error: expect.toBeVisible: Error: strict mode violation: locator('section').filter({ hasText: 'Contact Us' }) resolved to 2 elements:\n    1) <section>…</section> aka getByText('Your Privacy RightsYou have')\n    2) <section>…</section> aka getByText('Contact UsIf you have any')\n\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('section').filter({ hasText: 'Contact Us' })\u001b[22m\n\n    at /Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts:80:34", "location": {"file": "/Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts", "column": 34, "line": 80}, "snippet": "\u001b[0m \u001b[90m 78 |\u001b[39m     \u001b[90m// Check if contact section is readable on mobile\u001b[39m\n \u001b[90m 79 |\u001b[39m     \u001b[36mconst\u001b[39m contactSection \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'section'\u001b[39m)\u001b[33m.\u001b[39mfilter({ hasText\u001b[33m:\u001b[39m \u001b[32m'Contact Us'\u001b[39m })\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 80 |\u001b[39m     \u001b[36mawait\u001b[39m expect(contactSection)\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 81 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 82 |\u001b[39m\n \u001b[90m 83 |\u001b[39m   test(\u001b[32m'should have working email link'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts", "column": 34, "line": 80}, "message": "Error: expect.toBeVisible: Error: strict mode violation: locator('section').filter({ hasText: 'Contact Us' }) resolved to 2 elements:\n    1) <section>…</section> aka getByText('Your Privacy RightsYou have')\n    2) <section>…</section> aka getByText('Contact UsIf you have any')\n\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('section').filter({ hasText: 'Contact Us' })\u001b[22m\n\n\n\u001b[0m \u001b[90m 78 |\u001b[39m     \u001b[90m// Check if contact section is readable on mobile\u001b[39m\n \u001b[90m 79 |\u001b[39m     \u001b[36mconst\u001b[39m contactSection \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'section'\u001b[39m)\u001b[33m.\u001b[39mfilter({ hasText\u001b[33m:\u001b[39m \u001b[32m'Contact Us'\u001b[39m })\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 80 |\u001b[39m     \u001b[36mawait\u001b[39m expect(contactSection)\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 81 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 82 |\u001b[39m\n \u001b[90m 83 |\u001b[39m   test(\u001b[32m'should have working email link'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\u001b[0m\n\u001b[2m    at /Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts:80:34\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-02T00:28:56.759Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Development/Ice-Box-Hockey/verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-f8298-uld-be-responsive-on-mobile-Mobile-Safari/test-failed-1.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Development/Ice-Box-Hockey/verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-f8298-uld-be-responsive-on-mobile-Mobile-Safari/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts", "column": 34, "line": 80}}], "status": "unexpected"}], "id": "dc566187105446b158ed-595c6c8a4ef64a745af7", "file": "privacy-policy.spec.ts", "line": 69, "column": 3}, {"title": "should have working email link", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 32, "parallelIndex": 0, "status": "passed", "duration": 1123, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-02T00:28:55.675Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "dc566187105446b158ed-5629ab5abd3c67ee237a", "file": "privacy-policy.spec.ts", "line": 83, "column": 3}, {"title": "should display last updated date", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 36, "parallelIndex": 1, "status": "passed", "duration": 1664, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-02T00:28:57.776Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "dc566187105446b158ed-0ecb27868ca7c1f4f340", "file": "privacy-policy.spec.ts", "line": 92, "column": 3}, {"title": "should have proper heading hierarchy", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 32, "parallelIndex": 0, "status": "failed", "duration": 11271, "error": {"message": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveCount\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('h2')\nExpected: \u001b[32m8\u001b[39m\nReceived: \u001b[31m9\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveCount\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('h2')\u001b[22m\n\u001b[2m    14 × locator resolved to 9 elements\u001b[22m\n\u001b[2m       - unexpected value \"9\"\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveCount\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('h2')\nExpected: \u001b[32m8\u001b[39m\nReceived: \u001b[31m9\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveCount\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('h2')\u001b[22m\n\u001b[2m    14 × locator resolved to 9 elements\u001b[22m\n\u001b[2m       - unexpected value \"9\"\u001b[22m\n\n    at /Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts:115:30", "location": {"file": "/Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts", "column": 30, "line": 115}, "snippet": "\u001b[0m \u001b[90m 113 |\u001b[39m     \n \u001b[90m 114 |\u001b[39m     \u001b[90m// Should have multiple h2 sections\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 115 |\u001b[39m     \u001b[36mawait\u001b[39m expect(h2Elements)\u001b[33m.\u001b[39mtoHaveCount(\u001b[35m8\u001b[39m)\u001b[33m;\u001b[39m \u001b[90m// Based on our content structure\u001b[39m\n \u001b[90m     |\u001b[39m                              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 116 |\u001b[39m     \n \u001b[90m 117 |\u001b[39m     \u001b[90m// Should have h3 subsections\u001b[39m\n \u001b[90m 118 |\u001b[39m     \u001b[36mconst\u001b[39m h3Count \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m h3Elements\u001b[33m.\u001b[39mcount()\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts", "column": 30, "line": 115}, "message": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveCount\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('h2')\nExpected: \u001b[32m8\u001b[39m\nReceived: \u001b[31m9\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveCount\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('h2')\u001b[22m\n\u001b[2m    14 × locator resolved to 9 elements\u001b[22m\n\u001b[2m       - unexpected value \"9\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 113 |\u001b[39m     \n \u001b[90m 114 |\u001b[39m     \u001b[90m// Should have multiple h2 sections\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 115 |\u001b[39m     \u001b[36mawait\u001b[39m expect(h2Elements)\u001b[33m.\u001b[39mtoHaveCount(\u001b[35m8\u001b[39m)\u001b[33m;\u001b[39m \u001b[90m// Based on our content structure\u001b[39m\n \u001b[90m     |\u001b[39m                              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 116 |\u001b[39m     \n \u001b[90m 117 |\u001b[39m     \u001b[90m// Should have h3 subsections\u001b[39m\n \u001b[90m 118 |\u001b[39m     \u001b[36mconst\u001b[39m h3Count \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m h3Elements\u001b[33m.\u001b[39mcount()\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts:115:30\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-02T00:28:56.811Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Development/Ice-Box-Hockey/verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-10ffc-ve-proper-heading-hierarchy-Mobile-Safari/test-failed-1.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Development/Ice-Box-Hockey/verification-reports/playwright-artifacts/privacy-policy-Privacy-Pol-10ffc-ve-proper-heading-hierarchy-Mobile-Safari/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Development/Ice-Box-Hockey/tests/privacy-policy.spec.ts", "column": 30, "line": 115}}], "status": "unexpected"}], "id": "dc566187105446b158ed-b692c2748edd88cf135a", "file": "privacy-policy.spec.ts", "line": 103, "column": 3}]}]}], "errors": [], "stats": {"startTime": "2025-08-02T00:27:58.961Z", "duration": 69196.421, "expected": 9, "skipped": 0, "unexpected": 31, "flaky": 0}}