<?xml version="1.0" encoding="UTF-8"?>
<testsuites name="jest tests" tests="14" failures="0" errors="0" time="3.795">
  <testsuite name="ProductCard" errors="0" failures="0" skipped="0" timestamp="2025-08-01T20:31:29" time="2.532" tests="6">
    <testcase classname="ProductCard" name="renders product information correctly" time="0.043">
    </testcase>
    <testcase classname="ProductCard" name="does not render description when not provided" time="0.006">
    </testcase>
    <testcase classname="ProductCard" name="applies correct CSS classes" time="0.002">
    </testcase>
    <testcase classname="ProductCard" name="has proper accessibility attributes" time="0.003">
    </testcase>
    <testcase classname="ProductCard" name="handles missing optional properties gracefully" time="0.004">
    </testcase>
    <testcase classname="ProductCard" name="has hover effects applied" time="0.002">
    </testcase>
  </testsuite>
  <testsuite name="CategoryCard" errors="0" failures="0" skipped="0" timestamp="2025-08-01T20:31:29" time="3.003" tests="8">
    <testcase classname="CategoryCard" name="renders category information correctly" time="0.077">
    </testcase>
    <testcase classname="CategoryCard" name="renders the icon correctly" time="0.012">
    </testcase>
    <testcase classname="CategoryCard" name="applies correct CSS classes" time="0.009">
    </testcase>
    <testcase classname="CategoryCard" name="renders items list with correct structure" time="0.35">
    </testcase>
    <testcase classname="CategoryCard" name="handles empty items array gracefully" time="0.005">
    </testcase>
    <testcase classname="CategoryCard" name="handles single item correctly" time="0.014">
    </testcase>
    <testcase classname="CategoryCard" name="has hover effects applied" time="0.006">
    </testcase>
    <testcase classname="CategoryCard" name="has proper semantic structure" time="0.015">
    </testcase>
  </testsuite>
</testsuites>