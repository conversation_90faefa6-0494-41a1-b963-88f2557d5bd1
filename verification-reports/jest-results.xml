<?xml version="1.0" encoding="UTF-8"?>
<testsuites name="jest tests" tests="375" failures="64" errors="0" time="235.334">
  <testsuite name="BuildVerificationEngine" errors="0" failures="0" skipped="0" timestamp="2025-08-01T19:02:05" time="0.779" tests="15">
    <testcase classname="BuildVerificationEngine › verify" name="should return successful result when build succeeds" time="0.009">
    </testcase>
    <testcase classname="BuildVerificationEngine › verify" name="should return failed result when build fails" time="0.001">
    </testcase>
    <testcase classname="BuildVerificationEngine › verify" name="should handle build process errors" time="0.002">
    </testcase>
    <testcase classname="BuildVerificationEngine › verify" name="should clean build directory before building" time="0.001">
    </testcase>
    <testcase classname="BuildVerificationEngine › error parsing" name="should parse TypeScript errors correctly" time="0.001">
    </testcase>
    <testcase classname="BuildVerificationEngine › error parsing" name="should parse TypeScript warnings correctly" time="0.001">
    </testcase>
    <testcase classname="BuildVerificationEngine › error parsing" name="should parse Vite bundling errors correctly" time="0.001">
    </testcase>
    <testcase classname="BuildVerificationEngine › error parsing" name="should parse Vite warnings correctly" time="0">
    </testcase>
    <testcase classname="BuildVerificationEngine › build metrics collection" name="should collect chunk information correctly" time="0.001">
    </testcase>
    <testcase classname="BuildVerificationEngine › build metrics collection" name="should handle metrics collection errors gracefully" time="0.001">
    </testcase>
    <testcase classname="BuildVerificationEngine › utility methods" name="should extract chunk names correctly" time="0.002">
    </testcase>
    <testcase classname="BuildVerificationEngine › utility methods" name="should provide detailed error information" time="0.001">
    </testcase>
    <testcase classname="BuildVerificationEngine › utility methods" name="should check size thresholds correctly" time="0">
    </testcase>
    <testcase classname="BuildVerificationEngine › utility methods" name="should generate build summary correctly" time="0">
    </testcase>
    <testcase classname="BuildVerificationEngine › factory function" name="should create build verification engine instance" time="0">
    </testcase>
  </testsuite>
  <testsuite name="Reporting Integration Tests" errors="0" failures="4" skipped="0" timestamp="2025-08-01T19:02:05" time="1.034" tests="11">
    <testcase classname="Reporting Integration Tests › End-to-End Report Generation" name="should generate complete reports with real file system operations" time="0.082">
      <failure>Error: expect(received).resolves.toBeUndefined()

Matcher error: received value must be a promise

Received has value: undefined
    at Object.toBeUndefined (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/expect/build/index.js:162:13)
    at Object.toBeUndefined (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/__tests__/reporting.integration.test.ts:224:57)</failure>
    </testcase>
    <testcase classname="Reporting Integration Tests › End-to-End Report Generation" name="should handle complex deployment decision logic" time="0.016">
    </testcase>
    <testcase classname="Reporting Integration Tests › End-to-End Report Generation" name="should generate accurate summary statistics" time="0.011">
    </testcase>
    <testcase classname="Reporting Integration Tests › End-to-End Report Generation" name="should create detailed sections for all verification areas" time="0.008">
    </testcase>
    <testcase classname="Reporting Integration Tests › End-to-End Report Generation" name="should generate responsive HTML with interactive features" time="0.008">
    </testcase>
    <testcase classname="Reporting Integration Tests › End-to-End Report Generation" name="should handle custom themes correctly" time="0.011">
    </testcase>
    <testcase classname="Reporting Integration Tests › End-to-End Report Generation" name="should preserve all original data in JSON report" time="0.007">
    </testcase>
    <testcase classname="Reporting Integration Tests › Factory Function Integration" name="should work with generateVerificationReport utility" time="0.005">
      <failure>Error: expect(received).resolves.toBeUndefined()

Matcher error: received value must be a promise

Received has value: undefined
    at Object.toBeUndefined (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/expect/build/index.js:162:13)
    at Object.toBeUndefined (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/__tests__/reporting.integration.test.ts:409:57)</failure>
    </testcase>
    <testcase classname="Reporting Integration Tests › Error Handling" name="should handle file system errors gracefully" time="0.027">
      <failure>Error: expect(received).resolves.toBeUndefined()

Matcher error: received value must be a promise

Received has value: undefined
    at Object.toBeUndefined (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/expect/build/index.js:162:13)
    at Object.toBeUndefined (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/__tests__/reporting.integration.test.ts:436:57)</failure>
    </testcase>
    <testcase classname="Reporting Integration Tests › Error Handling" name="should handle malformed report data" time="0.007">
    </testcase>
    <testcase classname="Reporting Integration Tests › Performance and Scalability" name="should handle large reports efficiently" time="0.015">
      <failure>Error: expect(received).resolves.toBeUndefined()

Matcher error: received value must be a promise

Received has value: undefined
    at Object.toBeUndefined (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/expect/build/index.js:162:13)
    at Object.toBeUndefined (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/__tests__/reporting.integration.test.ts:503:57)</failure>
    </testcase>
  </testsuite>
  <testsuite name="PWAValidator" errors="0" failures="20" skipped="0" timestamp="2025-08-01T19:02:05" time="1.408" tests="25">
    <testcase classname="PWAValidator › Service Worker Registration Testing" name="should detect successful service worker registration" time="0.014">
      <failure>browserType.launch: setImmediate is not defined
    at PWAValidator.launch (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/pwa.ts:42:39)
    at PWAValidator.initialize (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/pwa.ts:74:20)
    at Object.validate (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/__tests__/pwa.test.ts:45:38)
    at Promise.then.completed (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/utils.js:298:28)
    at callAsyncCircusFn (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusTest (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:316:40)
    at _runTest (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:252:3)
    at _runTestsForDescribeBlock (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-runner/build/runTest.js:444:34)
    at Object.worker (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-runner/build/testWorker.js:106:12)</failure>
    </testcase>
    <testcase classname="PWAValidator › Service Worker Registration Testing" name="should detect failed service worker registration" time="0">
      <failure>browserType.launch: setImmediate is not defined
    at PWAValidator.launch (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/pwa.ts:42:39)
    at PWAValidator.initialize (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/pwa.ts:74:20)
    at Object.validate (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/__tests__/pwa.test.ts:62:38)
    at Promise.then.completed (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/utils.js:298:28)
    at callAsyncCircusFn (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusTest (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:316:40)
    at _runTest (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:252:3)
    at _runTestsForDescribeBlock (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-runner/build/runTest.js:444:34)
    at Object.worker (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-runner/build/testWorker.js:106:12)</failure>
    </testcase>
    <testcase classname="PWAValidator › Service Worker Registration Testing" name="should handle service worker registration errors" time="0.001">
      <failure>browserType.launch: setImmediate is not defined
    at PWAValidator.launch (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/pwa.ts:42:39)
    at PWAValidator.initialize (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/pwa.ts:74:20)
    at Object.validate (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/__tests__/pwa.test.ts:79:38)
    at Promise.then.completed (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/utils.js:298:28)
    at callAsyncCircusFn (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusTest (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:316:40)
    at _runTest (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:252:3)
    at _runTestsForDescribeBlock (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-runner/build/runTest.js:444:34)
    at Object.worker (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-runner/build/testWorker.js:106:12)</failure>
    </testcase>
    <testcase classname="PWAValidator › PWA Manifest Validation" name="should validate a correct PWA manifest" time="0">
      <failure>browserType.launch: setImmediate is not defined
    at PWAValidator.launch (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/pwa.ts:42:39)
    at PWAValidator.initialize (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/pwa.ts:74:20)
    at Object.validate (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/__tests__/pwa.test.ts:98:38)
    at Promise.then.completed (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/utils.js:298:28)
    at callAsyncCircusFn (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusTest (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:316:40)
    at _runTest (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:252:3)
    at _runTestsForDescribeBlock (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-runner/build/runTest.js:444:34)
    at Object.worker (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-runner/build/testWorker.js:106:12)</failure>
    </testcase>
    <testcase classname="PWAValidator › PWA Manifest Validation" name="should detect invalid PWA manifest" time="0">
      <failure>browserType.launch: setImmediate is not defined
    at PWAValidator.launch (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/pwa.ts:42:39)
    at PWAValidator.initialize (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/pwa.ts:74:20)
    at Object.validate (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/__tests__/pwa.test.ts:115:38)
    at Promise.then.completed (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/utils.js:298:28)
    at callAsyncCircusFn (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusTest (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:316:40)
    at _runTest (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:252:3)
    at _runTestsForDescribeBlock (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-runner/build/runTest.js:444:34)
    at Object.worker (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-runner/build/testWorker.js:106:12)</failure>
    </testcase>
    <testcase classname="PWAValidator › PWA Manifest Validation" name="should handle missing manifest file" time="0.001">
      <failure>browserType.launch: setImmediate is not defined
    at PWAValidator.launch (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/pwa.ts:42:39)
    at PWAValidator.initialize (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/pwa.ts:74:20)
    at Object.validate (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/__tests__/pwa.test.ts:132:38)
    at Promise.then.completed (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/utils.js:298:28)
    at callAsyncCircusFn (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusTest (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:316:40)
    at _runTest (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:252:3)
    at _runTestsForDescribeBlock (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-runner/build/runTest.js:444:34)
    at Object.worker (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-runner/build/testWorker.js:106:12)</failure>
    </testcase>
    <testcase classname="PWAValidator › PWA Manifest Validation" name="should handle manifest fetch errors" time="0">
      <failure>browserType.launch: setImmediate is not defined
    at PWAValidator.launch (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/pwa.ts:42:39)
    at PWAValidator.initialize (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/pwa.ts:74:20)
    at Object.validate (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/__tests__/pwa.test.ts:149:38)
    at Promise.then.completed (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/utils.js:298:28)
    at callAsyncCircusFn (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusTest (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:316:40)
    at _runTest (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:252:3)
    at _runTestsForDescribeBlock (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-runner/build/runTest.js:444:34)
    at Object.worker (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-runner/build/testWorker.js:106:12)</failure>
    </testcase>
    <testcase classname="PWAValidator › Offline Functionality Testing" name="should validate offline functionality for all pages" time="0">
      <failure>browserType.launch: setImmediate is not defined
    at PWAValidator.launch (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/pwa.ts:42:39)
    at PWAValidator.initialize (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/pwa.ts:74:20)
    at Object.validate (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/__tests__/pwa.test.ts:168:38)
    at Promise.then.completed (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/utils.js:298:28)
    at callAsyncCircusFn (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusTest (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:316:40)
    at _runTest (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:252:3)
    at _runTestsForDescribeBlock (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-runner/build/runTest.js:444:34)
    at Object.worker (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-runner/build/testWorker.js:106:12)</failure>
    </testcase>
    <testcase classname="PWAValidator › Offline Functionality Testing" name="should detect offline functionality failures" time="0.001">
      <failure>browserType.launch: setImmediate is not defined
    at PWAValidator.launch (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/pwa.ts:42:39)
    at PWAValidator.initialize (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/pwa.ts:74:20)
    at Object.validate (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/__tests__/pwa.test.ts:185:38)
    at Promise.then.completed (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/utils.js:298:28)
    at callAsyncCircusFn (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusTest (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:316:40)
    at _runTest (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:252:3)
    at _runTestsForDescribeBlock (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-runner/build/runTest.js:444:34)
    at Object.worker (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-runner/build/testWorker.js:106:12)</failure>
    </testcase>
    <testcase classname="PWAValidator › Offline Functionality Testing" name="should handle offline testing errors" time="0">
      <failure>browserType.launch: setImmediate is not defined
    at PWAValidator.launch (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/pwa.ts:42:39)
    at PWAValidator.initialize (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/pwa.ts:74:20)
    at Object.validate (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/__tests__/pwa.test.ts:202:38)
    at Promise.then.completed (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/utils.js:298:28)
    at callAsyncCircusFn (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusTest (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:316:40)
    at _runTest (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:252:3)
    at _runTestsForDescribeBlock (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-runner/build/runTest.js:444:34)
    at Object.worker (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-runner/build/testWorker.js:106:12)</failure>
    </testcase>
    <testcase classname="PWAValidator › PWA Installability Testing" name="should detect installable PWA" time="0">
      <failure>browserType.launch: setImmediate is not defined
    at PWAValidator.launch (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/pwa.ts:42:39)
    at PWAValidator.initialize (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/pwa.ts:74:20)
    at Object.validate (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/__tests__/pwa.test.ts:221:38)
    at Promise.then.completed (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/utils.js:298:28)
    at callAsyncCircusFn (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusTest (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:316:40)
    at _runTest (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:252:3)
    at _runTestsForDescribeBlock (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-runner/build/runTest.js:444:34)
    at Object.worker (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-runner/build/testWorker.js:106:12)</failure>
    </testcase>
    <testcase classname="PWAValidator › PWA Installability Testing" name="should detect non-installable PWA" time="0.001">
      <failure>browserType.launch: setImmediate is not defined
    at PWAValidator.launch (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/pwa.ts:42:39)
    at PWAValidator.initialize (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/pwa.ts:74:20)
    at Object.validate (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/__tests__/pwa.test.ts:238:38)
    at Promise.then.completed (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/utils.js:298:28)
    at callAsyncCircusFn (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusTest (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:316:40)
    at _runTest (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:252:3)
    at _runTestsForDescribeBlock (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-runner/build/runTest.js:444:34)
    at Object.worker (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-runner/build/testWorker.js:106:12)</failure>
    </testcase>
    <testcase classname="PWAValidator › PWA Installability Testing" name="should handle installability testing errors" time="0">
      <failure>browserType.launch: setImmediate is not defined
    at PWAValidator.launch (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/pwa.ts:42:39)
    at PWAValidator.initialize (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/pwa.ts:74:20)
    at Object.validate (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/__tests__/pwa.test.ts:255:38)
    at Promise.then.completed (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/utils.js:298:28)
    at callAsyncCircusFn (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusTest (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:316:40)
    at _runTest (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:252:3)
    at _runTestsForDescribeBlock (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-runner/build/runTest.js:444:34)
    at Object.worker (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-runner/build/testWorker.js:106:12)</failure>
    </testcase>
    <testcase classname="PWAValidator › Cache Strategy Validation" name="should validate proper cache strategy" time="0">
      <failure>browserType.launch: setImmediate is not defined
    at PWAValidator.launch (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/pwa.ts:42:39)
    at PWAValidator.initialize (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/pwa.ts:74:20)
    at Object.validate (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/__tests__/pwa.test.ts:276:38)
    at Promise.then.completed (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/utils.js:298:28)
    at callAsyncCircusFn (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusTest (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:316:40)
    at _runTest (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:252:3)
    at _runTestsForDescribeBlock (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-runner/build/runTest.js:444:34)
    at Object.worker (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-runner/build/testWorker.js:106:12)</failure>
    </testcase>
    <testcase classname="PWAValidator › Cache Strategy Validation" name="should detect missing cache strategy" time="0.001">
      <failure>browserType.launch: setImmediate is not defined
    at PWAValidator.launch (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/pwa.ts:42:39)
    at PWAValidator.initialize (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/pwa.ts:74:20)
    at Object.validate (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/__tests__/pwa.test.ts:295:38)
    at Promise.then.completed (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/utils.js:298:28)
    at callAsyncCircusFn (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusTest (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:316:40)
    at _runTest (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:252:3)
    at _runTestsForDescribeBlock (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-runner/build/runTest.js:444:34)
    at Object.worker (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-runner/build/testWorker.js:106:12)</failure>
    </testcase>
    <testcase classname="PWAValidator › Cache Strategy Validation" name="should handle cache validation errors" time="0.003">
      <failure>browserType.launch: setImmediate is not defined
    at PWAValidator.launch (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/pwa.ts:42:39)
    at PWAValidator.initialize (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/pwa.ts:74:20)
    at Object.validate (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/__tests__/pwa.test.ts:309:38)
    at Promise.then.completed (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/utils.js:298:28)
    at callAsyncCircusFn (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusTest (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:316:40)
    at _runTest (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:252:3)
    at _runTestsForDescribeBlock (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-runner/build/runTest.js:444:34)
    at Object.worker (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-runner/build/testWorker.js:106:12)</failure>
    </testcase>
    <testcase classname="PWATestSuite" name="should execute PWA validation and return test results" time="0.002">
      <failure>Error: expect(received).toBeGreaterThan(expected)

Expected: &gt; 0
Received:   0
    at Object.toBeGreaterThan (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/__tests__/pwa.test.ts:348:30)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</failure>
    </testcase>
    <testcase classname="PWATestSuite" name="should report failures for failed PWA validation" time="0.002">
    </testcase>
    <testcase classname="PWATestSuite" name="should handle validation errors gracefully" time="0.006">
    </testcase>
    <testcase classname="PWATestSuite" name="should have correct test suite properties" time="0.001">
    </testcase>
    <testcase classname="validatePWA standalone function" name="should use default configuration when no config provided" time="0.001">
    </testcase>
    <testcase classname="validatePWA standalone function" name="should merge custom configuration with defaults" time="0">
    </testcase>
    <testcase classname="Manifest Content Validation" name="should validate manifest with all required fields" time="0.002">
      <failure>browserType.launch: setImmediate is not defined
    at PWAValidator.launch (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/pwa.ts:42:39)
    at PWAValidator.initialize (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/pwa.ts:74:20)
    at PWAValidator.&lt;anonymous&gt; (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-mock/build/index.js:794:25)
    at /Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-mock/build/index.js:397:39
    at PWAValidator.&lt;anonymous&gt; (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-mock/build/index.js:404:13)
    at PWAValidator.mockConstructor (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-mock/build/index.js:148:19)
    at Object.validate (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/__tests__/pwa.test.ts:487:36)
    at Promise.then.completed (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/utils.js:298:28)
    at callAsyncCircusFn (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusTest (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:316:40)
    at _runTest (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:252:3)
    at _runTestsForDescribeBlock (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-runner/build/runTest.js:444:34)
    at Object.worker (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-runner/build/testWorker.js:106:12)</failure>
    </testcase>
    <testcase classname="Manifest Content Validation" name="should detect missing required fields" time="0.001">
      <failure>browserType.launch: setImmediate is not defined
    at PWAValidator.launch (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/pwa.ts:42:39)
    at PWAValidator.initialize (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/pwa.ts:74:20)
    at PWAValidator.&lt;anonymous&gt; (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-mock/build/index.js:794:25)
    at /Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-mock/build/index.js:397:39
    at PWAValidator.&lt;anonymous&gt; (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-mock/build/index.js:404:13)
    at PWAValidator.mockConstructor (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-mock/build/index.js:148:19)
    at Object.validate (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/__tests__/pwa.test.ts:501:36)
    at Promise.then.completed (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/utils.js:298:28)
    at callAsyncCircusFn (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusTest (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:316:40)
    at _runTest (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:252:3)
    at _runTestsForDescribeBlock (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-runner/build/runTest.js:444:34)
    at Object.worker (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-runner/build/testWorker.js:106:12)</failure>
    </testcase>
    <testcase classname="Manifest Content Validation" name="should validate icon requirements" time="0.001">
      <failure>browserType.launch: setImmediate is not defined
    at PWAValidator.launch (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/pwa.ts:42:39)
    at PWAValidator.initialize (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/pwa.ts:74:20)
    at PWAValidator.&lt;anonymous&gt; (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-mock/build/index.js:794:25)
    at /Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-mock/build/index.js:397:39
    at PWAValidator.&lt;anonymous&gt; (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-mock/build/index.js:404:13)
    at PWAValidator.mockConstructor (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-mock/build/index.js:148:19)
    at Object.validate (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/__tests__/pwa.test.ts:524:36)
    at Promise.then.completed (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/utils.js:298:28)
    at callAsyncCircusFn (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusTest (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:316:40)
    at _runTest (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:252:3)
    at _runTestsForDescribeBlock (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-runner/build/runTest.js:444:34)
    at Object.worker (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-runner/build/testWorker.js:106:12)</failure>
    </testcase>
  </testsuite>
  <testsuite name="Type Definitions and Interface Contracts" errors="0" failures="0" skipped="0" timestamp="2025-08-01T19:02:06" time="0.106" tests="31">
    <testcase classname="Type Definitions and Interface Contracts › Build Types" name="should create valid BuildError objects" time="0.001">
    </testcase>
    <testcase classname="Type Definitions and Interface Contracts › Build Types" name="should create valid BuildWarning objects" time="0.001">
    </testcase>
    <testcase classname="Type Definitions and Interface Contracts › Build Types" name="should create valid ChunkInfo objects" time="0.004">
    </testcase>
    <testcase classname="Type Definitions and Interface Contracts › Build Types" name="should create valid BuildVerificationResult objects" time="0.001">
    </testcase>
    <testcase classname="Type Definitions and Interface Contracts › Test Types" name="should create valid TestFailure objects" time="0.001">
    </testcase>
    <testcase classname="Type Definitions and Interface Contracts › Test Types" name="should create valid CoverageReport objects" time="0">
    </testcase>
    <testcase classname="Type Definitions and Interface Contracts › Test Types" name="should create valid TestResult objects" time="0.001">
    </testcase>
    <testcase classname="Type Definitions and Interface Contracts › Test Types" name="should create valid TestSuite objects" time="0">
    </testcase>
    <testcase classname="Type Definitions and Interface Contracts › Performance Types" name="should create valid LighthouseScore objects" time="0">
    </testcase>
    <testcase classname="Type Definitions and Interface Contracts › Performance Types" name="should create valid PerformanceMetrics objects" time="0">
    </testcase>
    <testcase classname="Type Definitions and Interface Contracts › Accessibility Types" name="should create valid A11yViolation objects" time="0">
    </testcase>
    <testcase classname="Type Definitions and Interface Contracts › Accessibility Types" name="should create valid A11yWarning objects" time="0.001">
    </testcase>
    <testcase classname="Type Definitions and Interface Contracts › Accessibility Types" name="should create valid AccessibilityResult objects" time="0">
    </testcase>
    <testcase classname="Type Definitions and Interface Contracts › PWA Types" name="should create valid CacheValidation objects" time="0">
    </testcase>
    <testcase classname="Type Definitions and Interface Contracts › PWA Types" name="should create valid PWAValidationResult objects" time="0.001">
    </testcase>
    <testcase classname="Type Definitions and Interface Contracts › External Dependencies Types" name="should create valid DependencyStatus objects" time="0">
    </testcase>
    <testcase classname="Type Definitions and Interface Contracts › External Dependencies Types" name="should create valid ExternalDependencies objects" time="0.001">
    </testcase>
    <testcase classname="Type Definitions and Interface Contracts › Configuration Types" name="should create valid TestSuiteConfig objects" time="0">
    </testcase>
    <testcase classname="Type Definitions and Interface Contracts › Configuration Types" name="should create valid ExternalDependency objects" time="0.001">
    </testcase>
    <testcase classname="Type Definitions and Interface Contracts › Configuration Types" name="should create valid AccessibilityRule objects" time="0">
    </testcase>
    <testcase classname="Type Definitions and Interface Contracts › Configuration Types" name="should create valid VerificationConfig objects" time="0">
    </testcase>
    <testcase classname="Type Definitions and Interface Contracts › Report Types" name="should create valid VerificationReport objects" time="0">
    </testcase>
    <testcase classname="Type Definitions and Interface Contracts › Production Environment Types" name="should create valid NetworkCondition objects" time="0">
    </testcase>
    <testcase classname="Type Definitions and Interface Contracts › Production Environment Types" name="should create valid BrowserType objects" time="0">
    </testcase>
    <testcase classname="Type Definitions and Interface Contracts › Production Environment Types" name="should create valid ProductionEnvironmentConfig objects" time="0.001">
    </testcase>
    <testcase classname="Type Definitions and Interface Contracts › Pipeline Types" name="should create valid VerificationStage objects" time="0">
    </testcase>
    <testcase classname="Type Definitions and Interface Contracts › Pipeline Types" name="should create valid VerificationStageResult objects" time="0.001">
    </testcase>
    <testcase classname="Type Definitions and Interface Contracts › Pipeline Types" name="should create valid PipelineOptions objects" time="0">
    </testcase>
    <testcase classname="Type Definitions and Interface Contracts › Type Validation and Edge Cases" name="should handle optional properties correctly" time="0.001">
    </testcase>
    <testcase classname="Type Definitions and Interface Contracts › Type Validation and Edge Cases" name="should handle union types correctly" time="0">
    </testcase>
    <testcase classname="Type Definitions and Interface Contracts › Type Validation and Edge Cases" name="should handle nested object types correctly" time="0">
    </testcase>
  </testsuite>
  <testsuite name="Error Handling Integration Tests" errors="0" failures="0" skipped="0" timestamp="2025-08-01T19:02:06" time="0.071" tests="4">
    <testcase classname="Error Handling Integration Tests" name="should create and handle errors properly" time="0.001">
    </testcase>
    <testcase classname="Error Handling Integration Tests" name="should collect and provide error statistics" time="0.001">
    </testcase>
    <testcase classname="Error Handling Integration Tests" name="should determine error recoverability based on error patterns" time="0.001">
    </testcase>
    <testcase classname="Error Handling Integration Tests" name="should set appropriate retry limits based on error type and severity" time="0.001">
    </testcase>
  </testsuite>
  <testsuite name="VerificationLogger" errors="0" failures="0" skipped="0" timestamp="2025-08-01T19:02:06" time="0.162" tests="17">
    <testcase classname="VerificationLogger" name="should log messages at appropriate levels" time="0.001">
    </testcase>
    <testcase classname="VerificationLogger" name="should log verification errors with proper structure" time="0">
    </testcase>
    <testcase classname="RetryManager" name="should succeed on first attempt when operation succeeds" time="0">
    </testcase>
    <testcase classname="RetryManager" name="should retry retryable errors" time="0.039">
    </testcase>
    <testcase classname="RetryManager" name="should not retry non-retryable errors" time="0.001">
    </testcase>
    <testcase classname="RetryManager" name="should respect max retries limit" time="0.037">
    </testcase>
    <testcase classname="RetryManager" name="should calculate exponential backoff delays" time="0.005">
    </testcase>
    <testcase classname="ErrorHandler" name="should create verification errors with proper context" time="0.001">
    </testcase>
    <testcase classname="ErrorHandler" name="should handle errors and provide recovery actions" time="0.003">
    </testcase>
    <testcase classname="ErrorHandler" name="should execute operations with comprehensive error handling" time="0">
    </testcase>
    <testcase classname="ErrorHandler" name="should handle operation timeouts" time="0">
    </testcase>
    <testcase classname="ErrorHandler" name="should provide error statistics" time="0.001">
    </testcase>
    <testcase classname="ErrorHandler" name="should generate appropriate recovery actions for different error types" time="0.001">
    </testcase>
    <testcase classname="ErrorHandler" name="should determine error recoverability correctly" time="0.001">
    </testcase>
    <testcase classname="ErrorHandler" name="should set appropriate max retries based on error type and severity" time="0">
    </testcase>
    <testcase classname="Integration Tests" name="should create and handle errors properly" time="0.001">
    </testcase>
    <testcase classname="Integration Tests" name="should provide error statistics" time="0">
    </testcase>
  </testsuite>
  <testsuite name="CLI Integration Tests" errors="0" failures="0" skipped="0" timestamp="2025-08-01T19:02:06" time="0.065" tests="17">
    <testcase classname="CLI Integration Tests › CLI Command Execution" name="should handle run command successfully" time="0.002">
    </testcase>
    <testcase classname="CLI Integration Tests › CLI Command Execution" name="should handle build command" time="0">
    </testcase>
    <testcase classname="CLI Integration Tests › CLI Command Execution" name="should handle test command" time="0.001">
    </testcase>
    <testcase classname="CLI Integration Tests › CLI Command Execution" name="should handle performance command" time="0">
    </testcase>
    <testcase classname="CLI Integration Tests › CLI Command Execution" name="should handle accessibility command" time="0">
    </testcase>
    <testcase classname="CLI Integration Tests › Configuration Management" name="should handle init command for configuration setup" time="0">
    </testcase>
    <testcase classname="CLI Integration Tests › Configuration Management" name="should handle validate-config command" time="0.001">
    </testcase>
    <testcase classname="CLI Integration Tests › Configuration Management" name="should handle health-check command" time="0">
    </testcase>
    <testcase classname="CLI Integration Tests › Output Format Handling" name="should handle JSON output format" time="0.001">
    </testcase>
    <testcase classname="CLI Integration Tests › Output Format Handling" name="should handle HTML output format" time="0">
    </testcase>
    <testcase classname="CLI Integration Tests › Output Format Handling" name="should handle console output format" time="0">
    </testcase>
    <testcase classname="CLI Integration Tests › Error Handling" name="should handle CLI command errors gracefully" time="0.001">
    </testcase>
    <testcase classname="CLI Integration Tests › Error Handling" name="should handle configuration file errors" time="0">
    </testcase>
    <testcase classname="CLI Integration Tests › Error Handling" name="should handle report generation errors" time="0.001">
    </testcase>
    <testcase classname="CLI Integration Tests › CI/CD Integration" name="should handle ci-check command" time="0">
    </testcase>
    <testcase classname="CLI Integration Tests › CI/CD Integration" name="should handle verbose mode" time="0.001">
    </testcase>
    <testcase classname="CLI Integration Tests › CI/CD Integration" name="should handle skip-stages option" time="0.001">
    </testcase>
  </testsuite>
  <testsuite name="ConfigManager" errors="0" failures="0" skipped="0" timestamp="2025-08-01T19:02:06" time="0.079" tests="29">
    <testcase classname="ConfigManager › Basic Configuration" name="should create a config manager with default configuration" time="0.001">
    </testcase>
    <testcase classname="ConfigManager › Basic Configuration" name="should merge custom configuration with defaults" time="0.001">
    </testcase>
    <testcase classname="ConfigManager › Environment Configuration" name="should load environment-specific configuration" time="0.001">
    </testcase>
    <testcase classname="ConfigManager › Environment Configuration" name="should handle environment inheritance" time="0">
    </testcase>
    <testcase classname="ConfigManager › Environment Configuration" name="should list available environments" time="0.001">
    </testcase>
    <testcase classname="ConfigManager › Environment Configuration" name="should switch environments" time="0">
    </testcase>
    <testcase classname="ConfigManager › Configuration Options" name="should apply test suite filtering" time="0">
    </testcase>
    <testcase classname="ConfigManager › Configuration Options" name="should apply performance threshold overrides" time="0">
    </testcase>
    <testcase classname="ConfigManager › Configuration Options" name="should apply accessibility overrides" time="0">
    </testcase>
    <testcase classname="ConfigManager › Configuration Options" name="should apply custom thresholds" time="0.001">
    </testcase>
    <testcase classname="ConfigManager › Configuration Management" name="should update performance thresholds" time="0">
    </testcase>
    <testcase classname="ConfigManager › Configuration Management" name="should update accessibility settings" time="0">
    </testcase>
    <testcase classname="ConfigManager › Configuration Management" name="should update test suite configuration" time="0">
    </testcase>
    <testcase classname="ConfigManager › Configuration Management" name="should add new test suite" time="0">
    </testcase>
    <testcase classname="ConfigManager › Configuration Management" name="should remove test suite" time="0">
    </testcase>
    <testcase classname="ConfigManager › Configuration Management" name="should toggle test suites by type" time="0.001">
    </testcase>
    <testcase classname="ConfigManager › Configuration Validation" name="should validate valid configuration" time="0">
    </testcase>
    <testcase classname="ConfigManager › Configuration Validation" name="should detect invalid performance thresholds" time="0">
    </testcase>
    <testcase classname="ConfigManager › Configuration Validation" name="should detect invalid test suite configuration" time="0">
    </testcase>
    <testcase classname="ConfigManager › Configuration Summary" name="should provide configuration summary" time="0.002">
    </testcase>
    <testcase classname="ConfigManager › File Operations" name="should load configuration from file" time="0.001">
    </testcase>
    <testcase classname="ConfigManager › File Operations" name="should handle file loading errors gracefully" time="0">
    </testcase>
    <testcase classname="ConfigManager › File Operations" name="should save configuration to file" time="0">
    </testcase>
    <testcase classname="ConfigTemplateGenerator" name="should generate basic template" time="0.001">
    </testcase>
    <testcase classname="ConfigTemplateGenerator" name="should generate environment-specific config" time="0.001">
    </testcase>
    <testcase classname="ConfigTemplateGenerator" name="should generate comprehensive config" time="0">
    </testcase>
    <testcase classname="ConfigValidator" name="should validate configuration with suggestions" time="0.001">
    </testcase>
    <testcase classname="ConfigValidator" name="should generate performance recommendations" time="0.001">
    </testcase>
    <testcase classname="ConfigComparison" name="should compare configurations and find differences" time="0.001">
    </testcase>
  </testsuite>
  <testsuite name="PerformanceMonitor" errors="0" failures="0" skipped="0" timestamp="2025-08-01T19:02:06" time="0.054" tests="12">
    <testcase classname="PerformanceMonitor › constructor" name="should create a performance monitor with config" time="0.001">
    </testcase>
    <testcase classname="PerformanceMonitor › runPerformanceTest" name="should run a complete performance test" time="0.001">
    </testcase>
    <testcase classname="PerformanceMonitor › runPerformanceTest" name="should detect performance violations" time="0.001">
    </testcase>
    <testcase classname="PerformanceMonitor › runPerformanceTest" name="should handle Lighthouse audit failures gracefully" time="0">
    </testcase>
    <testcase classname="PerformanceMonitor › runPerformanceTest" name="should handle page navigation failures" time="0.008">
    </testcase>
    <testcase classname="PerformanceMonitor › testCriticalPages" name="should test multiple pages successfully" time="0.001">
    </testcase>
    <testcase classname="PerformanceMonitor › testCriticalPages" name="should handle individual page test failures" time="0">
    </testcase>
    <testcase classname="PerformanceMonitor › generatePerformanceReport" name="should generate a comprehensive performance report" time="0.001">
    </testcase>
    <testcase classname="PerformanceMonitor › generatePerformanceReport" name="should handle reports with violations" time="0">
    </testcase>
    <testcase classname="PerformanceMonitor › generatePerformanceReport" name="should handle empty results gracefully" time="0">
    </testcase>
    <testcase classname="PerformanceMonitor › createPerformanceMonitor" name="should create a performance monitor instance" time="0.001">
    </testcase>
    <testcase classname="PerformanceMonitor › threshold validation" name="should correctly identify warning vs error violations" time="0.001">
    </testcase>
  </testsuite>
  <testsuite name="VerificationPipeline Integration Tests" errors="0" failures="5" skipped="0" timestamp="2025-08-01T19:02:05" time="1.766" tests="14">
    <testcase classname="VerificationPipeline Integration Tests › Complete Pipeline Execution" name="should execute all enabled stages in correct order" time="0.078">
      <failure>Error: expect(received).toBe(expected) // Object.is equality

Expected: &quot;passed&quot;
Received: &quot;failed&quot;
    at Object.toBe (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/__tests__/pipeline.integration.test.ts:216:36)</failure>
    </testcase>
    <testcase classname="VerificationPipeline Integration Tests › Complete Pipeline Execution" name="should handle mixed success and failure scenarios" time="0.076">
      <failure>Error: expect(received).toContain(expected) // indexOf

Expected value: StringContaining &quot;Fix tests issues before deployment&quot;
Received array: [&quot;Fix performance issues before deployment&quot;, &quot;Fix accessibility issues before deployment&quot;]
    at Object.toContain (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/__tests__/pipeline.integration.test.ts:277:38)</failure>
    </testcase>
    <testcase classname="VerificationPipeline Integration Tests › Complete Pipeline Execution" name="should respect stage skipping configuration" time="0.035">
    </testcase>
    <testcase classname="VerificationPipeline Integration Tests › Complete Pipeline Execution" name="should handle configuration validation errors" time="0.021">
      <failure>Error: Configuration validation failed: LCP threshold must be positive
    at configValidation.errorHandler.executeWithErrorHandling.operationName (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/pipeline.ts:97:19)
    at ErrorHandler.operation [as executeWithErrorHandling] (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/error-handling.ts:466:30)
    at VerificationPipeline.executeWithErrorHandling [as execute] (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/pipeline.ts:93:56)
    at Object.&lt;anonymous&gt; (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/__tests__/pipeline.integration.test.ts:315:22)</failure>
    </testcase>
    <testcase classname="VerificationPipeline Integration Tests › Complete Pipeline Execution" name="should generate comprehensive recommendations" time="0.027">
    </testcase>
    <testcase classname="VerificationPipeline Integration Tests › Error Recovery and Resilience" name="should recover from transient build failures" time="0.029">
    </testcase>
    <testcase classname="VerificationPipeline Integration Tests › Error Recovery and Resilience" name="should handle critical system errors gracefully" time="0.013">
      <failure>Error: System error: Command not found
    at /Users/<USER>/Development/Ice-Box-Hockey/src/verification/__tests__/pipeline.integration.test.ts:385:15
    at /Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-mock/build/index.js:397:39
    at /Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-mock/build/index.js:404:13
    at mockConstructor (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-mock/build/index.js:148:19)
    at spawn (eval at _createMockFunction (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-mock/build/index.js:566:31), &lt;anonymous&gt;:3:51)
    at /Users/<USER>/Development/Ice-Box-Hockey/src/verification/build.ts:172:33
    at new Promise (&lt;anonymous&gt;)
    at BuildVerificationEngine.executeBuild (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/build.ts:171:12)
    at executeBuild (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/build.ts:54:22)
    at ErrorHandler.operation [as executeWithErrorHandling] (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/error-handling.ts:466:30)
    at BuildVerificationEngine.executeWithErrorHandling [as verify] (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/build.ts:53:48)
    at BuildVerificationStage.execute (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/pipeline.ts:393:27)</failure>
      <failure>Error: System error: Command not found
    at /Users/<USER>/Development/Ice-Box-Hockey/src/verification/__tests__/pipeline.integration.test.ts:385:15
    at /Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-mock/build/index.js:397:39
    at /Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-mock/build/index.js:404:13
    at mockConstructor (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-mock/build/index.js:148:19)
    at spawn (eval at _createMockFunction (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-mock/build/index.js:566:31), &lt;anonymous&gt;:3:51)
    at /Users/<USER>/Development/Ice-Box-Hockey/src/verification/build.ts:172:33
    at new Promise (&lt;anonymous&gt;)
    at BuildVerificationEngine.executeBuild (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/build.ts:171:12)
    at executeBuild (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/build.ts:54:22)
    at ErrorHandler.operation [as executeWithErrorHandling] (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/error-handling.ts:466:30)
    at BuildVerificationEngine.executeWithErrorHandling [as verify] (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/build.ts:53:48)
    at BuildVerificationStage.execute (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/pipeline.ts:393:27)
    at RetryManager.executeWithRetry (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/error-handling.ts:256:24)
    at ErrorHandler.executeWithErrorHandling (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/error-handling.ts:479:22)
    at VerificationPipeline.execute (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/pipeline.ts:123:26)
    at Object.&lt;anonymous&gt; (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/__tests__/pipeline.integration.test.ts:389:22)</failure>
      <failure>Error: expect(received).toContain(expected) // indexOf

Expected value: StringContaining &quot;Pipeline execution failed&quot;
Received array: [&quot;Fix build issues before deployment&quot;, &quot;Fix tests issues before deployment&quot;, &quot;Fix performance issues before deployment&quot;, &quot;Fix accessibility issues before deployment&quot;]
    at Object.toContain (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/__tests__/pipeline.integration.test.ts:393:38)</failure>
    </testcase>
    <testcase classname="VerificationPipeline Integration Tests › Error Recovery and Resilience" name="should handle file system errors during metrics collection" time="0.032">
    </testcase>
    <testcase classname="VerificationPipeline Integration Tests › Performance and Scalability" name="should handle large numbers of test suites efficiently" time="0.028">
    </testcase>
    <testcase classname="VerificationPipeline Integration Tests › Performance and Scalability" name="should handle concurrent stage execution when possible" time="0.028">
    </testcase>
    <testcase classname="VerificationPipeline Integration Tests › Reporting and Output" name="should generate detailed reports with all sections" time="0.033">
    </testcase>
    <testcase classname="VerificationPipeline Integration Tests › Reporting and Output" name="should include custom threshold violations in recommendations" time="0.027">
    </testcase>
    <testcase classname="runVerification Integration" name="should execute complete verification workflow" time="0.027">
    </testcase>
    <testcase classname="runVerification Integration" name="should handle different output formats" time="0.007">
      <failure>Error: Configuration validation failed: At least one test suite must be configured
    at configValidation.errorHandler.executeWithErrorHandling.operationName (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/pipeline.ts:97:19)
    at ErrorHandler.operation [as executeWithErrorHandling] (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/error-handling.ts:466:30)
    at VerificationPipeline.executeWithErrorHandling [as execute] (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/pipeline.ts:93:56)
    at runVerification (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/pipeline.ts:731:10)
    at Object.&lt;anonymous&gt; (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/__tests__/pipeline.integration.test.ts:578:24)</failure>
      <failure>Error: Configuration validation failed: At least one test suite must be configured
    at configValidation.errorHandler.executeWithErrorHandling.operationName (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/pipeline.ts:97:19)
    at ErrorHandler.operation [as executeWithErrorHandling] (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/error-handling.ts:466:30)
    at VerificationPipeline.executeWithErrorHandling [as execute] (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/pipeline.ts:93:56)
    at runVerification (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/pipeline.ts:731:10)
    at Object.&lt;anonymous&gt; (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/__tests__/pipeline.integration.test.ts:579:24)</failure>
      <failure>Error: Configuration validation failed: At least one test suite must be configured
    at configValidation.errorHandler.executeWithErrorHandling.operationName (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/pipeline.ts:97:19)
    at ErrorHandler.operation [as executeWithErrorHandling] (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/error-handling.ts:466:30)
    at VerificationPipeline.executeWithErrorHandling [as execute] (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/pipeline.ts:93:56)
    at runVerification (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/pipeline.ts:731:10)
    at Object.&lt;anonymous&gt; (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/__tests__/pipeline.integration.test.ts:580:27)</failure>
    </testcase>
  </testsuite>
  <testsuite name="TestConfigManager" errors="0" failures="0" skipped="0" timestamp="2025-08-01T19:02:06" time="0.156" tests="24">
    <testcase classname="TestConfigManager › loadConfig" name="should return default configuration when no options provided" time="0.007">
    </testcase>
    <testcase classname="TestConfigManager › loadConfig" name="should apply environment-specific overrides for development" time="0.001">
    </testcase>
    <testcase classname="TestConfigManager › loadConfig" name="should apply environment-specific overrides for production" time="0.007">
    </testcase>
    <testcase classname="TestConfigManager › loadConfig" name="should apply environment-specific overrides for test" time="0.001">
    </testcase>
    <testcase classname="TestConfigManager › loadConfig" name="should load configuration from file when configPath provided" time="0.001">
    </testcase>
    <testcase classname="TestConfigManager › loadConfig" name="should handle file loading errors gracefully" time="0.003">
    </testcase>
    <testcase classname="TestConfigManager › loadConfig" name="should apply user overrides on top of other configurations" time="0.001">
    </testcase>
    <testcase classname="TestConfigManager › saveConfig" name="should save configuration to specified file" time="0">
    </testcase>
    <testcase classname="TestConfigManager › createDefaultConfig" name="should create default configuration file" time="0">
    </testcase>
    <testcase classname="TestConfigManager › getTestSuiteConfig" name="should return configuration for specified test suite" time="0.001">
    </testcase>
    <testcase classname="TestConfigManager › getTestSuiteConfig" name="should return undefined for non-existent test suite" time="0">
    </testcase>
    <testcase classname="TestConfigManager › updateTestSuiteConfig" name="should update configuration for specified test suite" time="0.001">
    </testcase>
    <testcase classname="TestConfigManager › updateTestSuiteConfig" name="should not modify original configuration" time="0">
    </testcase>
    <testcase classname="TestConfigManager › toggleTestSuite" name="should enable test suite" time="0">
    </testcase>
    <testcase classname="TestConfigManager › toggleTestSuite" name="should disable test suite" time="0">
    </testcase>
    <testcase classname="TestConfigManager › getEnabledTestSuites" name="should return only enabled test suites" time="0.001">
    </testcase>
    <testcase classname="TestConfigManager › getEnabledTestSuites" name="should return all suites when all are enabled" time="0">
    </testcase>
    <testcase classname="TestConfigManager › configuration validation" name="should validate test suite names" time="0.009">
    </testcase>
    <testcase classname="TestConfigManager › configuration validation" name="should validate test suite types" time="0">
    </testcase>
    <testcase classname="TestConfigManager › configuration validation" name="should validate timeout values" time="0.001">
    </testcase>
    <testcase classname="TestConfigManager › configuration validation" name="should validate retry values" time="0.001">
    </testcase>
    <testcase classname="TestConfigManager › configuration validation" name="should validate overall timeout" time="0.001">
    </testcase>
    <testcase classname="TestConfigManager › configuration validation" name="should normalize output directory paths" time="0">
    </testcase>
    <testcase classname="TestConfigManager › getConfigSchema" name="should return valid JSON schema" time="0">
    </testcase>
  </testsuite>
  <testsuite name="HttpClient" errors="0" failures="3" skipped="0" timestamp="2025-08-01T19:02:06" time="0.31" tests="27">
    <testcase classname="HttpClient › testUrl" name="should return successful status for available URL" time="0.002">
    </testcase>
    <testcase classname="HttpClient › testUrl" name="should return failed status for unavailable URL" time="0.001">
    </testcase>
    <testcase classname="HttpClient › testUrl" name="should handle network errors" time="0.001">
    </testcase>
    <testcase classname="HttpClient › testUrl" name="should handle timeout errors" time="0.002">
    </testcase>
    <testcase classname="HttpClient › testUrl" name="should use custom timeout" time="0.001">
    </testcase>
    <testcase classname="HttpClient › retry logic" name="should retry on failure and eventually succeed" time="0.001">
    </testcase>
    <testcase classname="HttpClient › retry logic" name="should fail after all retries are exhausted" time="0">
    </testcase>
    <testcase classname="GoogleMapsApiTester › testGoogleMapsApi" name="should test Google Maps API without API key" time="0.012">
    </testcase>
    <testcase classname="GoogleMapsApiTester › testGoogleMapsApi" name="should test Google Maps API with API key" time="0">
    </testcase>
    <testcase classname="GoogleMapsApiTester › testGoogleMapsApi" name="should handle Google Maps API failure" time="0.002">
    </testcase>
    <testcase classname="GoogleMapsApiTester › testGoogleMapsServices" name="should test multiple Google Maps services" time="0">
    </testcase>
    <testcase classname="CdnResourceChecker › testCdnResources" name="should test multiple CDN resources" time="0">
    </testcase>
    <testcase classname="CdnResourceChecker › testCdnResources" name="should handle CDN resource failures" time="0.001">
    </testcase>
    <testcase classname="CdnResourceChecker › testCommonCdnResources" name="should test common CDN resources" time="0">
    </testcase>
    <testcase classname="ExternalDependencyChecker › checkAllDependencies" name="should check all types of dependencies" time="0.001">
    </testcase>
    <testcase classname="ExternalDependencyChecker › checkAllDependencies" name="should handle errors gracefully" time="0.001">
    </testcase>
    <testcase classname="ExternalDependencyChecker › checkCriticalDependencies" name="should only check critical dependencies" time="0">
    </testcase>
    <testcase classname="ExternalDependencyChecker › validateDependencyConfig" name="should validate correct dependency configuration" time="0.001">
    </testcase>
    <testcase classname="ExternalDependencyChecker › validateDependencyConfig" name="should detect invalid dependency configuration" time="0">
    </testcase>
    <testcase classname="ExternalDependencyChecker › getDependencySummary" name="should provide correct dependency summary" time="0">
    </testcase>
    <testcase classname="Factory functions › createExternalDependencyChecker" name="should create ExternalDependencyChecker with default dependencies" time="0">
    </testcase>
    <testcase classname="Factory functions › createExternalDependencyChecker" name="should create ExternalDependencyChecker with custom dependencies" time="0.001">
    </testcase>
    <testcase classname="Factory functions › testSingleUrl" name="should test single URL with default timeout" time="0">
    </testcase>
    <testcase classname="Factory functions › testSingleUrl" name="should test single URL with custom timeout" time="0">
    </testcase>
    <testcase classname="ExternalDependencyChecker Mock Scenarios › timeout scenarios" name="should handle timeout for slow dependencies" time="0.001">
      <failure>Error: expect(received).toBe(expected) // Object.is equality

Expected: false
Received: true
    at Object.toBe (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/__tests__/external-dependencies.test.ts:610:45)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</failure>
    </testcase>
    <testcase classname="ExternalDependencyChecker Mock Scenarios › timeout scenarios" name="should handle network errors gracefully" time="0.042">
      <failure>Error: expect(received).toBe(expected) // Object.is equality

Expected: false
Received: true
    at Object.toBe (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/__tests__/external-dependencies.test.ts:618:45)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</failure>
    </testcase>
    <testcase classname="ExternalDependencyChecker Mock Scenarios › performance and optimization" name="should handle large numbers of dependencies efficiently" time="0.005">
      <failure>Error: expect(jest.fn()).toHaveBeenCalledTimes(expected)

Expected number of calls: 50
Received number of calls: 51
    at Object.toHaveBeenCalledTimes (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/__tests__/external-dependencies.test.ts:646:25)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</failure>
    </testcase>
  </testsuite>
  <testsuite name="External Dependencies Integration Tests" errors="0" failures="0" skipped="0" timestamp="2025-08-01T19:02:07" time="0.057" tests="9">
    <testcase classname="External Dependencies Integration Tests › Integration with ConfigManager" name="should use dependencies from configuration" time="0.002">
    </testcase>
    <testcase classname="External Dependencies Integration Tests › Integration with ConfigManager" name="should handle critical dependencies from configuration" time="0">
    </testcase>
    <testcase classname="External Dependencies Integration Tests › Integration with ConfigManager" name="should validate configuration dependencies" time="0.001">
    </testcase>
    <testcase classname="External Dependencies Integration Tests › Real-world dependency scenarios" name="should handle mixed success and failure scenarios" time="0.001">
    </testcase>
    <testcase classname="External Dependencies Integration Tests › Real-world dependency scenarios" name="should provide comprehensive dependency summary" time="0.001">
    </testcase>
    <testcase classname="External Dependencies Integration Tests › Performance and timeout handling" name="should respect custom timeout configurations" time="0">
    </testcase>
    <testcase classname="External Dependencies Integration Tests › Performance and timeout handling" name="should handle concurrent dependency checks efficiently" time="0">
    </testcase>
    <testcase classname="External Dependencies Integration Tests › Error recovery and resilience" name="should continue checking other dependencies when some fail" time="0">
    </testcase>
    <testcase classname="External Dependencies Integration Tests › Error recovery and resilience" name="should provide meaningful error messages for different failure types" time="0.001">
    </testcase>
  </testsuite>
  <testsuite name="AccessibilityValidator" errors="0" failures="0" skipped="0" timestamp="2025-08-01T19:02:06" time="0.134" tests="14">
    <testcase classname="AccessibilityValidator › constructor" name="should create validator with default config" time="0.001">
    </testcase>
    <testcase classname="AccessibilityValidator › constructor" name="should create validator with custom config" time="0.001">
    </testcase>
    <testcase classname="AccessibilityValidator › validate" name="should validate all configured pages" time="0.001">
    </testcase>
    <testcase classname="AccessibilityValidator › validate" name="should handle browser launch failure" time="0.033">
    </testcase>
    <testcase classname="AccessibilityValidator › validate" name="should close browser even if validation fails" time="0.005">
    </testcase>
    <testcase classname="AccessibilityValidator › generateReport" name="should generate report for compliant result" time="0.001">
    </testcase>
    <testcase classname="AccessibilityValidator › generateReport" name="should generate report with violations" time="0.001">
    </testcase>
    <testcase classname="AccessibilityValidator › generateReport" name="should generate report with warnings" time="0.001">
    </testcase>
    <testcase classname="AccessibilityTestSuite › constructor" name="should create test suite with config manager" time="0.003">
    </testcase>
    <testcase classname="AccessibilityTestSuite › execute" name="should execute accessibility validation and return test result" time="0.001">
    </testcase>
    <testcase classname="AccessibilityTestSuite › execute" name="should handle validation failure" time="0">
    </testcase>
    <testcase classname="AccessibilityTestSuite › execute" name="should handle validator exception" time="0">
    </testcase>
    <testcase classname="Accessibility Integration" name="should integrate with ConfigManager for WCAG level" time="0">
    </testcase>
    <testcase classname="Accessibility Integration" name="should use timeout from test suite config" time="0.001">
    </testcase>
  </testsuite>
  <testsuite name="ReportGenerator" errors="0" failures="5" skipped="0" timestamp="2025-08-01T19:02:07" time="0.105" tests="28">
    <testcase classname="ReportGenerator › constructor" name="should initialize with provided options" time="0.001">
    </testcase>
    <testcase classname="ReportGenerator › constructor" name="should use default theme when no custom theme provided" time="0">
    </testcase>
    <testcase classname="ReportGenerator › constructor" name="should use custom theme when provided" time="0">
    </testcase>
    <testcase classname="ReportGenerator › generateReport" name="should generate both JSON and HTML reports" time="0.012">
    </testcase>
    <testcase classname="ReportGenerator › generateReport" name="should create output directory if it does not exist" time="0.001">
    </testcase>
    <testcase classname="ReportGenerator › generateReport" name="should write JSON report to file" time="0">
    </testcase>
    <testcase classname="ReportGenerator › generateReport" name="should write HTML report to file" time="0.001">
    </testcase>
    <testcase classname="ReportGenerator › generateReport" name="should include timestamp in filename when option is enabled" time="0">
    </testcase>
    <testcase classname="ReportGenerator › generateDeploymentDecision" name="should mark as ready when all checks pass" time="0">
    </testcase>
    <testcase classname="ReportGenerator › generateDeploymentDecision" name="should mark as not ready when build fails" time="0.001">
    </testcase>
    <testcase classname="ReportGenerator › generateDeploymentDecision" name="should mark as not ready when tests fail" time="0">
    </testcase>
    <testcase classname="ReportGenerator › generateDeploymentDecision" name="should add warnings for performance issues" time="0.002">
      <failure>Error: expect(received).toContain(expected) // indexOf

Expected value: StringContaining &quot;LCP (3000ms) exceeds threshold&quot;
Received array: [&quot;1 build warnings found&quot;, &quot;1 individual test failures found&quot;, &quot;LCP (3000ms) exceeds threshold (2500ms)&quot;, &quot;Lighthouse performance score (70) below threshold (90)&quot;, &quot;1 minor accessibility violations found&quot;]

Looks like you wanted to test for object/array equality with the stricter `toContain` matcher. You probably need to use `toContainEqual` instead.
    at Object.toContain (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/__tests__/reporting.test.ts:346:33)
    at Promise.then.completed (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusTest (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:252:3)
    at _runTestsForDescribeBlock (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-runner/build/runTest.js:444:34)
    at Object.worker (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-runner/build/testWorker.js:106:12)</failure>
    </testcase>
    <testcase classname="ReportGenerator › generateDeploymentDecision" name="should add blockers for critical accessibility violations" time="0.001">
      <failure>Error: expect(received).toContain(expected) // indexOf

Expected value: StringContaining &quot;critical accessibility violations found&quot;
Received array: [&quot;1 test suites failed&quot;, &quot;1 critical accessibility violations found&quot;]

Looks like you wanted to test for object/array equality with the stricter `toContain` matcher. You probably need to use `toContainEqual` instead.
    at Object.toContain (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/__tests__/reporting.test.ts:372:33)
    at Promise.then.completed (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusTest (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:252:3)
    at _runTestsForDescribeBlock (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-runner/build/runTest.js:444:34)
    at Object.worker (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-runner/build/testWorker.js:106:12)</failure>
    </testcase>
    <testcase classname="ReportGenerator › generateDeploymentDecision" name="should generate appropriate recommendations" time="0">
      <failure>Error: expect(received).toContain(expected) // indexOf

Expected value: StringContaining &quot;Fix critical issues before deployment&quot;
Received array: [&quot;Fix critical issues before deployment&quot;, &quot;Address warnings for optimal production performance&quot;, &quot;Fix failing tests to ensure application stability&quot;, &quot;Address accessibility violations to ensure compliance&quot;]

Looks like you wanted to test for object/array equality with the stricter `toContain` matcher. You probably need to use `toContainEqual` instead.
    at Object.toContain (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/__tests__/reporting.test.ts:380:40)
    at Promise.then.completed (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusTest (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:252:3)
    at _runTestsForDescribeBlock (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-runner/build/runTest.js:444:34)
    at Object.worker (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-runner/build/testWorker.js:106:12)</failure>
    </testcase>
    <testcase classname="ReportGenerator › generateSummary" name="should generate accurate summary statistics" time="0.001">
    </testcase>
    <testcase classname="ReportGenerator › generateSummary" name="should count checks correctly" time="0">
    </testcase>
    <testcase classname="ReportGenerator › generateSummary" name="should identify critical issues" time="0">
    </testcase>
    <testcase classname="ReportGenerator › HTML generation" name="should generate valid HTML structure" time="0">
    </testcase>
    <testcase classname="ReportGenerator › HTML generation" name="should include CSS styles" time="0.001">
    </testcase>
    <testcase classname="ReportGenerator › HTML generation" name="should include JavaScript for interactivity" time="0">
    </testcase>
    <testcase classname="ReportGenerator › HTML generation" name="should display deployment status correctly" time="0">
    </testcase>
    <testcase classname="ReportGenerator › HTML generation" name="should show section-specific metrics" time="0.001">
    </testcase>
    <testcase classname="ReportGenerator › JSON generation" name="should generate valid JSON structure" time="0.002">
    </testcase>
    <testcase classname="ReportGenerator › JSON generation" name="should include all required metadata" time="0">
    </testcase>
    <testcase classname="ReportGenerator › JSON generation" name="should preserve original report data" time="0.001">
    </testcase>
    <testcase classname="Factory functions › createReportGenerator" name="should create ReportGenerator instance" time="0.001">
    </testcase>
    <testcase classname="Factory functions › generateVerificationReport" name="should generate report with default options" time="0.001">
      <failure>Error: expect(received).toContain(expected) // indexOf

Expected substring: &quot;./verification-reports&quot;
Received string:    &quot;verification-reports/verification-report-2025-08-01T19-02-07-197Z.json&quot;
    at Object.toContain (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/__tests__/reporting.test.ts:613:31)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</failure>
    </testcase>
    <testcase classname="Factory functions › generateVerificationReport" name="should use custom output directory" time="0.002">
      <failure>Error: expect(received).toContain(expected) // indexOf

Expected substring: &quot;./custom-reports&quot;
Received string:    &quot;custom-reports/verification-report-2025-08-01T19-02-07-199Z.json&quot;
    at Object.toContain (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/__tests__/reporting.test.ts:661:31)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</failure>
    </testcase>
  </testsuite>
  <testsuite name="VerificationPipeline" errors="0" failures="3" skipped="0" timestamp="2025-08-01T19:02:05" time="2.111" tests="16">
    <testcase classname="VerificationPipeline › constructor" name="should initialize with provided options" time="0.004">
    </testcase>
    <testcase classname="VerificationPipeline › constructor" name="should create error handler with correct configuration" time="0.002">
    </testcase>
    <testcase classname="VerificationPipeline › constructor" name="should use debug level when verbose is true" time="0.001">
    </testcase>
    <testcase classname="VerificationPipeline › execute" name="should execute all stages successfully" time="0.001">
    </testcase>
    <testcase classname="VerificationPipeline › execute" name="should handle configuration validation failure" time="0.001">
      <failure>TypeError: Cannot read properties of undefined (reading &apos;message&apos;)
    at VerificationPipeline.message (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/pipeline.ts:238:59)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at Object.&lt;anonymous&gt; (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/__tests__/pipeline.test.ts:180:22)</failure>
    </testcase>
    <testcase classname="VerificationPipeline › execute" name="should handle stage execution failures" time="0.001">
    </testcase>
    <testcase classname="VerificationPipeline › execute" name="should handle stage execution warnings" time="0.002">
    </testcase>
    <testcase classname="VerificationPipeline › execute" name="should skip stages when specified in options" time="0.002">
    </testcase>
    <testcase classname="VerificationPipeline › execute" name="should handle critical pipeline errors" time="0.003">
      <failure>Error: expect(received).toContain(expected) // indexOf

Expected value: StringContaining &quot;Pipeline execution failed&quot;
Received array: [&quot;Pipeline execution failed: Pipeline execution failed&quot;, &quot;Check logs for detailed error information and recovery suggestions&quot;]

Looks like you wanted to test for object/array equality with the stricter `toContain` matcher. You probably need to use `toContainEqual` instead.
    at Object.toContain (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/__tests__/pipeline.test.ts:273:38)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</failure>
    </testcase>
    <testcase classname="VerificationPipeline › execute" name="should log error statistics when errors occur" time="0">
    </testcase>
    <testcase classname="VerificationPipeline › execute" name="should always cleanup error handler resources" time="0.001">
    </testcase>
    <testcase classname="VerificationPipeline › generateRecommendations" name="should generate recommendations for failed stages" time="0">
      <failure>Error: expect(received).toContain(expected) // indexOf

Expected value: &quot;Address performance warnings for optimal performance&quot;
Received array: [&quot;Fix build issues before deployment&quot;, &quot;Address tests warnings for optimal performance&quot;]
    at Object.toContain (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/__tests__/pipeline.test.ts:350:38)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</failure>
    </testcase>
    <testcase classname="VerificationPipeline › generateRecommendations" name="should generate positive recommendation when all stages pass" time="0">
    </testcase>
    <testcase classname="VerificationPipeline › stage timeout configuration" name="should use appropriate timeouts for different stages" time="0.001">
    </testcase>
    <testcase classname="runVerification factory function" name="should create and execute verification pipeline" time="0.001">
    </testcase>
    <testcase classname="runVerification factory function" name="should handle pipeline creation errors" time="0.001">
    </testcase>
  </testsuite>
  <testsuite name="Accessibility Validation System Integration" errors="0" failures="0" skipped="0" timestamp="2025-08-01T19:02:06" time="1.104" tests="19">
    <testcase classname="Accessibility Validation System Integration › AccessibilityValidator" name="should instantiate with correct default configuration" time="0.001">
    </testcase>
    <testcase classname="Accessibility Validation System Integration › AccessibilityValidator" name="should accept custom configuration" time="0">
    </testcase>
    <testcase classname="Accessibility Validation System Integration › AccessibilityValidator" name="should support all WCAG levels" time="0.001">
    </testcase>
    <testcase classname="Accessibility Validation System Integration › AccessibilityValidator" name="should generate axe tags correctly for different WCAG levels" time="0">
    </testcase>
    <testcase classname="Accessibility Validation System Integration › AccessibilityValidator" name="should calculate color contrast ratios correctly" time="0">
    </testcase>
    <testcase classname="Accessibility Validation System Integration › AccessibilityValidator" name="should handle invalid color values gracefully" time="0.001">
    </testcase>
    <testcase classname="Accessibility Validation System Integration › AccessibilityValidator" name="should provide comprehensive remediation guidance" time="0.002">
    </testcase>
    <testcase classname="Accessibility Validation System Integration › AccessibilityValidator" name="should provide fallback guidance for unknown rules" time="0">
    </testcase>
    <testcase classname="Accessibility Validation System Integration › AccessibilityValidator" name="should generate comprehensive accessibility reports" time="0.001">
    </testcase>
    <testcase classname="Accessibility Validation System Integration › AccessibilityValidator" name="should generate success report when compliant" time="0">
    </testcase>
    <testcase classname="Accessibility Validation System Integration › AccessibilityTestSuite" name="should integrate with ConfigManager correctly" time="0">
    </testcase>
    <testcase classname="Accessibility Validation System Integration › AccessibilityTestSuite" name="should handle different WCAG levels from config" time="0">
    </testcase>
    <testcase classname="Accessibility Validation System Integration › AccessibilityTestSuite" name="should use timeout from test suite configuration" time="0">
    </testcase>
    <testcase classname="Accessibility Validation System Integration › AccessibilityTestSuite" name="should use default timeout when not specified in config" time="0">
    </testcase>
    <testcase classname="Accessibility Validation System Integration › Error Handling" name="should handle browser launch failures gracefully" time="0.001">
    </testcase>
    <testcase classname="Accessibility Validation System Integration › Error Handling" name="should handle page navigation failures" time="0">
    </testcase>
    <testcase classname="Accessibility Validation System Integration › Error Handling" name="should handle axe-core analysis failures" time="0">
    </testcase>
    <testcase classname="Accessibility Validation System Integration › Feature Coverage" name="should implement all required task features" time="0">
    </testcase>
    <testcase classname="Accessibility Validation System Integration › Feature Coverage" name="should support comprehensive accessibility testing configuration" time="0.001">
    </testcase>
  </testsuite>
  <testsuite name="Complete Verification Workflow E2E Tests" errors="0" failures="9" skipped="0" timestamp="2025-08-01T19:02:05" time="2.575" tests="14">
    <testcase classname="Complete Verification Workflow E2E Tests › Complete Workflow Scenarios" name="should execute complete verification workflow with mixed results" time="0.163">
      <failure>Error: expect(received).toBeGreaterThan(expected)

Expected: &gt; 0
Received:   0
    at Object.toBeGreaterThan (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/__tests__/verification-workflow.e2e.test.ts:421:45)</failure>
    </testcase>
    <testcase classname="Complete Verification Workflow E2E Tests › Complete Workflow Scenarios" name="should handle all stages passing scenario" time="0.072">
      <failure>Error: expect(received).toBe(expected) // Object.is equality

Expected: &quot;passed&quot;
Received: &quot;failed&quot;
    at Object.toBe (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/__tests__/verification-workflow.e2e.test.ts:474:36)</failure>
    </testcase>
    <testcase classname="Complete Verification Workflow E2E Tests › Complete Workflow Scenarios" name="should handle critical build failure scenario" time="0.053">
      <failure>Error: expect(received).toContain(expected) // indexOf

Expected value: StringContaining &quot;Fix build issues&quot;
Received array: [&quot;Fix build issues before deployment&quot;, &quot;Fix tests issues before deployment&quot;, &quot;Fix performance issues before deployment&quot;, &quot;Fix accessibility issues before deployment&quot;]

Looks like you wanted to test for object/array equality with the stricter `toContain` matcher. You probably need to use `toContainEqual` instead.
    at Object.toContain (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/__tests__/verification-workflow.e2e.test.ts:512:38)</failure>
    </testcase>
    <testcase classname="Complete Verification Workflow E2E Tests › Complete Workflow Scenarios" name="should handle performance threshold violations" time="0.121">
      <failure>Error: expect(received).toBeGreaterThan(expected)

Expected: &gt; 2500
Received:   0
    at Object.toBeGreaterThan (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/__tests__/verification-workflow.e2e.test.ts:555:45)</failure>
    </testcase>
    <testcase classname="Complete Verification Workflow E2E Tests › Complete Workflow Scenarios" name="should handle accessibility violations" time="0.12">
      <failure>Error: expect(received).toContain(expected) // indexOf

Expected value: StringContaining &quot;accessibility&quot;
Received array: [&quot;Address build warnings for optimal performance&quot;, &quot;Fix performance issues before deployment&quot;, &quot;Fix accessibility issues before deployment&quot;]

Looks like you wanted to test for object/array equality with the stricter `toContain` matcher. You probably need to use `toContainEqual` instead.
    at Object.toContain (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/__tests__/verification-workflow.e2e.test.ts:617:38)</failure>
    </testcase>
    <testcase classname="Complete Verification Workflow E2E Tests › Complete Workflow Scenarios" name="should handle external dependency failures" time="0.121">
      <failure>Error: expect(received).toBe(expected) // Object.is equality

Expected: false
Received: true
    at Object.toBe (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/__tests__/verification-workflow.e2e.test.ts:646:60)</failure>
    </testcase>
    <testcase classname="Complete Verification Workflow E2E Tests › Report Generation Integration" name="should generate comprehensive HTML and JSON reports" time="0.162">
    </testcase>
    <testcase classname="Complete Verification Workflow E2E Tests › Report Generation Integration" name="should include detailed deployment decision logic" time="0.116">
    </testcase>
    <testcase classname="Complete Verification Workflow E2E Tests › Report Generation Integration" name="should handle report generation errors gracefully" time="0.115">
      <failure>Error: expect(received).resolves.toBeDefined()

Received promise rejected instead of resolved
Rejected to value: [Error: Disk full]
    at expect (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/expect/build/index.js:113:15)
    at Object.expect (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/__tests__/verification-workflow.e2e.test.ts:721:13)</failure>
    </testcase>
    <testcase classname="Complete Verification Workflow E2E Tests › Configuration Management Integration" name="should validate and use custom configuration" time="0.112">
    </testcase>
    <testcase classname="Complete Verification Workflow E2E Tests › Configuration Management Integration" name="should handle invalid configuration gracefully" time="0.008">
      <failure>Error: Configuration validation failed: LCP threshold must be positive, Lighthouse performance threshold must be between 0 and 100
    at configValidation.errorHandler.executeWithErrorHandling.operationName (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/pipeline.ts:97:19)
    at ErrorHandler.operation [as executeWithErrorHandling] (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/error-handling.ts:466:30)
    at VerificationPipeline.executeWithErrorHandling [as execute] (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/pipeline.ts:93:56)
    at runVerification (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/pipeline.ts:731:10)
    at Object.&lt;anonymous&gt; (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/__tests__/verification-workflow.e2e.test.ts:768:22)</failure>
    </testcase>
    <testcase classname="Complete Verification Workflow E2E Tests › Configuration Management Integration" name="should support configuration overrides" time="0.115">
    </testcase>
    <testcase classname="Complete Verification Workflow E2E Tests › Error Recovery and Resilience" name="should recover from transient failures" time="0.035">
    </testcase>
    <testcase classname="Complete Verification Workflow E2E Tests › Error Recovery and Resilience" name="should handle complete system failure gracefully" time="0.004">
      <failure>Error: System error: Command not found
    at /Users/<USER>/Development/Ice-Box-Hockey/src/verification/__tests__/verification-workflow.e2e.test.ts:835:15
    at /Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-mock/build/index.js:397:39
    at /Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-mock/build/index.js:404:13
    at mockConstructor (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-mock/build/index.js:148:19)
    at spawn (eval at _createMockFunction (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-mock/build/index.js:566:31), &lt;anonymous&gt;:3:51)
    at /Users/<USER>/Development/Ice-Box-Hockey/src/verification/build.ts:172:33
    at new Promise (&lt;anonymous&gt;)
    at BuildVerificationEngine.executeBuild (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/build.ts:171:12)
    at executeBuild (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/build.ts:54:22)
    at ErrorHandler.operation [as executeWithErrorHandling] (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/error-handling.ts:466:30)
    at BuildVerificationEngine.executeWithErrorHandling [as verify] (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/build.ts:53:48)
    at BuildVerificationStage.execute (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/pipeline.ts:393:27)</failure>
      <failure>Error: System error: Command not found
    at /Users/<USER>/Development/Ice-Box-Hockey/src/verification/__tests__/verification-workflow.e2e.test.ts:835:15
    at /Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-mock/build/index.js:397:39
    at /Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-mock/build/index.js:404:13
    at mockConstructor (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-mock/build/index.js:148:19)
    at spawn (eval at _createMockFunction (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jest-mock/build/index.js:566:31), &lt;anonymous&gt;:3:51)
    at /Users/<USER>/Development/Ice-Box-Hockey/src/verification/build.ts:172:33
    at new Promise (&lt;anonymous&gt;)
    at BuildVerificationEngine.executeBuild (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/build.ts:171:12)
    at executeBuild (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/build.ts:54:22)
    at ErrorHandler.operation [as executeWithErrorHandling] (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/error-handling.ts:466:30)
    at BuildVerificationEngine.executeWithErrorHandling [as verify] (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/build.ts:53:48)
    at BuildVerificationStage.execute (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/pipeline.ts:393:27)
    at RetryManager.executeWithRetry (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/error-handling.ts:256:24)
    at ErrorHandler.executeWithErrorHandling (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/error-handling.ts:479:22)
    at VerificationPipeline.execute (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/pipeline.ts:123:26)
    at runVerification (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/pipeline.ts:731:10)
    at Object.&lt;anonymous&gt; (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/__tests__/verification-workflow.e2e.test.ts:843:22)</failure>
      <failure>Error: expect(received).toContain(expected) // indexOf

Expected value: StringContaining &quot;Pipeline execution failed&quot;
Received array: [&quot;Fix build issues before deployment&quot;, &quot;Fix tests issues before deployment&quot;, &quot;Fix performance issues before deployment&quot;, &quot;Fix accessibility issues before deployment&quot;]
    at Object.toContain (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/__tests__/verification-workflow.e2e.test.ts:848:38)</failure>
    </testcase>
  </testsuite>
  <testsuite name="ProductionEnvironmentTester" errors="0" failures="0" skipped="0" timestamp="2025-08-01T19:02:06" time="2.199" tests="24">
    <testcase classname="ProductionEnvironmentTester › constructor" name="should initialize with provided configuration" time="0.005">
    </testcase>
    <testcase classname="ProductionEnvironmentTester › buildProduction" name="should return true when build succeeds" time="0.003">
    </testcase>
    <testcase classname="ProductionEnvironmentTester › buildProduction" name="should return false when build fails" time="0">
    </testcase>
    <testcase classname="ProductionEnvironmentTester › buildProduction" name="should handle build process errors" time="0">
    </testcase>
    <testcase classname="ProductionEnvironmentTester › startPreviewServer" name="should return true when server starts successfully" time="2.102">
    </testcase>
    <testcase classname="ProductionEnvironmentTester › startPreviewServer" name="should return false when server fails to start" time="0.001">
    </testcase>
    <testcase classname="ProductionEnvironmentTester › startPreviewServer" name="should timeout if server takes too long to start" time="0.001">
    </testcase>
    <testcase classname="ProductionEnvironmentTester › testPageLoad" name="should successfully test page loading" time="0.012">
    </testcase>
    <testcase classname="ProductionEnvironmentTester › testPageLoad" name="should handle page load failures" time="0">
    </testcase>
    <testcase classname="ProductionEnvironmentTester › testPageLoad" name="should capture console errors" time="0.001">
    </testcase>
    <testcase classname="ProductionEnvironmentTester › testNavigation" name="should successfully test navigation" time="0">
    </testcase>
    <testcase classname="ProductionEnvironmentTester › testNavigation" name="should handle navigation failures" time="0">
    </testcase>
    <testcase classname="ProductionEnvironmentTester › testSearch" name="should successfully test search functionality" time="0">
    </testcase>
    <testcase classname="ProductionEnvironmentTester › testSearch" name="should handle missing search functionality" time="0.001">
    </testcase>
    <testcase classname="ProductionEnvironmentTester › testResponsiveDesign" name="should successfully test responsive design" time="0">
    </testcase>
    <testcase classname="ProductionEnvironmentTester › testResponsiveDesign" name="should detect responsive design issues" time="0">
    </testcase>
    <testcase classname="ProductionEnvironmentTester › testPWAFeatures" name="should successfully test PWA features" time="0">
    </testcase>
    <testcase classname="ProductionEnvironmentTester › testPWAFeatures" name="should detect missing PWA features" time="0">
    </testcase>
    <testcase classname="ProductionEnvironmentTester › evaluateOverallSuccess" name="should return false if build failed" time="0.001">
    </testcase>
    <testcase classname="ProductionEnvironmentTester › evaluateOverallSuccess" name="should return false if server failed to start" time="0">
    </testcase>
    <testcase classname="ProductionEnvironmentTester › evaluateOverallSuccess" name="should return true if critical tests pass" time="0">
    </testcase>
    <testcase classname="ProductionEnvironmentTester › evaluateOverallSuccess" name="should add warnings for test failures" time="0">
    </testcase>
    <testcase classname="ProductionEnvironmentTester › generateReport" name="should generate comprehensive report" time="0">
    </testcase>
    <testcase classname="ProductionEnvironmentTester › generateReport" name="should include errors and warnings in report" time="0.001">
    </testcase>
  </testsuite>
  <testsuite name="PWA Validation Integration Tests" errors="0" failures="14" skipped="0" timestamp="2025-08-01T19:02:05" time="31.24" tests="14">
    <testcase classname="PWA Validation Integration Tests › Real PWA Validation" name="should validate service worker registration in real application" time="0.006">
      <failure>Error: Server failed to start within timeout
    at /Users/<USER>/Development/Ice-Box-Hockey/src/verification/__tests__/pwa.integration.test.ts:26:16
    at Timeout.task [as _onTimeout] (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jsdom/lib/jsdom/browser/Window.js:520:19)
    at listOnTimeout (node:internal/timers:608:17)
    at processTimers (node:internal/timers:543:7)</failure>
    </testcase>
    <testcase classname="PWA Validation Integration Tests › Real PWA Validation" name="should validate PWA manifest in real application" time="0">
      <failure>Error: Server failed to start within timeout
    at /Users/<USER>/Development/Ice-Box-Hockey/src/verification/__tests__/pwa.integration.test.ts:26:16
    at Timeout.task [as _onTimeout] (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jsdom/lib/jsdom/browser/Window.js:520:19)
    at listOnTimeout (node:internal/timers:608:17)
    at processTimers (node:internal/timers:543:7)</failure>
    </testcase>
    <testcase classname="PWA Validation Integration Tests › Real PWA Validation" name="should test offline functionality in real application" time="0.001">
      <failure>Error: Server failed to start within timeout
    at /Users/<USER>/Development/Ice-Box-Hockey/src/verification/__tests__/pwa.integration.test.ts:26:16
    at Timeout.task [as _onTimeout] (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jsdom/lib/jsdom/browser/Window.js:520:19)
    at listOnTimeout (node:internal/timers:608:17)
    at processTimers (node:internal/timers:543:7)</failure>
    </testcase>
    <testcase classname="PWA Validation Integration Tests › Real PWA Validation" name="should test PWA installability in real application" time="0">
      <failure>Error: Server failed to start within timeout
    at /Users/<USER>/Development/Ice-Box-Hockey/src/verification/__tests__/pwa.integration.test.ts:26:16
    at Timeout.task [as _onTimeout] (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jsdom/lib/jsdom/browser/Window.js:520:19)
    at listOnTimeout (node:internal/timers:608:17)
    at processTimers (node:internal/timers:543:7)</failure>
    </testcase>
    <testcase classname="PWA Validation Integration Tests › Real PWA Validation" name="should validate cache strategy in real application" time="0">
      <failure>Error: Server failed to start within timeout
    at /Users/<USER>/Development/Ice-Box-Hockey/src/verification/__tests__/pwa.integration.test.ts:26:16
    at Timeout.task [as _onTimeout] (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jsdom/lib/jsdom/browser/Window.js:520:19)
    at listOnTimeout (node:internal/timers:608:17)
    at processTimers (node:internal/timers:543:7)</failure>
    </testcase>
    <testcase classname="PWA Validation Integration Tests › Real PWA Validation" name="should run complete PWA validation suite" time="0">
      <failure>Error: Server failed to start within timeout
    at /Users/<USER>/Development/Ice-Box-Hockey/src/verification/__tests__/pwa.integration.test.ts:26:16
    at Timeout.task [as _onTimeout] (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jsdom/lib/jsdom/browser/Window.js:520:19)
    at listOnTimeout (node:internal/timers:608:17)
    at processTimers (node:internal/timers:543:7)</failure>
    </testcase>
    <testcase classname="PWA Validation Integration Tests › PWA Test Suite Integration" name="should execute PWA test suite against real application" time="0">
      <failure>Error: Server failed to start within timeout
    at /Users/<USER>/Development/Ice-Box-Hockey/src/verification/__tests__/pwa.integration.test.ts:26:16
    at Timeout.task [as _onTimeout] (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jsdom/lib/jsdom/browser/Window.js:520:19)
    at listOnTimeout (node:internal/timers:608:17)
    at processTimers (node:internal/timers:543:7)</failure>
    </testcase>
    <testcase classname="PWA Validation Integration Tests › PWA Test Suite Integration" name="should provide detailed failure information when PWA validation fails" time="0">
      <failure>Error: Server failed to start within timeout
    at /Users/<USER>/Development/Ice-Box-Hockey/src/verification/__tests__/pwa.integration.test.ts:26:16
    at Timeout.task [as _onTimeout] (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jsdom/lib/jsdom/browser/Window.js:520:19)
    at listOnTimeout (node:internal/timers:608:17)
    at processTimers (node:internal/timers:543:7)</failure>
    </testcase>
    <testcase classname="PWA Validation Integration Tests › Error Handling Integration" name="should handle network errors gracefully" time="0">
      <failure>Error: Server failed to start within timeout
    at /Users/<USER>/Development/Ice-Box-Hockey/src/verification/__tests__/pwa.integration.test.ts:26:16
    at Timeout.task [as _onTimeout] (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jsdom/lib/jsdom/browser/Window.js:520:19)
    at listOnTimeout (node:internal/timers:608:17)
    at processTimers (node:internal/timers:543:7)</failure>
    </testcase>
    <testcase classname="PWA Validation Integration Tests › Error Handling Integration" name="should handle timeout errors gracefully" time="0">
      <failure>Error: Server failed to start within timeout
    at /Users/<USER>/Development/Ice-Box-Hockey/src/verification/__tests__/pwa.integration.test.ts:26:16
    at Timeout.task [as _onTimeout] (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jsdom/lib/jsdom/browser/Window.js:520:19)
    at listOnTimeout (node:internal/timers:608:17)
    at processTimers (node:internal/timers:543:7)</failure>
    </testcase>
    <testcase classname="PWA Validation Integration Tests › Real Manifest Validation" name="should validate the actual manifest.json file" time="0.001">
      <failure>Error: Server failed to start within timeout
    at /Users/<USER>/Development/Ice-Box-Hockey/src/verification/__tests__/pwa.integration.test.ts:26:16
    at Timeout.task [as _onTimeout] (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jsdom/lib/jsdom/browser/Window.js:520:19)
    at listOnTimeout (node:internal/timers:608:17)
    at processTimers (node:internal/timers:543:7)</failure>
    </testcase>
    <testcase classname="PWA Validation Integration Tests › Real Manifest Validation" name="should validate the actual service worker file" time="0">
      <failure>Error: Server failed to start within timeout
    at /Users/<USER>/Development/Ice-Box-Hockey/src/verification/__tests__/pwa.integration.test.ts:26:16
    at Timeout.task [as _onTimeout] (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jsdom/lib/jsdom/browser/Window.js:520:19)
    at listOnTimeout (node:internal/timers:608:17)
    at processTimers (node:internal/timers:543:7)</failure>
    </testcase>
    <testcase classname="PWA Validation Integration Tests › Performance and Reliability" name="should complete PWA validation within reasonable time" time="0">
      <failure>Error: Server failed to start within timeout
    at /Users/<USER>/Development/Ice-Box-Hockey/src/verification/__tests__/pwa.integration.test.ts:26:16
    at Timeout.task [as _onTimeout] (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jsdom/lib/jsdom/browser/Window.js:520:19)
    at listOnTimeout (node:internal/timers:608:17)
    at processTimers (node:internal/timers:543:7)</failure>
    </testcase>
    <testcase classname="PWA Validation Integration Tests › Performance and Reliability" name="should be reliable across multiple runs" time="0">
      <failure>Error: Server failed to start within timeout
    at /Users/<USER>/Development/Ice-Box-Hockey/src/verification/__tests__/pwa.integration.test.ts:26:16
    at Timeout.task [as _onTimeout] (/Users/<USER>/Development/Ice-Box-Hockey/node_modules/jsdom/lib/jsdom/browser/Window.js:520:19)
    at listOnTimeout (node:internal/timers:608:17)
    at processTimers (node:internal/timers:543:7)</failure>
    </testcase>
  </testsuite>
  <testsuite name="ProductionEnvironmentTester Integration" errors="0" failures="1" skipped="0" timestamp="2025-08-01T19:02:05" time="234.309" tests="11">
    <testcase classname="ProductionEnvironmentTester Integration › Full Production Environment Test" name="should run complete production environment test suite" time="32.313">
    </testcase>
    <testcase classname="ProductionEnvironmentTester Integration › Full Production Environment Test" name="should generate detailed report" time="37.022">
    </testcase>
    <testcase classname="ProductionEnvironmentTester Integration › Individual Test Components" name="should successfully build production version" time="2.06">
    </testcase>
    <testcase classname="ProductionEnvironmentTester Integration › Individual Test Components" name="should handle server startup and shutdown" time="31.926">
    </testcase>
    <testcase classname="ProductionEnvironmentTester Integration › Error Handling" name="should handle build failures gracefully" time="32.302">
      <failure>Error: expect(received).toBe(expected) // Object.is equality

Expected: false
Received: true
    at Object.toBe (/Users/<USER>/Development/Ice-Box-Hockey/src/verification/__tests__/production-environment.integration.test.ts:159:35)</failure>
    </testcase>
    <testcase classname="ProductionEnvironmentTester Integration › Error Handling" name="should handle server startup failures gracefully" time="32.408">
    </testcase>
    <testcase classname="ProductionEnvironmentTester Integration › Configuration Validation" name="should work with minimal configuration" time="0.001">
    </testcase>
    <testcase classname="ProductionEnvironmentTester Integration › Configuration Validation" name="should handle custom network conditions" time="0">
    </testcase>
    <testcase classname="ProductionEnvironmentTester Integration › Configuration Validation" name="should handle multiple browsers configuration" time="0.001">
    </testcase>
    <testcase classname="ProductionEnvironmentTester Integration › Performance Metrics" name="should collect meaningful performance metrics" time="32.329">
    </testcase>
    <testcase classname="ProductionEnvironmentTester Integration › Cross-Browser Testing" name="should test across enabled browsers" time="32.179">
    </testcase>
  </testsuite>
</testsuites>