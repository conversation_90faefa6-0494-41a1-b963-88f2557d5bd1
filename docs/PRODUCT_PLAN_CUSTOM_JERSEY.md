# Product Plan: Custom Jersey Designer

## 1. Overview

This document outlines the plan for a new **Custom Jersey Designer** feature for the Ice Box Hockey website. This feature will allow users to create personalized hockey jerseys by choosing styles, colors, and adding custom names, numbers, and logos.

**Goal:** Increase user engagement, provide a unique user experience, and drive sales of customized apparel.

## 2. Feature Requirements (PRD)

### User Stories

-   **As a user, I want to** choose a base jersey style (e.g., home, away, alternate).
-   **As a user, I want to** select a primary and secondary color for the jersey.
-   **As a user, I want to** add a team logo to the front of the jersey from a predefined gallery.
-   **As a user, I want to** add a custom name and number to the back of the jersey.
-   **As a user, I want to** choose the font style and color for the name and number.
-   **As a user, I want to** see a live, interactive preview of the jersey that updates as I make changes.
-   **As a user, I want to** view the total price, which updates based on my customizations.
-   **As a user, I want to** select my desired size and quantity.
-   **As a user, I want to** add the final custom jersey design to my shopping cart.

### UI/UX Design

-   A new, dedicated page at the `/create-jersey` route.
-   An interactive canvas component displaying the front and back of the jersey.
-   A control panel with intuitive options for style, color, text, and logos.
-   Real-time updates on the canvas to reflect user selections.
-   A clear, dynamic pricing display.

## 3. Technical Specification

### New Components

-   `pages/JerseyCustomizer.tsx`: The main page component for the feature.
-   `components/customizer/Canvas.tsx`: The interactive jersey preview component (using SVG or a canvas library).
-   `components/customizer/Controls.tsx`: The main panel for all customization options.
-   `components/customizer/ColorPicker.tsx`: A component for selecting colors.
-   `components/customizer/FontSelector.tsx`: A component for selecting fonts.
-   `components/customizer/LogoPicker.tsx`: A component for selecting a logo.

### Data Model

We will introduce a new type for the custom jersey design object.

```typescript
// src/types/product.ts
export interface CustomJerseyDesign {
  baseStyle: 'home' | 'away' | 'alternate';
  primaryColor: string;
  secondaryColor: string;
  logoId: string; // ID of a predefined logo
  name: {
    text: string;
    font: string;
    color: string;
  };
  number: {
    text: string;
    font: string;
    color: string;
  };
}
```

### State Management

-   The state of the jersey customizer will be managed locally within the `JerseyCustomizer` page component, likely using the `useReducer` hook for complex state logic.

### API & Backend

-   No new backend endpoints are required initially. The custom design object and product details will be added to the client-side cart state managed by Zustand.

## 4. Implementation Plan (Task Breakdown)

1.  **Setup & Routing:**
    -   Create the new file structure for the components.
    -   Add the new `/create-jersey` route in `src/App.tsx`.

2.  **Build UI Components:**
    -   Develop the static UI for the `JerseyCustomizer` page, including the `Canvas` and `Controls` components.

3.  **Implement Canvas Logic:**
    -   Implement the logic to render an SVG-based jersey that can be dynamically updated.

4.  **State & Integration:**
    -   Set up the state management for the customizer.
    -   Connect the `Controls` to the `Canvas` so user selections update the preview in real-time.

5.  **Add to Cart Functionality:**
    -   Implement the logic to add the final custom jersey configuration to the shopping cart.

6.  **Testing:**
    -   Write unit tests for the new components and state logic.
    -   Write a Playwright E2E test for the entire jersey customization flow.