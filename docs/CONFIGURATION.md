# Configuration Reference

This document provides comprehensive documentation for configuring the production deployment verification system.

## Table of Contents

- [Configuration File Structure](#configuration-file-structure)
- [Configuration Sections](#configuration-sections)
- [Environment-Specific Configuration](#environment-specific-configuration)
- [CLI Configuration Options](#cli-configuration-options)
- [Configuration Validation](#configuration-validation)
- [Configuration Best Practices](#configuration-best-practices)
- [Advanced Configuration](#advanced-configuration)
- [Migration Guide](#migration-guide)
- [Troubleshooting Configuration](#troubleshooting-configuration)

## Configuration File Structure

The verification system uses a JSON configuration file (`verification.config.json`) with support for environment-specific overrides.

### Basic Structure

```json
{
  "default": {
    // Default configuration applied to all environments
    "buildSettings": { ... },
    "performanceThresholds": { ... },
    "testSuites": [ ... ],
    "externalDependencies": [ ... ]
  },
  "environments": {
    "development": {
      "name": "development",
      "overrides": {
        // Development-specific overrides
      }
    },
    "staging": {
      "name": "staging",
      "extends": "development",  // Optional inheritance
      "overrides": {
        // Staging-specific overrides
      }
    },
    "production": {
      "name": "production",
      "overrides": {
        // Production-specific overrides
      }
    }
  }
}
```

### Configuration Initialization

```bash
# Create default configuration
npm run verify:init

# Create with specific preset
npx tsx src/verification/cli.ts init --preset production

# Force overwrite existing configuration
npm run verify:init -- --force
```

## Configuration Sections

### Build Settings

Controls how the application is built and verified.

```json
{
  "buildSettings": {
    "mode": "production",        // "development" | "production"
    "sourceMaps": false,         // Generate source maps for debugging
    "minification": true         // Enable code minification and optimization
  }
}
```

**Options:**
- **`mode`**: Build mode affecting optimization level
  - `"development"`: Fast builds with debugging support
  - `"production"`: Optimized builds for deployment
- **`sourceMaps`**: Whether to generate source maps
  - `true`: Include source maps (larger bundle, better debugging)
  - `false`: No source maps (smaller bundle, production-ready)
- **`minification`**: Enable/disable code minification
  - `true`: Minify code for smaller bundle size
  - `false`: Keep code readable for debugging

### Performance Thresholds

Defines acceptable performance limits for Core Web Vitals and Lighthouse metrics.

```json
{
  "performanceThresholds": {
    "lcp": 2500,                    // Largest Contentful Paint (ms)
    "fid": 100,                     // First Input Delay (ms)
    "cls": 0.1,                     // Cumulative Layout Shift (0-1)
    "lighthousePerformance": 90,    // Lighthouse Performance Score (0-100)
    "fcp": 1800,                    // First Contentful Paint (ms)
    "ttfb": 600,                    // Time to First Byte (ms)
    "speedIndex": 3000              // Speed Index (ms)
  }
}
```

**Core Web Vitals Guidelines:**
- **LCP (Largest Contentful Paint)**:
  - Good: ≤ 2.5s
  - Needs Improvement: 2.5s - 4.0s
  - Poor: > 4.0s
- **FID (First Input Delay)**:
  - Good: ≤ 100ms
  - Needs Improvement: 100ms - 300ms
  - Poor: > 300ms
- **CLS (Cumulative Layout Shift)**:
  - Good: ≤ 0.1
  - Needs Improvement: 0.1 - 0.25
  - Poor: > 0.25

**Lighthouse Performance Score:**
- 90-100: Excellent
- 50-89: Good
- 0-49: Needs Improvement

### Accessibility Configuration

Controls accessibility testing and compliance requirements.

```json
{
  "accessibilityLevel": "AA",         // "A" | "AA" | "AAA"
  "accessibilityRules": {
    "include": ["wcag2a", "wcag2aa"], // Rule sets to include
    "exclude": ["color-contrast-enhanced"], // Specific rules to exclude
    "customRules": []                 // Custom accessibility rules
  }
}
```

**WCAG Compliance Levels:**
- **A**: Basic accessibility compliance (minimum level)
- **AA**: Standard compliance (recommended for most websites)
- **AAA**: Enhanced compliance (highest level, very strict)

**Common Rule Sets:**
- `wcag2a`: WCAG 2.1 Level A rules
- `wcag2aa`: WCAG 2.1 Level AA rules
- `wcag2aaa`: WCAG 2.1 Level AAA rules
- `best-practice`: Additional best practice rules

### Test Suite Configuration

Defines which test suites to run and their configuration.

```json
{
  "testSuites": [
    {
      "name": "unit-tests",           // Unique identifier for the test suite
      "type": "unit",                 // Test suite type
      "enabled": true,                // Enable/disable this suite
      "timeout": 30000,               // Timeout in milliseconds
      "retries": 2,                   // Number of retries on failure
      "tags": ["fast", "isolated"],   // Tags for filtering and organization
      "description": "Unit tests for individual components and functions",
      "priority": "high"              // Priority level for execution order
    },
    {
      "name": "integration-tests",
      "type": "integration",
      "enabled": true,
      "timeout": 60000,
      "retries": 2,
      "tags": ["integration", "api"],
      "description": "Integration tests for component interactions",
      "priority": "high"
    },
    {
      "name": "e2e-tests",
      "type": "e2e",
      "enabled": true,
      "timeout": 120000,
      "retries": 3,
      "tags": ["e2e", "user-flows"],
      "description": "End-to-end user journey tests",
      "priority": "critical"
    },
    {
      "name": "performance-tests",
      "type": "performance",
      "enabled": true,
      "timeout": 180000,
      "retries": 2,
      "tags": ["performance", "lighthouse"],
      "description": "Performance and Core Web Vitals tests",
      "priority": "high"
    },
    {
      "name": "accessibility-tests",
      "type": "accessibility",
      "enabled": true,
      "timeout": 90000,
      "retries": 2,
      "tags": ["accessibility", "a11y", "wcag"],
      "description": "WCAG accessibility compliance tests",
      "priority": "high"
    }
  ]
}
```

**Test Suite Types:**
- **`unit`**: Fast, isolated component tests (Jest)
- **`integration`**: Component interaction tests
- **`e2e`**: End-to-end user journey tests (Playwright)
- **`performance`**: Performance and Core Web Vitals tests
- **`accessibility`**: WCAG compliance tests

**Priority Levels:**
- **`critical`**: Must pass for deployment
- **`high`**: Important for quality assurance
- **`medium`**: Good to have, may not block deployment
- **`low`**: Optional, informational

### External Dependencies

Configures external services and APIs that the application depends on.

```json
{
  "externalDependencies": [
    {
      "name": "Google Maps API",      // Human-readable service name
      "url": "https://maps.googleapis.com/maps/api/js", // Service endpoint URL
      "timeout": 10000,               // Request timeout in milliseconds
      "critical": true                // Whether failure blocks deployment
    },
    {
      "name": "Google Fonts",
      "url": "https://fonts.googleapis.com",
      "timeout": 5000,
      "critical": false               // Non-critical dependency
    }
  ]
}
```

**Dependency Configuration:**
- **`name`**: Descriptive name for the service
- **`url`**: Endpoint URL to test availability
- **`timeout`**: Maximum time to wait for response
- **`critical`**: Whether failure should block deployment
  - `true`: Deployment fails if service is unavailable
  - `false`: Warning only, deployment can proceed

### Custom Thresholds

Additional performance and quality thresholds beyond Core Web Vitals.

```json
{
  "customThresholds": {
    "performance.bundleSize": 2097152,        // 2MB bundle size limit (bytes)
    "performance.imageOptimization": 0.8,     // 80% image optimization ratio
    "accessibility.colorContrast": 4.5,       // Minimum color contrast ratio
    "performance.cacheHitRatio": 0.9,         // 90% cache hit ratio
    "security.httpsOnly": true                // Require HTTPS for all resources
  }
}
```

### Reporting Options

Controls what information is included in verification reports.

```json
{
  "reportingOptions": {
    "includeScreenshots": true,       // Include screenshots in HTML reports
    "includeNetworkLogs": true,       // Include network request logs
    "includeConsoleErrors": true,     // Include browser console errors
    "detailLevel": "standard"         // Report detail level
  }
}
```

**Detail Levels:**
- **`minimal`**: Basic pass/fail information only
- **`standard`**: Standard reporting with key metrics
- **`detailed`**: Comprehensive reporting with all available data

## Environment-Specific Configuration

### Environment Inheritance

Environments can inherit from other environments to reduce duplication:

```json
{
  "environments": {
    "development": {
      "name": "development",
      "overrides": {
        "buildSettings": {
          "mode": "development",
          "sourceMaps": true,
          "minification": false
        },
        "performanceThresholds": {
          "lcp": 4000,
          "fid": 200,
          "cls": 0.2,
          "lighthousePerformance": 70
        }
      }
    },
    "staging": {
      "name": "staging",
      "extends": "development",       // Inherit from development
      "overrides": {
        // Only specify differences from development
        "buildSettings": {
          "mode": "production",       // Override specific settings
          "minification": true
        },
        "performanceThresholds": {
          "lcp": 3000,                // Slightly stricter than development
          "lighthousePerformance": 85
        }
      }
    }
  }
}
```

This comprehensive configuration reference provides all the information needed to properly configure the verification system for your specific needs and environments.