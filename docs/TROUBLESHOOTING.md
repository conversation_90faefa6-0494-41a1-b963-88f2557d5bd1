# Troubleshooting Guide

This guide provides solutions for common issues encountered when using the Production Deployment Verification System.

## Table of Contents

- [Quick Diagnostics](#quick-diagnostics)
- [Configuration Issues](#configuration-issues)
- [Build Verification Problems](#build-verification-problems)
- [Test Execution Issues](#test-execution-issues)
- [Performance Testing Problems](#performance-testing-problems)
- [Accessibility Testing Issues](#accessibility-testing-issues)
- [PWA Validation Problems](#pwa-validation-problems)
- [External Dependency Issues](#external-dependency-issues)
- [CLI and Reporting Issues](#cli-and-reporting-issues)
- [CI/CD Integration Problems](#cicd-integration-problems)
- [Environment-Specific Issues](#environment-specific-issues)
- [Advanced Troubleshooting](#advanced-troubleshooting)

## Quick Diagnostics

### Health Check
Run the health check to identify common issues:

```bash
npm run verify:health
```

This will check:
- Node.js version compatibility
- npm/yarn availability
- Playwright installation
- Jest configuration
- Required dependencies

### Configuration Validation
Validate your configuration file:

```bash
npm run verify:validate-config
```

### Verbose Mode
Enable verbose logging for detailed error information:

```bash
npm run verify:verbose
```

## Configuration Issues

### Issue: Configuration File Not Found

**Error Message:**
```
❌ Failed to load config from verification.config.json: ENOENT: no such file or directory
```

**Solution:**
```bash
# Initialize default configuration
npm run verify:init

# Or create configuration with specific preset
npx tsx src/verification/cli.ts init --preset production
```

### Issue: Invalid Configuration Format

**Error Message:**
```
❌ Configuration validation failed: LCP threshold must be positive
```

**Solution:**
1. Check configuration syntax:
   ```bash
   npm run verify:validate-config
   ```

2. Common configuration fixes:
   ```json
   {
     "performanceThresholds": {
       "lcp": 2500,     // Must be positive number
       "fid": 100,      // Must be positive number
       "cls": 0.1,      // Must be between 0 and 1
       "lighthousePerformance": 90  // Must be between 0 and 100
     }
   }
   ```

### Issue: Environment Configuration Not Found

**Error Message:**
```
❌ Environment 'staging' not found in configuration
```

**Solution:**
1. List available environments:
   ```bash
   npx tsx src/verification/cli.ts list-environments
   ```

2. Add missing environment to `verification.config.json`:
   ```json
   {
     "environments": {
       "staging": {
         "name": "staging",
         "overrides": {
           "performanceThresholds": {
             "lcp": 3000
           }
         }
       }
     }
   }
   ```

## Build Verification Problems

### Issue: TypeScript Compilation Errors

**Error Message:**
```
❌ Build verification failed: TypeScript compilation errors found
```

**Solution:**
1. Check TypeScript configuration:
   ```bash
   npx tsc --noEmit
   ```

2. Common fixes:
   - Update `tsconfig.json` for proper module resolution
   - Install missing type definitions: `npm install @types/package-name`
   - Fix import/export statements

### Issue: Vite Build Failures

**Error Message:**
```
❌ Build failed: Could not resolve entry module
```

**Solution:**
1. Verify entry point exists:
   ```bash
   ls -la src/main.tsx
   ```

2. Check Vite configuration:
   ```typescript
   // vite.config.ts
   export default defineConfig({
     plugins: [react()],
     build: {
       outDir: 'dist',
       sourcemap: false,
       minify: true
     }
   });
   ```

### Issue: Asset Bundling Problems

**Error Message:**
```
❌ Build verification failed: Asset optimization failed
```

**Solution:**
1. Check for missing assets:
   ```bash
   find src -name "*.png" -o -name "*.jpg" -o -name "*.svg"
   ```

2. Verify asset imports:
   ```typescript
   // Correct asset import
   import logoImage from './assets/logo.png';
   ```

## Test Execution Issues

### Issue: Jest Tests Failing

**Error Message:**
```
❌ Test suite 'unit-tests' failed: Cannot find module
```

**Solution:**
1. Check Jest configuration:
   ```javascript
   // jest.config.js
   export default {
     testEnvironment: 'jsdom',
     setupFilesAfterEnv: ['<rootDir>/src/test/setupTests.ts'],
     moduleNameMapping: {
       '\\.(css|less|scss|sass)$': 'identity-obj-proxy',
       '\\.(jpg|jpeg|png|gif|eot|otf|webp|svg|ttf|woff|woff2)$': '<rootDir>/src/test/mocks/fileMock.js'
     }
   };
   ```

2. Install missing dependencies:
   ```bash
   npm install --save-dev @testing-library/jest-dom @testing-library/react
   ```

### Issue: Playwright Tests Failing

**Error Message:**
```
❌ E2E tests failed: Browser not found
```

**Solution:**
1. Install Playwright browsers:
   ```bash
   npx playwright install
   ```

2. Check Playwright configuration:
   ```typescript
   // playwright.config.ts
   export default defineConfig({
     testDir: './tests',
     use: {
       baseURL: 'http://localhost:8080',
       headless: true
     },
     webServer: {
       command: 'npm run preview',
       port: 8080,
       reuseExistingServer: !process.env.CI
     }
   });
   ```

### Issue: Test Timeouts

**Error Message:**
```
❌ Test suite timed out after 30000ms
```

**Solution:**
1. Increase timeout in configuration:
   ```json
   {
     "testSuites": [
       {
         "name": "e2e-tests",
         "timeout": 120000,
         "retries": 3
       }
     ]
   }
   ```

2. Or use CLI option:
   ```bash
   npm run verify -- --timeout 600
   ```

## Performance Testing Problems

### Issue: Lighthouse Audit Failures

**Error Message:**
```
❌ Performance tests failed: Lighthouse audit failed
```

**Solution:**
1. Check if application is running:
   ```bash
   curl http://localhost:8080
   ```

2. Start application if needed:
   ```bash
   npm run build
   npm run preview
   ```

3. Check for port conflicts:
   ```bash
   lsof -i :8080
   ```

### Issue: Core Web Vitals Threshold Violations

**Error Message:**
```
⚠️ Performance warning: LCP 3200ms exceeds threshold 2500ms
```

**Solution:**
1. Analyze performance issues:
   ```bash
   npm run verify:performance -- --verbose
   ```

2. Common optimizations:
   - Optimize images: Use WebP format, proper sizing
   - Minimize JavaScript bundles
   - Implement code splitting
   - Use CDN for static assets
   - Enable compression (gzip/brotli)

3. Adjust thresholds if necessary:
   ```json
   {
     "performanceThresholds": {
       "lcp": 3000,  // Temporarily increase if needed
       "fid": 150,
       "cls": 0.15
     }
   }
   ```

### Issue: Network Condition Simulation Problems

**Error Message:**
```
❌ Performance test failed: Network throttling not working
```

**Solution:**
1. Check Playwright configuration:
   ```typescript
   // In performance test
   await page.route('**/*', route => {
     route.continue({
       // Simulate slow 3G
       headers: {
         ...route.request().headers(),
         'Cache-Control': 'no-cache'
       }
     });
   });
   ```

## Accessibility Testing Issues

### Issue: axe-core Integration Problems

**Error Message:**
```
❌ Accessibility tests failed: axe-core not found
```

**Solution:**
1. Install required dependencies:
   ```bash
   npm install --save-dev @axe-core/playwright axe-core
   ```

2. Check test setup:
   ```typescript
   import { injectAxe, checkA11y } from '@axe-core/playwright';
   
   test('accessibility test', async ({ page }) => {
     await page.goto('/');
     await injectAxe(page);
     await checkA11y(page);
   });
   ```

### Issue: WCAG Compliance Violations

**Error Message:**
```
❌ Accessibility failed: 5 critical violations found
```

**Solution:**
1. Generate detailed accessibility report:
   ```bash
   npm run verify:accessibility -- --verbose
   ```

2. Common fixes:
   - Add alt text to images: `<img src="..." alt="Description" />`
   - Ensure proper heading hierarchy: h1 → h2 → h3
   - Add ARIA labels: `<button aria-label="Close dialog">×</button>`
   - Improve color contrast ratios
   - Add focus indicators for keyboard navigation

### Issue: Keyboard Navigation Testing

**Error Message:**
```
❌ Keyboard navigation test failed: Focus trap not working
```

**Solution:**
1. Test keyboard navigation manually:
   ```bash
   # Use Tab, Shift+Tab, Enter, Space, Arrow keys
   ```

2. Implement proper focus management:
   ```typescript
   // Focus trap for modals
   useEffect(() => {
     const focusableElements = modal.querySelectorAll(
       'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
     );
     const firstElement = focusableElements[0];
     const lastElement = focusableElements[focusableElements.length - 1];
     
     firstElement?.focus();
   }, []);
   ```

## PWA Validation Problems

### Issue: Service Worker Registration Failures

**Error Message:**
```
❌ PWA validation failed: Service worker not registered
```

**Solution:**
1. Check service worker file exists:
   ```bash
   ls -la public/sw.js
   ```

2. Verify registration code:
   ```typescript
   // In main.tsx or App.tsx
   if ('serviceWorker' in navigator) {
     navigator.serviceWorker.register('/sw.js')
       .then(registration => console.log('SW registered'))
       .catch(error => console.log('SW registration failed'));
   }
   ```

3. Check service worker syntax:
   ```javascript
   // public/sw.js
   self.addEventListener('install', event => {
     console.log('Service worker installing...');
   });
   ```

### Issue: PWA Manifest Validation

**Error Message:**
```
❌ PWA manifest validation failed: Invalid manifest
```

**Solution:**
1. Check manifest file:
   ```bash
   cat public/manifest.json
   ```

2. Ensure proper manifest structure:
   ```json
   {
     "name": "Ice Box Hockey",
     "short_name": "IceBox",
     "start_url": "/",
     "display": "standalone",
     "theme_color": "#000000",
     "background_color": "#ffffff",
     "icons": [
       {
         "src": "/favicon-192.png",
         "sizes": "192x192",
         "type": "image/png"
       }
     ]
   }
   ```

### Issue: Offline Functionality Testing

**Error Message:**
```
❌ Offline test failed: Page not cached
```

**Solution:**
1. Check service worker caching strategy:
   ```javascript
   // public/sw.js
   const CACHE_NAME = 'icebox-v1';
   const urlsToCache = [
     '/',
     '/static/js/bundle.js',
     '/static/css/main.css'
   ];
   
   self.addEventListener('install', event => {
     event.waitUntil(
       caches.open(CACHE_NAME)
         .then(cache => cache.addAll(urlsToCache))
     );
   });
   ```

## External Dependency Issues

### Issue: Google Maps API Failures

**Error Message:**
```
❌ External dependency check failed: Google Maps API timeout
```

**Solution:**
1. Check API key configuration:
   ```bash
   echo $GOOGLE_MAPS_API_KEY
   ```

2. Verify API key permissions:
   - Enable Maps JavaScript API
   - Set up proper domain restrictions
   - Check billing account status

3. Test API endpoint manually:
   ```bash
   curl "https://maps.googleapis.com/maps/api/js?key=YOUR_API_KEY"
   ```

### Issue: CDN Resource Availability

**Error Message:**
```
❌ CDN resource check failed: fonts.googleapis.com timeout
```

**Solution:**
1. Check network connectivity:
   ```bash
   ping fonts.googleapis.com
   ```

2. Test with different CDN:
   ```html
   <!-- Fallback to different CDN -->
   <link href="https://fonts.bunny.net/css?family=Inter:400,500,600,700" rel="stylesheet">
   ```

3. Implement local fallbacks:
   ```css
   @font-face {
     font-family: 'Inter';
     src: url('./assets/fonts/inter.woff2') format('woff2');
     font-display: swap;
   }
   ```

## CLI and Reporting Issues

### Issue: CLI Command Not Found

**Error Message:**
```
bash: verify-deployment: command not found
```

**Solution:**
1. Use npm scripts instead:
   ```bash
   npm run verify
   ```

2. Or run directly with tsx:
   ```bash
   npx tsx src/verification/cli.ts run
   ```

3. For global installation:
   ```bash
   npm install -g .
   ```

### Issue: Report Generation Failures

**Error Message:**
```
❌ Failed to generate HTML report: Template not found
```

**Solution:**
1. Check if all dependencies are installed:
   ```bash
   npm install
   ```

2. Verify report generator:
   ```bash
   npm run verify -- --output json --output-file test-report.json
   ```

### Issue: Permission Denied on Scripts

**Error Message:**
```
bash: ./scripts/ci-verification.sh: Permission denied
```

**Solution:**
```bash
chmod +x scripts/ci-verification.sh
chmod +x scripts/*.sh
```

## CI/CD Integration Problems

### Issue: GitHub Actions Failures

**Error Message:**
```
❌ CI verification failed: Process completed with exit code 1
```

**Solution:**
1. Check GitHub Actions logs for specific errors
2. Ensure all secrets are configured:
   - `GOOGLE_MAPS_API_KEY`
   - Other required environment variables

3. Update workflow file:
   ```yaml
   # .github/workflows/verification.yml
   - name: Install dependencies
     run: npm ci
   
   - name: Install Playwright browsers
     run: npx playwright install --with-deps
   
   - name: Run verification
     run: npm run verify:ci
   ```

### Issue: Docker Container Issues

**Error Message:**
```
❌ Container verification failed: Port 8080 not accessible
```

**Solution:**
1. Check Docker port mapping:
   ```bash
   docker run -p 8080:8080 your-app
   ```

2. Verify container health:
   ```bash
   docker ps
   docker logs container-id
   ```

### Issue: Environment Variable Problems

**Error Message:**
```
❌ Configuration error: Required environment variable not set
```

**Solution:**
1. Set required environment variables:
   ```bash
   export NODE_ENV=production
   export GOOGLE_MAPS_API_KEY=your-key
   ```

2. Use .env file for local development:
   ```bash
   # .env
   NODE_ENV=development
   GOOGLE_MAPS_API_KEY=your-development-key
   ```

## Environment-Specific Issues

### Development Environment

**Common Issues:**
- Hot reload conflicts with verification
- Development server port conflicts
- Source map generation issues

**Solutions:**
```bash
# Use development-specific configuration
npm run verify -- --environment development

# Skip performance tests in development
npm run verify -- --skip-stages performance
```

### Staging Environment

**Common Issues:**
- Production-like settings with development data
- SSL certificate issues
- Database connection problems

**Solutions:**
```json
{
  "environments": {
    "staging": {
      "overrides": {
        "performanceThresholds": {
          "lcp": 3000,
          "lighthousePerformance": 85
        }
      }
    }
  }
}
```

### Production Environment

**Common Issues:**
- Strict performance requirements
- Security restrictions
- Limited debugging information

**Solutions:**
- Use production-specific thresholds
- Enable comprehensive logging
- Implement proper error handling

## Advanced Troubleshooting

### Debug Mode Configuration

Enable maximum debugging information:

```json
{
  "reportingOptions": {
    "includeScreenshots": true,
    "includeNetworkLogs": true,
    "includeConsoleErrors": true,
    "detailLevel": "detailed"
  }
}
```

### Memory and Performance Issues

**Issue: Out of memory errors**

**Solution:**
```bash
# Increase Node.js memory limit
export NODE_OPTIONS="--max-old-space-size=4096"
npm run verify
```

### Network Debugging

**Issue: Network-related failures**

**Solution:**
1. Enable network logging:
   ```bash
   DEBUG=pw:api npm run verify:verbose
   ```

2. Check proxy settings:
   ```bash
   echo $HTTP_PROXY
   echo $HTTPS_PROXY
   ```

### File System Issues

**Issue: File permission or path problems**

**Solution:**
1. Check file permissions:
   ```bash
   ls -la verification.config.json
   chmod 644 verification.config.json
   ```

2. Verify working directory:
   ```bash
   pwd
   ls -la
   ```

## Getting Help

### Diagnostic Information

When reporting issues, include:

1. **System Information:**
   ```bash
   node --version
   npm --version
   npx playwright --version
   ```

2. **Configuration:**
   ```bash
   npm run verify:validate-config
   ```

3. **Health Check:**
   ```bash
   npm run verify:health
   ```

4. **Verbose Output:**
   ```bash
   npm run verify:verbose 2>&1 | tee verification-debug.log
   ```

### Common Commands for Debugging

```bash
# Full diagnostic run
npm run verify:health
npm run verify:validate-config
npm run verify:verbose

# Test individual components
npm run verify:build
npm run verify:test
npm run verify:performance
npm run verify:accessibility

# Generate reports for analysis
npm run verify:json
npm run verify:html
```

### Support Resources

- **Documentation**: Check all documentation files in the `docs/` directory
- **Configuration Examples**: See `verification.config.json` for complete examples
- **Test Examples**: Review test files in `src/verification/__tests__/`
- **CLI Help**: Run `npx tsx src/verification/cli.ts --help` for command-specific help

Remember to always run the health check first and validate your configuration before diving into specific troubleshooting steps.