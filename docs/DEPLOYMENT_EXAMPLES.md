# Deployment Examples

This document provides real-world examples of using the Production Deployment Verification System in different deployment scenarios.

## Table of Contents

- [Basic Deployment Workflow](#basic-deployment-workflow)
- [GitHub Actions Integration](#github-actions-integration)
- [GitLab CI/CD Integration](#gitlab-cicd-integration)
- [Jenkins Pipeline Integration](#jenkins-pipeline-integration)
- [Docker-based Deployment](#docker-based-deployment)
- [Multi-Environment Deployment](#multi-environment-deployment)
- [Automated Deployment Gates](#automated-deployment-gates)
- [Performance Regression Detection](#performance-regression-detection)
- [Accessibility Compliance Workflow](#accessibility-compliance-workflow)
- [Custom Verification Scenarios](#custom-verification-scenarios)

## Basic Deployment Workflow

### Local Development Workflow

```bash
# 1. Initialize verification configuration
npm run verify:init

# 2. Run quick verification during development
npm run verify:build
npm run verify:test

# 3. Full verification before committing
npm run verify

# 4. Generate report for review
npm run verify:html
```

### Pre-deployment Checklist

```bash
#!/bin/bash
# pre-deploy.sh

echo "🚀 Starting pre-deployment verification..."

# 1. Build verification
echo "📦 Verifying build..."
npm run verify:build || exit 1

# 2. Test verification
echo "🧪 Running tests..."
npm run verify:test || exit 1

# 3. Performance check
echo "⚡ Checking performance..."
npm run verify:performance || exit 1

# 4. Accessibility check
echo "♿ Checking accessibility..."
npm run verify:accessibility || exit 1

# 5. Full verification
echo "🔍 Running full verification..."
npm run verify:ci || exit 1

echo "✅ Pre-deployment verification completed successfully!"
```

## GitHub Actions Integration

### Complete Workflow Example

```yaml
# .github/workflows/deployment-verification.yml
name: Production Deployment Verification

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

env:
  NODE_VERSION: '18'
  GOOGLE_MAPS_API_KEY: ${{ secrets.GOOGLE_MAPS_API_KEY }}

jobs:
  verification:
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        node-version: [18, 20]
        environment: [staging, production]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Setup Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v4
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'
    
    - name: Install dependencies
      run: |
        npm ci
        npx playwright install --with-deps
    
    - name: Initialize verification configuration
      run: npm run verify:init
    
    - name: Run verification pipeline
      run: |
        npm run verify -- \
          --environment ${{ matrix.environment }} \
          --output json \
          --output-file verification-report-${{ matrix.environment }}.json \
          --ci
    
    - name: Generate HTML report
      if: always()
      run: |
        npx tsx src/verification/cli.ts report \
          --input verification-report-${{ matrix.environment }}.json \
          --output html \
          --output-file verification-report-${{ matrix.environment }}.html
    
    - name: Upload verification reports
      if: always()
      uses: actions/upload-artifact@v4
      with:
        name: verification-reports-${{ matrix.node-version }}-${{ matrix.environment }}
        path: |
          verification-report-*.json
          verification-report-*.html
    
    - name: Comment PR with results
      if: github.event_name == 'pull_request'
      uses: actions/github-script@v7
      with:
        script: |
          const fs = require('fs');
          const reportPath = `verification-report-${{ matrix.environment }}.json`;
          
          if (fs.existsSync(reportPath)) {
            const report = JSON.parse(fs.readFileSync(reportPath, 'utf8'));
            const status = report.overallStatus;
            const deploymentReady = report.deploymentReady;
            
            const statusIcon = status === 'passed' ? '✅' : 
                              status === 'warning' ? '⚠️' : '❌';
            
            const comment = `
            ## ${statusIcon} Verification Report - ${{ matrix.environment }}
            
            **Overall Status:** ${status.toUpperCase()}
            **Deployment Ready:** ${deploymentReady ? 'YES' : 'NO'}
            **Node.js Version:** ${{ matrix.node-version }}
            
            ### Results Summary
            - **Build:** ${report.buildVerification.success ? '✅' : '❌'}
            - **Tests:** ${report.testResults.filter(t => t.passed).length}/${report.testResults.length} passed
            - **Performance:** LCP ${report.performanceMetrics.lcp}ms, FID ${report.performanceMetrics.fid}ms
            - **Accessibility:** ${report.accessibilityResults.compliant ? '✅' : '❌'} WCAG compliant
            - **PWA:** ${report.pwaValidation.serviceWorkerRegistered ? '✅' : '❌'} Service Worker
            
            [View detailed report](https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }})
            `;
            
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: comment
            });
          }
    
    - name: Deployment gate
      if: matrix.environment == 'production'
      run: |
        # Check if verification passed for production deployment
        if [ ! -f verification-report-production.json ]; then
          echo "❌ Verification report not found"
          exit 1
        fi
        
        DEPLOYMENT_READY=$(cat verification-report-production.json | jq -r '.deploymentReady')
        if [ "$DEPLOYMENT_READY" != "true" ]; then
          echo "❌ Verification failed - not ready for production deployment"
          exit 1
        fi
        
        echo "✅ Verification passed - ready for production deployment"

  deploy:
    needs: verification
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main' && needs.verification.result == 'success'
    
    steps:
    - name: Deploy to production
      run: |
        echo "🚀 Deploying to production..."
        # Add your deployment commands here
```

### Simplified Workflow for Small Projects

```yaml
# .github/workflows/simple-verification.yml
name: Simple Verification

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  verify:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: Install and verify
      run: |
        npm ci
        npx playwright install --with-deps
        npm run verify:ci
    
    - name: Upload report
      if: always()
      uses: actions/upload-artifact@v4
      with:
        name: verification-report
        path: verification-report.html
```

## GitLab CI/CD Integration

### Complete Pipeline Example

```yaml
# .gitlab-ci.yml
stages:
  - build
  - test
  - verify
  - deploy

variables:
  NODE_VERSION: "18"
  GOOGLE_MAPS_API_KEY: $GOOGLE_MAPS_API_KEY

cache:
  paths:
    - node_modules/
    - .npm/

before_script:
  - npm ci --cache .npm --prefer-offline

build:
  stage: build
  image: node:$NODE_VERSION
  script:
    - npm run build
  artifacts:
    paths:
      - dist/
    expire_in: 1 hour

test:
  stage: test
  image: node:$NODE_VERSION
  script:
    - npm run test
  artifacts:
    reports:
      junit: test-results.xml
    paths:
      - coverage/
    expire_in: 1 hour

verify:staging:
  stage: verify
  image: mcr.microsoft.com/playwright:v1.40.0-focal
  script:
    - npm run verify:init
    - npm run verify -- --environment staging --output json --output-file verification-staging.json
    - npx tsx src/verification/cli.ts report --input verification-staging.json --output html --output-file verification-staging.html
  artifacts:
    paths:
      - verification-staging.json
      - verification-staging.html
    reports:
      junit: verification-staging.xml
    expire_in: 1 week
  only:
    - develop
    - merge_requests

verify:production:
  stage: verify
  image: mcr.microsoft.com/playwright:v1.40.0-focal
  script:
    - npm run verify:init
    - npm run verify:ci --output json --output-file verification-production.json
    - npx tsx src/verification/cli.ts report --input verification-production.json --output html --output-file verification-production.html
    # Check deployment readiness
    - |
      DEPLOYMENT_READY=$(cat verification-production.json | jq -r '.deploymentReady')
      if [ "$DEPLOYMENT_READY" != "true" ]; then
        echo "❌ Verification failed - not ready for production deployment"
        exit 1
      fi
  artifacts:
    paths:
      - verification-production.json
      - verification-production.html
    expire_in: 1 month
  only:
    - main

deploy:production:
  stage: deploy
  image: node:$NODE_VERSION
  script:
    - echo "🚀 Deploying to production..."
    # Add your deployment commands here
  dependencies:
    - verify:production
  only:
    - main
  when: manual  # Require manual approval
```

## Jenkins Pipeline Integration

### Declarative Pipeline Example

```groovy
// Jenkinsfile
pipeline {
    agent any
    
    environment {
        NODE_VERSION = '18'
        GOOGLE_MAPS_API_KEY = credentials('google-maps-api-key')
    }
    
    stages {
        stage('Setup') {
            steps {
                // Install Node.js
                nodejs(nodeJSInstallationName: 'Node 18') {
                    sh 'npm ci'
                    sh 'npx playwright install --with-deps'
                }
            }
        }
        
        stage('Build Verification') {
            steps {
                nodejs(nodeJSInstallationName: 'Node 18') {
                    sh 'npm run verify:build'
                }
            }
        }
        
        stage('Test Verification') {
            parallel {
                stage('Unit Tests') {
                    steps {
                        nodejs(nodeJSInstallationName: 'Node 18') {
                            sh 'npm run verify:test -- --test-types unit'
                        }
                    }
                }
                stage('Integration Tests') {
                    steps {
                        nodejs(nodeJSInstallationName: 'Node 18') {
                            sh 'npm run verify:test -- --test-types integration'
                        }
                    }
                }
                stage('E2E Tests') {
                    steps {
                        nodejs(nodeJSInstallationName: 'Node 18') {
                            sh 'npm run verify:test -- --test-types e2e'
                        }
                    }
                }
            }
        }
        
        stage('Performance Verification') {
            steps {
                nodejs(nodeJSInstallationName: 'Node 18') {
                    sh 'npm run verify:performance'
                }
            }
        }
        
        stage('Accessibility Verification') {
            steps {
                nodejs(nodeJSInstallationName: 'Node 18') {
                    sh 'npm run verify:accessibility'
                }
            }
        }
        
        stage('Full Verification') {
            steps {
                nodejs(nodeJSInstallationName: 'Node 18') {
                    script {
                        def environment = env.BRANCH_NAME == 'main' ? 'production' : 'staging'
                        sh """
                            npm run verify -- \
                                --environment ${environment} \
                                --output json \
                                --output-file verification-${environment}.json \
                                --ci
                        """
                        
                        // Generate HTML report
                        sh """
                            npx tsx src/verification/cli.ts report \
                                --input verification-${environment}.json \
                                --output html \
                                --output-file verification-${environment}.html
                        """
                        
                        // Check deployment readiness for production
                        if (environment == 'production') {
                            def report = readJSON file: "verification-${environment}.json"
                            if (!report.deploymentReady) {
                                error("❌ Verification failed - not ready for production deployment")
                            }
                        }
                    }
                }
            }
        }
        
        stage('Deploy') {
            when {
                branch 'main'
            }
            steps {
                script {
                    // Manual approval for production deployment
                    input message: 'Deploy to production?', ok: 'Deploy'
                    
                    nodejs(nodeJSInstallationName: 'Node 18') {
                        sh 'echo "🚀 Deploying to production..."'
                        // Add your deployment commands here
                    }
                }
            }
        }
    }
    
    post {
        always {
            // Archive verification reports
            archiveArtifacts artifacts: 'verification-*.json,verification-*.html', allowEmptyArchive: true
            
            // Publish HTML reports
            publishHTML([
                allowMissing: false,
                alwaysLinkToLastBuild: true,
                keepAll: true,
                reportDir: '.',
                reportFiles: 'verification-*.html',
                reportName: 'Verification Report'
            ])
        }
        
        failure {
            // Send notification on failure
            emailext (
                subject: "❌ Verification Failed: ${env.JOB_NAME} - ${env.BUILD_NUMBER}",
                body: """
                    Verification failed for ${env.JOB_NAME} build ${env.BUILD_NUMBER}.
                    
                    Check the verification report: ${env.BUILD_URL}Verification_Report/
                    
                    Build URL: ${env.BUILD_URL}
                """,
                to: "${env.CHANGE_AUTHOR_EMAIL}"
            )
        }
        
        success {
            // Send notification on success for production deployments
            script {
                if (env.BRANCH_NAME == 'main') {
                    emailext (
                        subject: "✅ Production Deployment Ready: ${env.JOB_NAME} - ${env.BUILD_NUMBER}",
                        body: """
                            Production verification completed successfully for ${env.JOB_NAME} build ${env.BUILD_NUMBER}.
                            
                            The application is ready for production deployment.
                            
                            Verification Report: ${env.BUILD_URL}Verification_Report/
                        """,
                        to: "${env.CHANGE_AUTHOR_EMAIL}"
                    )
                }
            }
        }
    }
}
```

## Docker-based Deployment

### Multi-stage Dockerfile with Verification

```dockerfile
# Dockerfile
FROM node:18-alpine AS base
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

FROM node:18-alpine AS build
WORKDIR /app
COPY package*.json ./
RUN npm ci
COPY . .
RUN npm run build

FROM mcr.microsoft.com/playwright:v1.40.0-focal AS verify
WORKDIR /app
COPY package*.json ./
RUN npm ci
COPY . .
COPY --from=build /app/dist ./dist

# Run verification
RUN npm run verify:init
RUN npm run verify:ci --output json --output-file verification-report.json

# Check deployment readiness
RUN node -e "
const report = require('./verification-report.json');
if (!report.deploymentReady) {
  console.error('❌ Verification failed - not ready for deployment');
  process.exit(1);
}
console.log('✅ Verification passed - ready for deployment');
"

FROM nginx:alpine AS production
COPY --from=build /app/dist /usr/share/nginx/html
COPY --from=verify /app/verification-report.json /usr/share/nginx/html/
COPY nginx.conf /etc/nginx/nginx.conf
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

### Docker Compose for Development

```yaml
# docker-compose.yml
version: '3.8'

services:
  app:
    build:
      context: .
      target: build
    ports:
      - "3000:3000"
    volumes:
      - .:/app
      - /app/node_modules
    environment:
      - NODE_ENV=development
      - GOOGLE_MAPS_API_KEY=${GOOGLE_MAPS_API_KEY}
    command: npm run dev

  verify:
    build:
      context: .
      target: verify
    volumes:
      - .:/app
      - /app/node_modules
    environment:
      - NODE_ENV=development
      - GOOGLE_MAPS_API_KEY=${GOOGLE_MAPS_API_KEY}
    command: npm run verify -- --environment development --watch
    depends_on:
      - app

  nginx:
    image: nginx:alpine
    ports:
      - "8080:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./dist:/usr/share/nginx/html
    depends_on:
      - app
```

## Multi-Environment Deployment

### Environment-specific Verification

```bash
#!/bin/bash
# deploy-multi-env.sh

ENVIRONMENT=${1:-staging}
BRANCH=${2:-develop}

echo "🚀 Deploying to $ENVIRONMENT from $BRANCH branch..."

# 1. Checkout appropriate branch
git checkout $BRANCH
git pull origin $BRANCH

# 2. Install dependencies
npm ci
npx playwright install --with-deps

# 3. Run environment-specific verification
echo "🔍 Running verification for $ENVIRONMENT..."
npm run verify -- \
  --environment $ENVIRONMENT \
  --output json \
  --output-file verification-$ENVIRONMENT.json \
  --ci

# 4. Check deployment readiness
DEPLOYMENT_READY=$(cat verification-$ENVIRONMENT.json | jq -r '.deploymentReady')
if [ "$DEPLOYMENT_READY" != "true" ]; then
  echo "❌ Verification failed for $ENVIRONMENT - not ready for deployment"
  exit 1
fi

# 5. Generate HTML report
npx tsx src/verification/cli.ts report \
  --input verification-$ENVIRONMENT.json \
  --output html \
  --output-file verification-$ENVIRONMENT.html

echo "✅ Verification passed for $ENVIRONMENT - proceeding with deployment"

# 6. Deploy based on environment
case $ENVIRONMENT in
  "development")
    echo "🚀 Deploying to development..."
    # Development deployment commands
    ;;
  "staging")
    echo "🚀 Deploying to staging..."
    # Staging deployment commands
    ;;
  "production")
    echo "🚀 Deploying to production..."
    # Production deployment commands
    # Require additional confirmation
    read -p "Are you sure you want to deploy to production? (y/N) " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
      # Production deployment commands
      echo "Production deployment completed!"
    else
      echo "Production deployment cancelled."
      exit 1
    fi
    ;;
  *)
    echo "❌ Unknown environment: $ENVIRONMENT"
    exit 1
    ;;
esac
```

### Kubernetes Deployment with Verification

```yaml
# k8s-deploy.yml
apiVersion: batch/v1
kind: Job
metadata:
  name: verification-job
spec:
  template:
    spec:
      containers:
      - name: verification
        image: mcr.microsoft.com/playwright:v1.40.0-focal
        command: ["/bin/bash"]
        args:
          - -c
          - |
            cd /app
            npm ci
            npm run verify:init
            npm run verify:ci --output json --output-file /shared/verification-report.json
            
            # Check deployment readiness
            DEPLOYMENT_READY=$(cat /shared/verification-report.json | jq -r '.deploymentReady')
            if [ "$DEPLOYMENT_READY" != "true" ]; then
              echo "❌ Verification failed - not ready for deployment"
              exit 1
            fi
            
            echo "✅ Verification passed - ready for deployment"
        volumeMounts:
        - name: app-source
          mountPath: /app
        - name: shared-data
          mountPath: /shared
        env:
        - name: GOOGLE_MAPS_API_KEY
          valueFrom:
            secretKeyRef:
              name: app-secrets
              key: google-maps-api-key
      volumes:
      - name: app-source
        configMap:
          name: app-source
      - name: shared-data
        emptyDir: {}
      restartPolicy: Never
  backoffLimit: 3

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: app-deployment
spec:
  replicas: 3
  selector:
    matchLabels:
      app: ice-box-hockey
  template:
    metadata:
      labels:
        app: ice-box-hockey
    spec:
      initContainers:
      - name: wait-for-verification
        image: busybox
        command: ['sh', '-c', 'until [ -f /shared/verification-report.json ]; do sleep 5; done']
        volumeMounts:
        - name: shared-data
          mountPath: /shared
      containers:
      - name: app
        image: ice-box-hockey:latest
        ports:
        - containerPort: 80
        volumeMounts:
        - name: shared-data
          mountPath: /shared
      volumes:
      - name: shared-data
        emptyDir: {}
```

## Automated Deployment Gates

### Deployment Decision Script

```typescript
// scripts/deployment-decision.ts
import { readFileSync } from 'fs';
import { VerificationReport } from '../src/verification/types';

interface DeploymentCriteria {
  buildMustPass: boolean;
  minimumTestPassRate: number;
  criticalTestsMustPass: string[];
  performanceThresholds: {
    lcp: number;
    fid: number;
    cls: number;
    lighthousePerformance: number;
  };
  accessibilityMustPass: boolean;
  maxCriticalA11yViolations: number;
  allowWarningDeployment: boolean;
  requireManualApprovalOnWarnings: boolean;
}

class DeploymentDecisionEngine {
  private criteria: DeploymentCriteria;

  constructor(criteriaPath: string = 'deployment-criteria.json') {
    this.criteria = JSON.parse(readFileSync(criteriaPath, 'utf-8'));
  }

  async evaluateDeployment(reportPath: string): Promise<{
    canDeploy: boolean;
    requiresManualApproval: boolean;
    reasons: string[];
    recommendations: string[];
  }> {
    const report: VerificationReport = JSON.parse(readFileSync(reportPath, 'utf-8'));
    
    const reasons: string[] = [];
    const recommendations: string[] = [];
    let canDeploy = true;
    let requiresManualApproval = false;

    // Check build status
    if (this.criteria.buildMustPass && !report.buildVerification.success) {
      canDeploy = false;
      reasons.push('Build verification failed');
      recommendations.push('Fix build errors before deployment');
    }

    // Check test pass rate
    const totalTests = report.testResults.reduce((sum, result) => sum + result.testCount, 0);
    const passedTests = report.testResults
      .filter(result => result.passed)
      .reduce((sum, result) => sum + result.testCount, 0);
    
    const passRate = totalTests > 0 ? passedTests / totalTests : 0;
    
    if (passRate < this.criteria.minimumTestPassRate) {
      canDeploy = false;
      reasons.push(`Test pass rate ${(passRate * 100).toFixed(1)}% below minimum ${(this.criteria.minimumTestPassRate * 100)}%`);
      recommendations.push('Fix failing tests before deployment');
    }

    // Check critical tests
    for (const criticalTest of this.criteria.criticalTestsMustPass) {
      const testResult = report.testResults.find(result => 
        result.name?.includes(criticalTest) || result.description?.includes(criticalTest)
      );
      
      if (!testResult || !testResult.passed) {
        canDeploy = false;
        reasons.push(`Critical test '${criticalTest}' failed`);
        recommendations.push(`Fix critical test: ${criticalTest}`);
      }
    }

    // Check performance thresholds
    const perf = report.performanceMetrics;
    if (perf.lcp > this.criteria.performanceThresholds.lcp) {
      if (this.criteria.allowWarningDeployment) {
        requiresManualApproval = true;
        reasons.push(`LCP ${perf.lcp}ms exceeds threshold ${this.criteria.performanceThresholds.lcp}ms`);
      } else {
        canDeploy = false;
        reasons.push(`LCP ${perf.lcp}ms exceeds threshold ${this.criteria.performanceThresholds.lcp}ms`);
      }
      recommendations.push('Optimize loading performance to improve LCP');
    }

    if (perf.fid > this.criteria.performanceThresholds.fid) {
      if (this.criteria.allowWarningDeployment) {
        requiresManualApproval = true;
        reasons.push(`FID ${perf.fid}ms exceeds threshold ${this.criteria.performanceThresholds.fid}ms`);
      } else {
        canDeploy = false;
        reasons.push(`FID ${perf.fid}ms exceeds threshold ${this.criteria.performanceThresholds.fid}ms`);
      }
      recommendations.push('Optimize JavaScript execution to improve FID');
    }

    // Check accessibility
    if (this.criteria.accessibilityMustPass && !report.accessibilityResults.compliant) {
      const criticalViolations = report.accessibilityResults.violations
        .filter(v => v.impact === 'critical').length;
      
      if (criticalViolations > this.criteria.maxCriticalA11yViolations) {
        canDeploy = false;
        reasons.push(`${criticalViolations} critical accessibility violations found`);
        recommendations.push('Fix critical accessibility violations before deployment');
      }
    }

    // Determine if manual approval is required
    if (requiresManualApproval && this.criteria.requireManualApprovalOnWarnings) {
      requiresManualApproval = true;
    }

    return {
      canDeploy,
      requiresManualApproval,
      reasons,
      recommendations
    };
  }
}

// CLI usage
async function main() {
  const reportPath = process.argv[2] || 'verification-report.json';
  const engine = new DeploymentDecisionEngine();
  
  try {
    const decision = await engine.evaluateDeployment(reportPath);
    
    console.log('🎯 Deployment Decision Analysis');
    console.log('================================');
    console.log(`Can Deploy: ${decision.canDeploy ? '✅ YES' : '❌ NO'}`);
    console.log(`Manual Approval Required: ${decision.requiresManualApproval ? '⚠️ YES' : '✅ NO'}`);
    
    if (decision.reasons.length > 0) {
      console.log('\n📋 Reasons:');
      decision.reasons.forEach(reason => console.log(`   • ${reason}`));
    }
    
    if (decision.recommendations.length > 0) {
      console.log('\n💡 Recommendations:');
      decision.recommendations.forEach(rec => console.log(`   • ${rec}`));
    }
    
    // Set exit code for CI/CD
    if (!decision.canDeploy) {
      process.exit(1);
    } else if (decision.requiresManualApproval) {
      process.exit(2); // Special exit code for manual approval
    } else {
      process.exit(0);
    }
    
  } catch (error) {
    console.error('❌ Error analyzing deployment decision:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

export { DeploymentDecisionEngine };
```

### Deployment Criteria Configuration

```json
{
  "buildMustPass": true,
  "minimumTestPassRate": 0.95,
  "criticalTestsMustPass": [
    "authentication",
    "payment",
    "user-registration",
    "core-navigation"
  ],
  "performanceThresholds": {
    "lcp": 2500,
    "fid": 100,
    "cls": 0.1,
    "lighthousePerformance": 90
  },
  "accessibilityMustPass": true,
  "maxCriticalA11yViolations": 0,
  "allowWarningDeployment": true,
  "requireManualApprovalOnWarnings": false,
  "customRules": {
    "bundleSizeLimit": 2097152,
    "imageOptimizationThreshold": 0.8,
    "cacheHitRatio": 0.9
  },
  "notifications": {
    "slack": {
      "webhook": "https://hooks.slack.com/services/...",
      "channel": "#deployments"
    },
    "email": {
      "recipients": ["<EMAIL>"],
      "smtp": {
        "host": "smtp.example.com",
        "port": 587
      }
    }
  }
}
```

## Performance Regression Detection

### Performance Trend Analysis

```typescript
// scripts/performance-trend-analysis.ts
import { readFileSync, writeFileSync, existsSync } from 'fs';
import { VerificationReport } from '../src/verification/types';

interface PerformanceHistory {
  timestamp: string;
  commit: string;
  branch: string;
  metrics: {
    lcp: number;
    fid: number;
    cls: number;
    lighthousePerformance: number;
  };
}

class PerformanceTrendAnalyzer {
  private historyFile = 'performance-history.json';
  private history: PerformanceHistory[] = [];

  constructor() {
    this.loadHistory();
  }

  private loadHistory(): void {
    if (existsSync(this.historyFile)) {
      this.history = JSON.parse(readFileSync(this.historyFile, 'utf-8'));
    }
  }

  private saveHistory(): void {
    writeFileSync(this.historyFile, JSON.stringify(this.history, null, 2));
  }

  addReport(report: VerificationReport, commit: string, branch: string): void {
    const entry: PerformanceHistory = {
      timestamp: report.timestamp.toISOString(),
      commit,
      branch,
      metrics: {
        lcp: report.performanceMetrics.lcp,
        fid: report.performanceMetrics.fid,
        cls: report.performanceMetrics.cls,
        lighthousePerformance: report.performanceMetrics.lighthouse.performance
      }
    };

    this.history.push(entry);
    this.saveHistory();
  }

  detectRegressions(currentReport: VerificationReport, threshold: number = 0.1): {
    hasRegression: boolean;
    regressions: Array<{
      metric: string;
      current: number;
      baseline: number;
      change: number;
      changePercent: number;
    }>;
  } {
    if (this.history.length < 5) {
      return { hasRegression: false, regressions: [] };
    }

    // Calculate baseline from last 5 successful runs
    const recentHistory = this.history.slice(-5);
    const baseline = {
      lcp: recentHistory.reduce((sum, h) => sum + h.metrics.lcp, 0) / recentHistory.length,
      fid: recentHistory.reduce((sum, h) => sum + h.metrics.fid, 0) / recentHistory.length,
      cls: recentHistory.reduce((sum, h) => sum + h.metrics.cls, 0) / recentHistory.length,
      lighthousePerformance: recentHistory.reduce((sum, h) => sum + h.metrics.lighthousePerformance, 0) / recentHistory.length
    };

    const current = currentReport.performanceMetrics;
    const regressions = [];

    // Check LCP regression
    const lcpChange = current.lcp - baseline.lcp;
    const lcpChangePercent = (lcpChange / baseline.lcp) * 100;
    if (lcpChangePercent > threshold * 100) {
      regressions.push({
        metric: 'LCP',
        current: current.lcp,
        baseline: baseline.lcp,
        change: lcpChange,
        changePercent: lcpChangePercent
      });
    }

    // Check FID regression
    const fidChange = current.fid - baseline.fid;
    const fidChangePercent = (fidChange / baseline.fid) * 100;
    if (fidChangePercent > threshold * 100) {
      regressions.push({
        metric: 'FID',
        current: current.fid,
        baseline: baseline.fid,
        change: fidChange,
        changePercent: fidChangePercent
      });
    }

    // Check CLS regression
    const clsChange = current.cls - baseline.cls;
    const clsChangePercent = (clsChange / baseline.cls) * 100;
    if (clsChangePercent > threshold * 100) {
      regressions.push({
        metric: 'CLS',
        current: current.cls,
        baseline: baseline.cls,
        change: clsChange,
        changePercent: clsChangePercent
      });
    }

    // Check Lighthouse regression (inverse - lower is worse)
    const lighthouseChange = baseline.lighthousePerformance - current.lighthouse.performance;
    const lighthouseChangePercent = (lighthouseChange / baseline.lighthousePerformance) * 100;
    if (lighthouseChangePercent > threshold * 100) {
      regressions.push({
        metric: 'Lighthouse Performance',
        current: current.lighthouse.performance,
        baseline: baseline.lighthousePerformance,
        change: -lighthouseChange,
        changePercent: -lighthouseChangePercent
      });
    }

    return {
      hasRegression: regressions.length > 0,
      regressions
    };
  }

  generateTrendReport(): string {
    if (this.history.length === 0) {
      return 'No performance history available.';
    }

    const recent = this.history.slice(-10);
    let report = '📈 Performance Trend Report\n';
    report += '============================\n\n';

    report += 'Recent Performance History:\n';
    recent.forEach((entry, index) => {
      report += `${index + 1}. ${entry.timestamp.substring(0, 10)} (${entry.commit.substring(0, 7)})\n`;
      report += `   LCP: ${entry.metrics.lcp}ms, FID: ${entry.metrics.fid}ms, CLS: ${entry.metrics.cls}, Lighthouse: ${entry.metrics.lighthousePerformance}\n`;
    });

    return report;
  }
}

// Usage in CI/CD
async function main() {
  const reportPath = process.argv[2] || 'verification-report.json';
  const commit = process.env.GITHUB_SHA || process.env.CI_COMMIT_SHA || 'unknown';
  const branch = process.env.GITHUB_REF_NAME || process.env.CI_COMMIT_REF_NAME || 'unknown';

  const analyzer = new PerformanceTrendAnalyzer();
  const report: VerificationReport = JSON.parse(readFileSync(reportPath, 'utf-8'));

  // Add current report to history
  analyzer.addReport(report, commit, branch);

  // Check for regressions
  const regressionAnalysis = analyzer.detectRegressions(report, 0.1); // 10% threshold

  if (regressionAnalysis.hasRegression) {
    console.log('⚠️ Performance Regression Detected!');
    console.log('===================================');
    
    regressionAnalysis.regressions.forEach(regression => {
      console.log(`${regression.metric}:`);
      console.log(`  Current: ${regression.current}`);
      console.log(`  Baseline: ${regression.baseline.toFixed(2)}`);
      console.log(`  Change: ${regression.change > 0 ? '+' : ''}${regression.change.toFixed(2)} (${regression.changePercent > 0 ? '+' : ''}${regression.changePercent.toFixed(1)}%)`);
      console.log('');
    });

    // Generate trend report
    console.log(analyzer.generateTrendReport());

    // Exit with warning code
    process.exit(2);
  } else {
    console.log('✅ No performance regressions detected');
    process.exit(0);
  }
}

if (require.main === module) {
  main();
}
```

These deployment examples provide comprehensive real-world scenarios for integrating the verification system into various CI/CD pipelines and deployment workflows. Each example can be adapted to specific needs and requirements.