# Production Deployment Verification CLI

This document provides comprehensive documentation for the production deployment verification CLI and automation system.

## Overview

The verification CLI provides a command-line interface for running comprehensive production readiness checks on the Ice Box Hockey website. It includes automated testing, performance monitoring, accessibility validation, and deployment decision workflows.

## Installation

### Local Installation

The CLI is included in the project and can be run using npm scripts:

```bash
# Install dependencies
npm install

# Initialize verification configuration
npm run verify:init

# Run complete verification
npm run verify
```

### Global Installation

To install the CLI globally (for use across projects):

```bash
npm install -g .
verify-deployment --help
```

## Quick Start

1. **Initialize Configuration**
   ```bash
   npm run verify:init
   ```

2. **Run Complete Verification**
   ```bash
   npm run verify
   ```

3. **Generate HTML Report**
   ```bash
   npm run verify:html
   ```

4. **CI/CD Integration**
   ```bash
   npm run ci:verify
   ```

## CLI Commands

### Main Commands

#### `run` - Complete Verification Pipeline
Runs the complete verification pipeline with all stages.

```bash
npm run verify
# or
npx tsx src/verification/cli.ts run [options]
```

**Options:**
- `-c, --config <path>` - Path to verification config file (default: verification.config.json)
- `-o, --output <format>` - Output format: console, json, html (default: console)
- `-f, --output-file <path>` - Output file path (for json/html formats)
- `-v, --verbose` - Enable verbose logging
- `-s, --skip-stages <stages>` - Comma-separated list of stages to skip
- `-w, --watch` - Watch mode - re-run on file changes
- `--ci` - CI mode - optimized for continuous integration
- `--fail-fast` - Stop on first failure
- `-t, --timeout <seconds>` - Global timeout in seconds (default: 300)

**Examples:**
```bash
# Basic verification
npm run verify

# Verbose output with HTML report
npm run verify -- --verbose --output html --output-file report.html

# Skip performance tests
npm run verify -- --skip-stages performance

# CI mode with JSON output
npm run verify -- --ci --output json --output-file ci-report.json
```

#### Individual Stage Commands

Run specific verification stages independently:

```bash
# Build verification only
npm run verify:build

# Test suites only
npm run verify:test

# Performance tests only
npm run verify:performance

# Accessibility tests only
npm run verify:accessibility
```

### Configuration Commands

#### `init` - Initialize Configuration
Creates a default verification configuration file.

```bash
npm run verify:init
# or
npx tsx src/verification/cli.ts init [--force]
```

#### `validate-config` - Validate Configuration
Validates the verification configuration file.

```bash
npm run verify:validate-config
# or
npx tsx src/verification/cli.ts validate-config [--config <path>]
```

### Reporting Commands

#### `report` - Generate Report
Generate reports from existing verification results.

```bash
npx tsx src/verification/cli.ts report [options]
```

**Options:**
- `-i, --input <path>` - Input JSON report file (default: verification-report.json)
- `-o, --output <format>` - Output format: html, json (default: html)
- `-f, --output-file <path>` - Output file path

### CI/CD Commands

#### `ci-check` - CI/CD Integration Check
Optimized verification for continuous integration pipelines.

```bash
npm run verify:ci
# or
npx tsx src/verification/cli.ts ci-check [options]
```

### Utility Commands

#### `list-stages` - List Available Stages
Shows all available verification stages.

```bash
npx tsx src/verification/cli.ts list-stages
```

#### `health-check` - Dependency Health Check
Checks if all required dependencies are available.

```bash
npm run verify:health
# or
npx tsx src/verification/cli.ts health-check
```

## Configuration

### Verification Configuration (`verification.config.json`)

The main configuration file controls all aspects of the verification process:

```json
{
  "buildSettings": {
    "mode": "production",
    "sourceMaps": false,
    "minification": true
  },
  "performanceThresholds": {
    "lcp": 2500,
    "fid": 100,
    "cls": 0.1,
    "lighthousePerformance": 90
  },
  "accessibilityLevel": "AA",
  "testSuites": [
    {
      "name": "unit",
      "type": "unit",
      "enabled": true,
      "timeout": 30000,
      "retries": 2
    }
  ],
  "externalDependencies": [
    {
      "name": "Google Maps API",
      "url": "https://maps.googleapis.com/maps/api/js",
      "timeout": 5000,
      "critical": true
    }
  ]
}
```

### Deployment Criteria (`deployment-criteria.json`)

Optional configuration for automated deployment decisions:

```json
{
  "buildMustPass": true,
  "minimumTestPassRate": 0.95,
  "criticalTestsMustPass": ["authentication", "payment"],
  "performanceThresholds": {
    "lcp": 2500,
    "fid": 100,
    "cls": 0.1,
    "lighthousePerformance": 90
  },
  "accessibilityMustPass": true,
  "maxCriticalA11yViolations": 0,
  "allowWarningDeployment": true,
  "requireManualApprovalOnWarnings": false
}
```

## Verification Stages

### 1. Build Verification
- TypeScript compilation
- Asset bundling
- Build optimization
- Output size analysis

### 2. Test Orchestration
- Unit tests (Jest)
- Integration tests
- End-to-end tests (Playwright)
- Test coverage analysis

### 3. Performance Monitoring
- Core Web Vitals (LCP, FID, CLS)
- Lighthouse auditing
- Network condition testing
- Resource optimization validation

### 4. Accessibility Validation
- WCAG 2.1 AA compliance
- Automated accessibility testing (axe-core)
- Keyboard navigation testing
- Screen reader compatibility

### 5. PWA Validation
- Service worker registration
- PWA manifest validation
- Offline functionality testing
- Installation capability

### 6. External Dependencies
- API availability checks
- CDN resource validation
- Third-party service testing
- Response time monitoring

## CI/CD Integration

### GitHub Actions

The project includes a complete GitHub Actions workflow for automated verification:

```yaml
# .github/workflows/deployment-verification.yml
name: Production Deployment Verification
on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]
```

**Features:**
- Multi-Node.js version testing
- Automated report generation
- PR comment integration
- Deployment gate functionality
- Artifact management

### Shell Script Integration

Use the provided shell script for CI/CD systems:

```bash
# Basic CI verification
./scripts/ci-verification.sh

# Skip environment setup (if already configured)
./scripts/ci-verification.sh --skip-setup

# Keep old reports for debugging
./scripts/ci-verification.sh --keep-reports
```

### Environment Variables

Configure CI/CD behavior with environment variables:

- `CI=true` - Enable CI mode
- `NODE_ENV=production` - Set production environment
- `CI_ARTIFACTS_DIR` - Directory for CI artifacts
- `GITHUB_ENV` - GitHub Actions environment file

## Automated Deployment Decisions

The system includes an automated deployment decision engine:

```bash
# Analyze verification results and make deployment decision
npx tsx scripts/deployment-decision.ts verification-report.json
```

**Decision Factors:**
- Build success/failure
- Test pass rates
- Performance metrics
- Accessibility compliance
- External dependency availability
- Custom business rules

## Output Formats

### Console Output
Human-readable summary with color-coded status indicators.

### JSON Output
Machine-readable format for CI/CD integration:

```json
{
  "timestamp": "2025-01-31T12:00:00.000Z",
  "overallStatus": "passed",
  "deploymentReady": true,
  "buildVerification": { ... },
  "testResults": [ ... ],
  "performanceMetrics": { ... },
  "accessibilityResults": { ... },
  "pwaValidation": { ... },
  "dependencyStatus": { ... },
  "recommendations": [ ... ]
}
```

### HTML Output
Comprehensive visual report with charts, graphs, and detailed analysis.

## Troubleshooting

### Common Issues

1. **Configuration Not Found**
   ```bash
   npm run verify:init
   ```

2. **Playwright Browsers Missing**
   ```bash
   npx playwright install
   ```

3. **Permission Denied (Shell Scripts)**
   ```bash
   chmod +x scripts/ci-verification.sh
   ```

4. **Timeout Issues**
   ```bash
   npm run verify -- --timeout 600
   ```

### Debug Mode

Enable verbose logging for detailed troubleshooting:

```bash
npm run verify:verbose
```

### Health Check

Verify all dependencies are properly installed:

```bash
npm run verify:health
```

## Best Practices

### Development Workflow

1. **Pre-commit Verification**
   ```bash
   npm run verify:build
   npm run verify:test
   ```

2. **Pre-deployment Verification**
   ```bash
   npm run verify
   ```

3. **Performance Monitoring**
   ```bash
   npm run verify:performance
   ```

### CI/CD Integration

1. **Use CI-specific commands**
   ```bash
   npm run verify:ci
   ```

2. **Generate artifacts**
   ```bash
   npm run verify -- --output html --output-file verification-report.html
   ```

3. **Set appropriate timeouts**
   ```bash
   npm run verify -- --timeout 900
   ```

### Configuration Management

1. **Environment-specific configs**
   - `verification.config.json` (production)
   - `verification.dev.config.json` (development)
   - `verification.staging.config.json` (staging)

2. **Version control**
   - Commit configuration files
   - Document configuration changes
   - Use semantic versioning for config updates

## Advanced Usage

### Custom Verification Stages

Extend the verification pipeline with custom stages by implementing the `VerificationStage` interface.

### Integration with External Tools

The CLI can be integrated with:
- Jenkins pipelines
- GitLab CI/CD
- Azure DevOps
- Custom deployment systems

### Monitoring and Alerting

Set up monitoring for verification results:
- Slack notifications
- Email alerts
- Dashboard integration
- Metrics collection

## Support

For issues and questions:
1. Check the troubleshooting section
2. Run health check: `npm run verify:health`
3. Enable verbose logging: `npm run verify:verbose`
4. Review configuration: `npm run verify:validate-config`

## Contributing

When contributing to the verification system:
1. Add tests for new features
2. Update documentation
3. Follow TypeScript best practices
4. Ensure backward compatibility