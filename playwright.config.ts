import { defineConfig, devices } from '@playwright/test';

/**
 * @see https://playwright.dev/docs/test-configuration
 */
export default defineConfig({
  testDir: './tests',
  /* Run tests in files in parallel */
  fullyParallel: true,
  /* Fail the build on CI if you accidentally left test.only in the source code. */
  forbidOnly: !!process.env.CI,
  /* Retry on CI only */
  retries: process.env.CI ? 2 : 0,
  /* Opt out of parallel tests on CI. */
  workers: process.env.CI ? 1 : undefined,
  /* Reporter to use. See https://playwright.dev/docs/test-reporters */
  reporter: [
    ['html', { outputFolder: 'playwright-report', open: 'never' }],
    ['json', { outputFile: 'verification-reports/playwright-results.json' }],
    ['junit', { outputFile: 'verification-reports/playwright-results.xml' }],
  ],
  /* Shared settings for all the projects below. See https://playwright.dev/docs/api/class-testoptions. */
  use: {
    /* Base URL to use in actions like `await page.goto('/')`. */
    baseURL: process.env.PLAYWRIGHT_BASE_URL || 'http://localhost:8080',

    /* Collect trace when retrying the failed test. See https://playwright.dev/docs/trace-viewer */
    trace: 'on-first-retry',
    
    /* Screenshot on failure for verification reports */
    screenshot: 'only-on-failure',
    
    /* Video recording for verification */
    video: process.env.CI ? 'retain-on-failure' : 'off',
    
    /* Additional context for verification system */
    extraHTTPHeaders: {
      'X-Verification-Run': process.env.VERIFICATION_RUN_ID || 'unknown',
    },
  },

  /* Global setup and teardown for verification integration */
  globalSetup: './src/test/playwright-global-setup.ts',
  globalTeardown: './src/test/playwright-global-teardown.ts',

  /* Configure projects for major browsers */
  projects: [
    {
      name: 'chromium',
      use: { 
        ...devices['Desktop Chrome'],
        // Additional Chrome flags for verification
        launchOptions: {
          args: [
            '--disable-web-security',
            '--disable-features=TranslateUI',
            '--disable-ipc-flooding-protection',
          ],
        },
      },
    },

    {
      name: 'firefox',
      use: { ...devices['Desktop Firefox'] },
    },

    {
      name: 'webkit',
      use: { ...devices['Desktop Safari'] },
    },

    /* Mobile testing for verification */
    {
      name: 'Mobile Chrome',
      use: { ...devices['Pixel 5'] },
    },
    {
      name: 'Mobile Safari',
      use: { ...devices['iPhone 12'] },
    },

    /* Accessibility testing project */
    {
      name: 'accessibility',
      testMatch: '**/accessibility.spec.ts',
      use: {
        ...devices['Desktop Chrome'],
        // Specific settings for accessibility testing
        colorScheme: 'light',
      },
    },

    /* Performance testing project */
    {
      name: 'performance',
      testMatch: '**/performance.spec.ts',
      use: {
        ...devices['Desktop Chrome'],
        // Network throttling for performance testing
        launchOptions: {
          args: ['--disable-dev-shm-usage', '--disable-gpu'],
        },
      },
    },
  ],

  /* Output directory for verification reports */
  outputDir: 'verification-reports/playwright-artifacts',

  /* Timeout configuration for verification compatibility */
  timeout: 30000,
  expect: {
    timeout: 10000,
  },

  /* Run your local dev server before starting the tests */
  webServer: {
    command: process.env.VERIFICATION_MODE === 'production' ? 'npm run preview' : 'npm run dev',
    url: process.env.PLAYWRIGHT_BASE_URL || 'http://localhost:8080',
    reuseExistingServer: !process.env.CI,
    timeout: 120000,
    stdout: 'pipe',
    stderr: 'pipe',
    env: {
      ...process.env,
      PLAYWRIGHT_WEBSERVER_PID: process.pid.toString(),
    },
  },
});