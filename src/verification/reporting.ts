/**
 * Comprehensive Reporting System
 * 
 * Generates detailed verification reports in multiple formats
 * Provides deployment readiness decision logic
 * Creates summary dashboards with pass/fail indicators
 */

import { promises as fs } from 'fs';
import path from 'path';
import { 
  VerificationReport, 
  BuildVerificationResult,
  TestResult,
  PerformanceMetrics,
  AccessibilityResult,
  PWAValidationResult,
  ExternalDependencies,
  A11yViolation,
  TestFailure,
  BuildError,
  DependencyStatus
} from './types';

export interface ReportGeneratorOptions {
  outputDir: string;
  includeTimestamp?: boolean;
  includeDetailedLogs?: boolean;
  customTheme?: ReportTheme;
}

export interface ReportTheme {
  primaryColor: string;
  successColor: string;
  warningColor: string;
  errorColor: string;
  backgroundColor: string;
  textColor: string;
}

export interface ReportSummary {
  overallStatus: 'passed' | 'failed' | 'warning';
  deploymentReady: boolean;
  totalChecks: number;
  passedChecks: number;
  failedChecks: number;
  warningChecks: number;
  executionTime: number;
  criticalIssues: string[];
  recommendations: string[];
}

export interface DetailedReportSection {
  name: string;
  status: 'passed' | 'failed' | 'warning';
  summary: string;
  details: any;
  errors: string[];
  warnings: string[];
  recommendations: string[];
}

/**
 * Main report generator class
 */
export class ReportGenerator {
  private options: ReportGeneratorOptions;
  private theme: ReportTheme;

  constructor(options: ReportGeneratorOptions) {
    this.options = options;
    this.theme = options.customTheme || this.getDefaultTheme();
  }

  /**
   * Generate comprehensive report in multiple formats
   */
  async generateReport(report: VerificationReport): Promise<{
    jsonPath: string;
    htmlPath: string;
    summary: ReportSummary;
  }> {
    // Ensure output directory exists
    await fs.mkdir(this.options.outputDir, { recursive: true });

    // Generate report summary
    const summary = this.generateSummary(report);

    // Generate detailed sections
    const sections = this.generateDetailedSections(report);

    // Generate JSON report
    const jsonReport = this.generateJSONReport(report, summary, sections);
    const jsonPath = await this.saveJSONReport(jsonReport);

    // Generate HTML report
    const htmlReport = this.generateHTMLReport(report, summary, sections);
    const htmlPath = await this.saveHTMLReport(htmlReport);

    return {
      jsonPath,
      htmlPath,
      summary,
    };
  }

  /**
   * Generate deployment readiness decision
   */
  generateDeploymentDecision(report: VerificationReport): {
    ready: boolean;
    confidence: number;
    blockers: string[];
    warnings: string[];
    recommendations: string[];
  } {
    const blockers: string[] = [];
    const warnings: string[] = [];
    const recommendations: string[] = [];
    let confidence = 100;

    // Check build verification
    if (!report.buildVerification.success) {
      blockers.push('Build verification failed');
      confidence -= 30;
    }

    if (report.buildVerification.errors.length > 0) {
      blockers.push(`${report.buildVerification.errors.length} build errors found`);
      confidence -= 20;
    }

    if (report.buildVerification.warnings.length > 0) {
      warnings.push(`${report.buildVerification.warnings.length} build warnings found`);
      confidence -= 5;
    }

    // Check test results
    const failedTests = report.testResults.filter(test => !test.passed);
    if (failedTests.length > 0) {
      blockers.push(`${failedTests.length} test suites failed`);
      confidence -= 25;
    }

    const totalFailures = report.testResults.reduce((sum, test) => sum + test.failures.length, 0);
    if (totalFailures > 0) {
      warnings.push(`${totalFailures} individual test failures found`);
      confidence -= Math.min(totalFailures * 2, 15);
    }

    // Check performance metrics
    const perfThresholds = {
      lcp: 2500,
      fid: 100,
      cls: 0.1,
      lighthouse: 90
    };

    if (report.performanceMetrics.lcp > perfThresholds.lcp) {
      warnings.push(`LCP (${report.performanceMetrics.lcp}ms) exceeds threshold (${perfThresholds.lcp}ms)`);
      confidence -= 10;
    }

    if (report.performanceMetrics.fid > perfThresholds.fid) {
      warnings.push(`FID (${report.performanceMetrics.fid}ms) exceeds threshold (${perfThresholds.fid}ms)`);
      confidence -= 10;
    }

    if (report.performanceMetrics.cls > perfThresholds.cls) {
      warnings.push(`CLS (${report.performanceMetrics.cls}) exceeds threshold (${perfThresholds.cls})`);
      confidence -= 10;
    }

    if (report.performanceMetrics.lighthouse.performance < perfThresholds.lighthouse) {
      warnings.push(`Lighthouse performance score (${report.performanceMetrics.lighthouse.performance}) below threshold (${perfThresholds.lighthouse})`);
      confidence -= 15;
    }

    // Check accessibility
    const criticalA11yViolations = report.accessibilityResults.violations.filter(
      v => v.impact === 'critical' || v.impact === 'serious'
    );

    if (criticalA11yViolations.length > 0) {
      blockers.push(`${criticalA11yViolations.length} critical accessibility violations found`);
      confidence -= 20;
    }

    if (report.accessibilityResults.violations.length > criticalA11yViolations.length) {
      const minorViolations = report.accessibilityResults.violations.length - criticalA11yViolations.length;
      warnings.push(`${minorViolations} minor accessibility violations found`);
      confidence -= Math.min(minorViolations, 10);
    }

    // Check PWA validation
    if (!report.pwaValidation.serviceWorkerRegistered) {
      warnings.push('Service worker not registered');
      confidence -= 5;
    }

    if (!report.pwaValidation.manifestValid) {
      warnings.push('PWA manifest validation failed');
      confidence -= 5;
    }

    // Check external dependencies
    const failedDependencies: string[] = [];
    
    if (!report.dependencyStatus.googleMaps.available) {
      failedDependencies.push('Google Maps API');
    }

    report.dependencyStatus.cdnResources.forEach(resource => {
      if (!resource.available) {
        failedDependencies.push(resource.service);
      }
    });

    report.dependencyStatus.apiEndpoints.forEach(endpoint => {
      if (!endpoint.available) {
        failedDependencies.push(endpoint.service);
      }
    });

    if (failedDependencies.length > 0) {
      warnings.push(`External dependencies unavailable: ${failedDependencies.join(', ')}`);
      confidence -= failedDependencies.length * 5;
    }

    // Generate recommendations
    if (blockers.length === 0 && warnings.length === 0) {
      recommendations.push('All verification checks passed - ready for deployment');
    } else {
      if (blockers.length > 0) {
        recommendations.push('Fix critical issues before deployment');
      }
      if (warnings.length > 0) {
        recommendations.push('Address warnings for optimal production performance');
      }
    }

    // Add specific recommendations based on issues found
    if (report.buildVerification.errors.length > 0) {
      recommendations.push('Review and fix TypeScript compilation errors');
    }

    if (failedTests.length > 0) {
      recommendations.push('Fix failing tests to ensure application stability');
    }

    if (report.performanceMetrics.lighthouse.performance < 90) {
      recommendations.push('Optimize application performance for better user experience');
    }

    if (criticalA11yViolations.length > 0) {
      recommendations.push('Address accessibility violations to ensure compliance');
    }

    const ready = blockers.length === 0 && confidence >= 70;
    confidence = Math.max(0, Math.min(100, confidence));

    return {
      ready,
      confidence,
      blockers,
      warnings,
      recommendations,
    };
  }

  /**
   * Generate report summary
   */
  private generateSummary(report: VerificationReport): ReportSummary {
    const decision = this.generateDeploymentDecision(report);
    
    let totalChecks = 0;
    let passedChecks = 0;
    let failedChecks = 0;
    let warningChecks = 0;

    // Count build checks
    totalChecks++;
    if (report.buildVerification.success) {
      passedChecks++;
    } else {
      failedChecks++;
    }

    // Count test checks
    report.testResults.forEach(test => {
      totalChecks++;
      if (test.passed) {
        passedChecks++;
      } else {
        failedChecks++;
      }
    });

    // Count performance checks
    totalChecks += 4; // LCP, FID, CLS, Lighthouse
    const perfThresholds = { lcp: 2500, fid: 100, cls: 0.1, lighthouse: 90 };
    
    if (report.performanceMetrics.lcp <= perfThresholds.lcp) passedChecks++;
    else warningChecks++;
    
    if (report.performanceMetrics.fid <= perfThresholds.fid) passedChecks++;
    else warningChecks++;
    
    if (report.performanceMetrics.cls <= perfThresholds.cls) passedChecks++;
    else warningChecks++;
    
    if (report.performanceMetrics.lighthouse.performance >= perfThresholds.lighthouse) passedChecks++;
    else warningChecks++;

    // Count accessibility checks
    totalChecks++;
    if (report.accessibilityResults.compliant) {
      passedChecks++;
    } else {
      const criticalViolations = report.accessibilityResults.violations.filter(
        v => v.impact === 'critical' || v.impact === 'serious'
      );
      if (criticalViolations.length > 0) {
        failedChecks++;
      } else {
        warningChecks++;
      }
    }

    // Count PWA checks
    totalChecks += 2;
    if (report.pwaValidation.serviceWorkerRegistered) passedChecks++;
    else warningChecks++;
    
    if (report.pwaValidation.manifestValid) passedChecks++;
    else warningChecks++;

    // Count dependency checks
    const allDependencies = [
      report.dependencyStatus.googleMaps,
      ...report.dependencyStatus.cdnResources,
      ...report.dependencyStatus.apiEndpoints
    ];
    
    allDependencies.forEach(dep => {
      totalChecks++;
      if (dep.available) {
        passedChecks++;
      } else {
        warningChecks++;
      }
    });

    return {
      overallStatus: report.overallStatus,
      deploymentReady: decision.ready,
      totalChecks,
      passedChecks,
      failedChecks,
      warningChecks,
      executionTime: 0, // Will be calculated from timestamps
      criticalIssues: decision.blockers,
      recommendations: decision.recommendations,
    };
  }

  /**
   * Generate detailed report sections
   */
  private generateDetailedSections(report: VerificationReport): DetailedReportSection[] {
    const sections: DetailedReportSection[] = [];

    // Build verification section
    sections.push({
      name: 'Build Verification',
      status: report.buildVerification.success ? 'passed' : 'failed',
      summary: this.generateBuildSummary(report.buildVerification),
      details: report.buildVerification,
      errors: report.buildVerification.errors.map(e => `${e.file}:${e.line}:${e.column} - ${e.message}`),
      warnings: report.buildVerification.warnings.map(w => `${w.file}:${w.line}:${w.column} - ${w.message}`),
      recommendations: this.generateBuildRecommendations(report.buildVerification),
    });

    // Test results section
    sections.push({
      name: 'Test Results',
      status: report.testResults.every(t => t.passed) ? 'passed' : 'failed',
      summary: this.generateTestSummary(report.testResults),
      details: report.testResults,
      errors: this.extractTestErrors(report.testResults),
      warnings: this.extractTestWarnings(report.testResults),
      recommendations: this.generateTestRecommendations(report.testResults),
    });

    // Performance section
    sections.push({
      name: 'Performance Metrics',
      status: this.getPerformanceStatus(report.performanceMetrics),
      summary: this.generatePerformanceSummary(report.performanceMetrics),
      details: report.performanceMetrics,
      errors: this.extractPerformanceErrors(report.performanceMetrics),
      warnings: this.extractPerformanceWarnings(report.performanceMetrics),
      recommendations: this.generatePerformanceRecommendations(report.performanceMetrics),
    });

    // Accessibility section
    sections.push({
      name: 'Accessibility Validation',
      status: report.accessibilityResults.compliant ? 'passed' : 
              report.accessibilityResults.violations.some(v => v.impact === 'critical' || v.impact === 'serious') ? 'failed' : 'warning',
      summary: this.generateAccessibilitySummary(report.accessibilityResults),
      details: report.accessibilityResults,
      errors: this.extractAccessibilityErrors(report.accessibilityResults),
      warnings: this.extractAccessibilityWarnings(report.accessibilityResults),
      recommendations: this.generateAccessibilityRecommendations(report.accessibilityResults),
    });

    // PWA section
    sections.push({
      name: 'PWA Validation',
      status: this.getPWAStatus(report.pwaValidation),
      summary: this.generatePWASummary(report.pwaValidation),
      details: report.pwaValidation,
      errors: this.extractPWAErrors(report.pwaValidation),
      warnings: this.extractPWAWarnings(report.pwaValidation),
      recommendations: this.generatePWARecommendations(report.pwaValidation),
    });

    // External dependencies section
    sections.push({
      name: 'External Dependencies',
      status: this.getDependencyStatus(report.dependencyStatus),
      summary: this.generateDependencySummary(report.dependencyStatus),
      details: report.dependencyStatus,
      errors: this.extractDependencyErrors(report.dependencyStatus),
      warnings: this.extractDependencyWarnings(report.dependencyStatus),
      recommendations: this.generateDependencyRecommendations(report.dependencyStatus),
    });

    return sections;
  }

  /**
   * Generate JSON report
   */
  private generateJSONReport(
    report: VerificationReport, 
    summary: ReportSummary, 
    sections: DetailedReportSection[]
  ): any {
    const decision = this.generateDeploymentDecision(report);

    return {
      metadata: {
        generatedAt: new Date().toISOString(),
        version: '1.0.0',
        reportType: 'production-deployment-verification',
      },
      summary,
      deploymentDecision: decision,
      originalReport: report,
      detailedSections: sections,
      rawData: {
        buildVerification: report.buildVerification,
        testResults: report.testResults,
        performanceMetrics: report.performanceMetrics,
        accessibilityResults: report.accessibilityResults,
        pwaValidation: report.pwaValidation,
        dependencyStatus: report.dependencyStatus,
      },
    };
  }

  /**
   * Save JSON report to file
   */
  private async saveJSONReport(jsonReport: any): Promise<string> {
    const timestamp = this.options.includeTimestamp ? 
      `-${new Date().toISOString().replace(/[:.]/g, '-')}` : '';
    const filename = `verification-report${timestamp}.json`;
    const filepath = path.join(this.options.outputDir, filename);
    
    await fs.writeFile(filepath, JSON.stringify(jsonReport, null, 2));
    return filepath;
  }

  /**
   * Get default theme
   */
  private getDefaultTheme(): ReportTheme {
    return {
      primaryColor: '#2563eb',
      successColor: '#16a34a',
      warningColor: '#d97706',
      errorColor: '#dc2626',
      backgroundColor: '#ffffff',
      textColor: '#1f2937',
    };
  }

  // Helper methods for generating summaries and extracting information
  private generateBuildSummary(build: BuildVerificationResult): string {
    if (build.success) {
      return `Build completed successfully in ${build.buildTime}ms. Output size: ${(build.outputSize.total / 1024 / 1024).toFixed(2)}MB`;
    } else {
      return `Build failed with ${build.errors.length} errors and ${build.warnings.length} warnings`;
    }
  }

  private generateBuildRecommendations(build: BuildVerificationResult): string[] {
    const recommendations: string[] = [];
    
    if (build.errors.length > 0) {
      recommendations.push('Fix TypeScript compilation errors before deployment');
    }
    
    if (build.warnings.length > 0) {
      recommendations.push('Address build warnings to improve code quality');
    }
    
    if (build.outputSize.total > 5 * 1024 * 1024) { // 5MB
      recommendations.push('Consider optimizing bundle size for better performance');
    }
    
    return recommendations;
  }

  private generateTestSummary(tests: TestResult[]): string {
    const totalTests = tests.reduce((sum, test) => sum + test.testCount, 0);
    const totalFailures = tests.reduce((sum, test) => sum + test.failures.length, 0);
    const passedSuites = tests.filter(test => test.passed).length;
    
    return `${passedSuites}/${tests.length} test suites passed. ${totalTests - totalFailures}/${totalTests} individual tests passed`;
  }

  private extractTestErrors(tests: TestResult[]): string[] {
    const errors: string[] = [];
    tests.forEach((test, index) => {
      if (!test.passed) {
        errors.push(`Test suite ${index + 1} failed`);
        test.failures.forEach(failure => {
          errors.push(`${failure.testName}: ${failure.error}`);
        });
      }
    });
    return errors;
  }

  private extractTestWarnings(tests: TestResult[]): string[] {
    const warnings: string[] = [];
    tests.forEach(test => {
      if (test.passed && test.failures.length > 0) {
        warnings.push(`Test suite passed but had ${test.failures.length} individual test failures`);
      }
    });
    return warnings;
  }

  private generateTestRecommendations(tests: TestResult[]): string[] {
    const recommendations: string[] = [];
    const failedSuites = tests.filter(test => !test.passed);
    
    if (failedSuites.length > 0) {
      recommendations.push('Fix failing test suites before deployment');
    }
    
    const totalFailures = tests.reduce((sum, test) => sum + test.failures.length, 0);
    if (totalFailures > 0) {
      recommendations.push('Review and fix individual test failures');
    }
    
    return recommendations;
  }

  private getPerformanceStatus(metrics: PerformanceMetrics): 'passed' | 'failed' | 'warning' {
    const thresholds = { lcp: 2500, fid: 100, cls: 0.1, lighthouse: 90 };
    
    const violations = [
      metrics.lcp > thresholds.lcp,
      metrics.fid > thresholds.fid,
      metrics.cls > thresholds.cls,
      metrics.lighthouse.performance < thresholds.lighthouse
    ].filter(Boolean).length;
    
    if (violations === 0) return 'passed';
    if (violations >= 2) return 'failed';
    return 'warning';
  }

  private generatePerformanceSummary(metrics: PerformanceMetrics): string {
    return `LCP: ${metrics.lcp}ms, FID: ${metrics.fid}ms, CLS: ${metrics.cls}, Lighthouse: ${metrics.lighthouse.performance}/100`;
  }

  private extractPerformanceErrors(metrics: PerformanceMetrics): string[] {
    const errors: string[] = [];
    const thresholds = { lcp: 2500, fid: 100, cls: 0.1, lighthouse: 70 };
    
    if (metrics.lcp > thresholds.lcp * 1.5) {
      errors.push(`LCP (${metrics.lcp}ms) significantly exceeds threshold`);
    }
    
    if (metrics.lighthouse.performance < thresholds.lighthouse) {
      errors.push(`Lighthouse performance score (${metrics.lighthouse.performance}) critically low`);
    }
    
    return errors;
  }

  private extractPerformanceWarnings(metrics: PerformanceMetrics): string[] {
    const warnings: string[] = [];
    const thresholds = { lcp: 2500, fid: 100, cls: 0.1, lighthouse: 90 };
    
    if (metrics.lcp > thresholds.lcp && metrics.lcp <= thresholds.lcp * 1.5) {
      warnings.push(`LCP (${metrics.lcp}ms) exceeds recommended threshold`);
    }
    
    if (metrics.fid > thresholds.fid) {
      warnings.push(`FID (${metrics.fid}ms) exceeds recommended threshold`);
    }
    
    if (metrics.cls > thresholds.cls) {
      warnings.push(`CLS (${metrics.cls}) exceeds recommended threshold`);
    }
    
    if (metrics.lighthouse.performance < thresholds.lighthouse && metrics.lighthouse.performance >= 70) {
      warnings.push(`Lighthouse performance score (${metrics.lighthouse.performance}) below recommended threshold`);
    }
    
    return warnings;
  }

  private generatePerformanceRecommendations(metrics: PerformanceMetrics): string[] {
    const recommendations: string[] = [];
    
    if (metrics.lcp > 2500) {
      recommendations.push('Optimize Largest Contentful Paint by reducing server response times and optimizing critical resources');
    }
    
    if (metrics.fid > 100) {
      recommendations.push('Improve First Input Delay by reducing JavaScript execution time and optimizing event handlers');
    }
    
    if (metrics.cls > 0.1) {
      recommendations.push('Reduce Cumulative Layout Shift by setting explicit dimensions for images and avoiding dynamic content insertion');
    }
    
    if (metrics.lighthouse.performance < 90) {
      recommendations.push('Follow Lighthouse recommendations to improve overall performance score');
    }
    
    return recommendations;
  }

  private generateAccessibilitySummary(results: AccessibilityResult): string {
    const criticalCount = results.violations.filter(v => v.impact === 'critical').length;
    const seriousCount = results.violations.filter(v => v.impact === 'serious').length;
    const totalViolations = results.violations.length;
    
    if (results.compliant) {
      return `WCAG compliance achieved. ${results.testedPages.length} pages tested`;
    } else {
      return `${totalViolations} violations found (${criticalCount} critical, ${seriousCount} serious)`;
    }
  }

  private extractAccessibilityErrors(results: AccessibilityResult): string[] {
    return results.violations
      .filter(v => v.impact === 'critical' || v.impact === 'serious')
      .map(v => `${v.rule}: ${v.description} (${v.element})`);
  }

  private extractAccessibilityWarnings(results: AccessibilityResult): string[] {
    const warnings = results.violations
      .filter(v => v.impact === 'moderate' || v.impact === 'minor')
      .map(v => `${v.rule}: ${v.description} (${v.element})`);
    
    warnings.push(...results.warnings.map(w => `${w.rule}: ${w.description} (${w.element})`));
    
    return warnings;
  }

  private generateAccessibilityRecommendations(results: AccessibilityResult): string[] {
    const recommendations: string[] = [];
    
    const criticalViolations = results.violations.filter(v => v.impact === 'critical');
    if (criticalViolations.length > 0) {
      recommendations.push('Address critical accessibility violations immediately');
    }
    
    const seriousViolations = results.violations.filter(v => v.impact === 'serious');
    if (seriousViolations.length > 0) {
      recommendations.push('Fix serious accessibility violations before deployment');
    }
    
    if (results.violations.length > 0) {
      recommendations.push('Review accessibility violations and implement fixes according to WCAG guidelines');
    }
    
    return recommendations;
  }

  private getPWAStatus(pwa: PWAValidationResult): 'passed' | 'failed' | 'warning' {
    const criticalChecks = [pwa.serviceWorkerRegistered, pwa.manifestValid];
    const failedCritical = criticalChecks.filter(check => !check).length;
    
    if (failedCritical > 0) return 'warning';
    if (!pwa.offlineFunctionality || !pwa.installable) return 'warning';
    return 'passed';
  }

  private generatePWASummary(pwa: PWAValidationResult): string {
    const checks = [
      pwa.serviceWorkerRegistered ? 'SW registered' : 'SW not registered',
      pwa.manifestValid ? 'Manifest valid' : 'Manifest invalid',
      pwa.offlineFunctionality ? 'Offline ready' : 'Offline not ready',
      pwa.installable ? 'Installable' : 'Not installable'
    ];
    
    return checks.join(', ');
  }

  private extractPWAErrors(pwa: PWAValidationResult): string[] {
    const errors: string[] = [];
    
    if (!pwa.serviceWorkerRegistered) {
      errors.push('Service worker not registered');
    }
    
    if (!pwa.manifestValid) {
      errors.push('PWA manifest validation failed');
    }
    
    return errors;
  }

  private extractPWAWarnings(pwa: PWAValidationResult): string[] {
    const warnings: string[] = [];
    
    if (!pwa.offlineFunctionality) {
      warnings.push('Offline functionality not working properly');
    }
    
    if (!pwa.installable) {
      warnings.push('PWA not installable');
    }
    
    if (!pwa.cacheStrategy.staticAssetsCache) {
      warnings.push('Static assets not properly cached');
    }
    
    return warnings;
  }

  private generatePWARecommendations(pwa: PWAValidationResult): string[] {
    const recommendations: string[] = [];
    
    if (!pwa.serviceWorkerRegistered) {
      recommendations.push('Implement and register service worker for PWA functionality');
    }
    
    if (!pwa.manifestValid) {
      recommendations.push('Fix PWA manifest validation issues');
    }
    
    if (!pwa.offlineFunctionality) {
      recommendations.push('Implement offline functionality with proper caching strategy');
    }
    
    return recommendations;
  }

  private getDependencyStatus(deps: ExternalDependencies): 'passed' | 'failed' | 'warning' {
    const allDeps = [deps.googleMaps, ...deps.cdnResources, ...deps.apiEndpoints];
    const unavailable = allDeps.filter(dep => !dep.available);
    
    if (unavailable.length === 0) return 'passed';
    
    // Check if any critical dependencies are down
    if (!deps.googleMaps.available) return 'warning';
    
    return 'warning';
  }

  private generateDependencySummary(deps: ExternalDependencies): string {
    const allDeps = [deps.googleMaps, ...deps.cdnResources, ...deps.apiEndpoints];
    const available = allDeps.filter(dep => dep.available).length;
    
    return `${available}/${allDeps.length} external dependencies available`;
  }

  private extractDependencyErrors(deps: ExternalDependencies): string[] {
    const errors: string[] = [];
    
    if (!deps.googleMaps.available) {
      errors.push(`Google Maps API unavailable: ${deps.googleMaps.error || 'Unknown error'}`);
    }
    
    return errors;
  }

  private extractDependencyWarnings(deps: ExternalDependencies): string[] {
    const warnings: string[] = [];
    
    deps.cdnResources.forEach(resource => {
      if (!resource.available) {
        warnings.push(`CDN resource unavailable: ${resource.service} - ${resource.error || 'Unknown error'}`);
      }
    });
    
    deps.apiEndpoints.forEach(endpoint => {
      if (!endpoint.available) {
        warnings.push(`API endpoint unavailable: ${endpoint.service} - ${endpoint.error || 'Unknown error'}`);
      }
    });
    
    return warnings;
  }

  private generateDependencyRecommendations(deps: ExternalDependencies): string[] {
    const recommendations: string[] = [];
    
    const allDeps = [deps.googleMaps, ...deps.cdnResources, ...deps.apiEndpoints];
    const unavailable = allDeps.filter(dep => !dep.available);
    
    if (unavailable.length > 0) {
      recommendations.push('Verify external service availability before deployment');
      recommendations.push('Consider implementing fallback mechanisms for external dependencies');
    }
    
    return recommendations;
  }

  /**
   * Generate HTML report
   */
  private generateHTMLReport(
    report: VerificationReport, 
    summary: ReportSummary, 
    sections: DetailedReportSection[]
  ): string {
    const decision = this.generateDeploymentDecision(report);
    
    return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Production Deployment Verification Report</title>
    <style>
        ${this.generateCSS()}
    </style>
</head>
<body>
    <div class="container">
        ${this.generateHeader(report, summary)}
        ${this.generateSummaryDashboard(summary, decision)}
        ${this.generateDetailedSectionsHTML(sections)}
        ${this.generateFooter()}
    </div>
    <script>
        ${this.generateJavaScript()}
    </script>
</body>
</html>`;
  }

  /**
   * Generate CSS styles for HTML report
   */
  private generateCSS(): string {
    return `
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: ${this.theme.textColor};
            background-color: ${this.theme.backgroundColor};
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, ${this.theme.primaryColor}, #3b82f6);
            color: white;
            border-radius: 8px;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .header .timestamp {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .summary-dashboard {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .summary-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            border-left: 4px solid ${this.theme.primaryColor};
        }

        .summary-card.passed {
            border-left-color: ${this.theme.successColor};
        }

        .summary-card.failed {
            border-left-color: ${this.theme.errorColor};
        }

        .summary-card.warning {
            border-left-color: ${this.theme.warningColor};
        }

        .summary-card h3 {
            font-size: 1.2rem;
            margin-bottom: 10px;
            color: ${this.theme.textColor};
        }

        .summary-card .value {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .summary-card .label {
            font-size: 0.9rem;
            color: #6b7280;
        }

        .deployment-status {
            grid-column: 1 / -1;
            text-align: center;
            padding: 30px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .deployment-status.ready {
            background: linear-gradient(135deg, ${this.theme.successColor}, #22c55e);
            color: white;
        }

        .deployment-status.not-ready {
            background: linear-gradient(135deg, ${this.theme.errorColor}, #ef4444);
            color: white;
        }

        .deployment-status h2 {
            font-size: 2rem;
            margin-bottom: 10px;
        }

        .deployment-status .confidence {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .section {
            background: white;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .section-header {
            padding: 20px;
            background: #f9fafb;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            justify-content: space-between;
            align-items: center;
            cursor: pointer;
        }

        .section-header h3 {
            font-size: 1.3rem;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: bold;
            text-transform: uppercase;
        }

        .status-badge.passed {
            background: ${this.theme.successColor};
            color: white;
        }

        .status-badge.failed {
            background: ${this.theme.errorColor};
            color: white;
        }

        .status-badge.warning {
            background: ${this.theme.warningColor};
            color: white;
        }

        .section-content {
            padding: 20px;
        }

        .section-content.collapsed {
            display: none;
        }

        .section-summary {
            font-size: 1.1rem;
            margin-bottom: 20px;
            padding: 15px;
            background: #f3f4f6;
            border-radius: 6px;
        }

        .issues-list {
            margin-bottom: 20px;
        }

        .issues-list h4 {
            margin-bottom: 10px;
            color: ${this.theme.textColor};
        }

        .issue-item {
            padding: 10px;
            margin-bottom: 8px;
            border-radius: 4px;
            border-left: 3px solid;
        }

        .issue-item.error {
            background: #fef2f2;
            border-left-color: ${this.theme.errorColor};
        }

        .issue-item.warning {
            background: #fffbeb;
            border-left-color: ${this.theme.warningColor};
        }

        .recommendations {
            background: #f0f9ff;
            border: 1px solid #bae6fd;
            border-radius: 6px;
            padding: 15px;
        }

        .recommendations h4 {
            color: ${this.theme.primaryColor};
            margin-bottom: 10px;
        }

        .recommendations ul {
            list-style-type: none;
            padding-left: 0;
        }

        .recommendations li {
            padding: 5px 0;
            padding-left: 20px;
            position: relative;
        }

        .recommendations li:before {
            content: "→";
            position: absolute;
            left: 0;
            color: ${this.theme.primaryColor};
            font-weight: bold;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e5e7eb;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, ${this.theme.successColor}, #22c55e);
            transition: width 0.3s ease;
        }

        .progress-fill.warning {
            background: linear-gradient(90deg, ${this.theme.warningColor}, #f59e0b);
        }

        .progress-fill.error {
            background: linear-gradient(90deg, ${this.theme.errorColor}, #ef4444);
        }

        .footer {
            text-align: center;
            padding: 20px;
            color: #6b7280;
            border-top: 1px solid #e5e7eb;
            margin-top: 40px;
        }

        .toggle-icon {
            transition: transform 0.3s ease;
        }

        .toggle-icon.collapsed {
            transform: rotate(-90deg);
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .metric-card {
            background: #f9fafb;
            padding: 15px;
            border-radius: 6px;
            text-align: center;
        }

        .metric-value {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .metric-label {
            font-size: 0.9rem;
            color: #6b7280;
        }

        .metric-card.good .metric-value {
            color: ${this.theme.successColor};
        }

        .metric-card.warning .metric-value {
            color: ${this.theme.warningColor};
        }

        .metric-card.error .metric-value {
            color: ${this.theme.errorColor};
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }

            .header h1 {
                font-size: 2rem;
            }

            .summary-dashboard {
                grid-template-columns: 1fr;
            }

            .section-header {
                padding: 15px;
            }

            .section-content {
                padding: 15px;
            }
        }
    `;
  }

  /**
   * Generate HTML header
   */
  private generateHeader(report: VerificationReport, summary: ReportSummary): string {
    return `
        <div class="header">
            <h1>Production Deployment Verification Report</h1>
            <div class="timestamp">Generated on ${report.timestamp.toLocaleString()}</div>
        </div>
    `;
  }

  /**
   * Generate summary dashboard
   */
  private generateSummaryDashboard(summary: ReportSummary, decision: any): string {
    const progressPercentage = Math.round((summary.passedChecks / summary.totalChecks) * 100);
    const progressClass = progressPercentage >= 90 ? '' : progressPercentage >= 70 ? 'warning' : 'error';

    return `
        <div class="summary-dashboard">
            <div class="deployment-status ${decision.ready ? 'ready' : 'not-ready'}">
                <h2>${decision.ready ? '✅ Ready for Deployment' : '❌ Not Ready for Deployment'}</h2>
                <div class="confidence">Confidence: ${decision.confidence}%</div>
                <div class="progress-bar">
                    <div class="progress-fill ${progressClass}" style="width: ${progressPercentage}%"></div>
                </div>
            </div>

            <div class="summary-card ${summary.overallStatus}">
                <h3>Overall Status</h3>
                <div class="value">${summary.overallStatus.toUpperCase()}</div>
                <div class="label">Verification Status</div>
            </div>

            <div class="summary-card">
                <h3>Total Checks</h3>
                <div class="value">${summary.totalChecks}</div>
                <div class="label">Verification Checks</div>
            </div>

            <div class="summary-card passed">
                <h3>Passed</h3>
                <div class="value" style="color: ${this.theme.successColor}">${summary.passedChecks}</div>
                <div class="label">Successful Checks</div>
            </div>

            <div class="summary-card failed">
                <h3>Failed</h3>
                <div class="value" style="color: ${this.theme.errorColor}">${summary.failedChecks}</div>
                <div class="label">Failed Checks</div>
            </div>

            <div class="summary-card warning">
                <h3>Warnings</h3>
                <div class="value" style="color: ${this.theme.warningColor}">${summary.warningChecks}</div>
                <div class="label">Warning Checks</div>
            </div>

            ${decision.blockers.length > 0 ? `
            <div class="summary-card failed" style="grid-column: 1 / -1;">
                <h3>Critical Issues</h3>
                <ul style="margin-top: 10px; padding-left: 20px;">
                    ${decision.blockers.map(blocker => `<li>${blocker}</li>`).join('')}
                </ul>
            </div>
            ` : ''}
        </div>
    `;
  }

  /**
   * Generate detailed sections HTML
   */
  private generateDetailedSectionsHTML(sections: DetailedReportSection[]): string {
    return sections.map((section, index) => `
        <div class="section">
            <div class="section-header" onclick="toggleSection(${index})">
                <h3>
                    <span class="toggle-icon" id="toggle-${index}">▼</span>
                    ${section.name}
                </h3>
                <span class="status-badge ${section.status}">${section.status}</span>
            </div>
            <div class="section-content" id="content-${index}">
                <div class="section-summary">${section.summary}</div>
                
                ${this.generateSectionSpecificContent(section)}
                
                ${section.errors.length > 0 ? `
                <div class="issues-list">
                    <h4>Errors</h4>
                    ${section.errors.map(error => `
                        <div class="issue-item error">${error}</div>
                    `).join('')}
                </div>
                ` : ''}
                
                ${section.warnings.length > 0 ? `
                <div class="issues-list">
                    <h4>Warnings</h4>
                    ${section.warnings.map(warning => `
                        <div class="issue-item warning">${warning}</div>
                    `).join('')}
                </div>
                ` : ''}
                
                ${section.recommendations.length > 0 ? `
                <div class="recommendations">
                    <h4>Recommendations</h4>
                    <ul>
                        ${section.recommendations.map(rec => `<li>${rec}</li>`).join('')}
                    </ul>
                </div>
                ` : ''}
            </div>
        </div>
    `).join('');
  }

  /**
   * Generate section-specific content
   */
  private generateSectionSpecificContent(section: DetailedReportSection): string {
    switch (section.name) {
      case 'Build Verification':
        return this.generateBuildContent(section.details);
      case 'Test Results':
        return this.generateTestContent(section.details);
      case 'Performance Metrics':
        return this.generatePerformanceContent(section.details);
      case 'Accessibility Validation':
        return this.generateAccessibilityContent(section.details);
      case 'PWA Validation':
        return this.generatePWAContent(section.details);
      case 'External Dependencies':
        return this.generateDependencyContent(section.details);
      default:
        return '';
    }
  }

  /**
   * Generate build-specific content
   */
  private generateBuildContent(build: BuildVerificationResult): string {
    return `
        <div class="metrics-grid">
            <div class="metric-card">
                <div class="metric-value">${build.buildTime}ms</div>
                <div class="metric-label">Build Time</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">${(build.outputSize.total / 1024 / 1024).toFixed(2)}MB</div>
                <div class="metric-label">Output Size</div>
            </div>
            <div class="metric-card ${build.errors.length > 0 ? 'error' : 'good'}">
                <div class="metric-value">${build.errors.length}</div>
                <div class="metric-label">Errors</div>
            </div>
            <div class="metric-card ${build.warnings.length > 0 ? 'warning' : 'good'}">
                <div class="metric-value">${build.warnings.length}</div>
                <div class="metric-label">Warnings</div>
            </div>
        </div>
    `;
  }

  /**
   * Generate test-specific content
   */
  private generateTestContent(tests: TestResult[]): string {
    const totalTests = tests.reduce((sum, test) => sum + test.testCount, 0);
    const totalFailures = tests.reduce((sum, test) => sum + test.failures.length, 0);
    const passedSuites = tests.filter(test => test.passed).length;

    return `
        <div class="metrics-grid">
            <div class="metric-card ${passedSuites === tests.length ? 'good' : 'error'}">
                <div class="metric-value">${passedSuites}/${tests.length}</div>
                <div class="metric-label">Test Suites Passed</div>
            </div>
            <div class="metric-card ${totalFailures === 0 ? 'good' : 'error'}">
                <div class="metric-value">${totalTests - totalFailures}/${totalTests}</div>
                <div class="metric-label">Individual Tests Passed</div>
            </div>
            <div class="metric-card ${totalFailures === 0 ? 'good' : 'error'}">
                <div class="metric-value">${totalFailures}</div>
                <div class="metric-label">Total Failures</div>
            </div>
        </div>
    `;
  }

  /**
   * Generate performance-specific content
   */
  private generatePerformanceContent(metrics: PerformanceMetrics): string {
    const thresholds = { lcp: 2500, fid: 100, cls: 0.1, lighthouse: 90 };

    return `
        <div class="metrics-grid">
            <div class="metric-card ${metrics.lcp <= thresholds.lcp ? 'good' : 'warning'}">
                <div class="metric-value">${metrics.lcp}ms</div>
                <div class="metric-label">LCP (≤${thresholds.lcp}ms)</div>
            </div>
            <div class="metric-card ${metrics.fid <= thresholds.fid ? 'good' : 'warning'}">
                <div class="metric-value">${metrics.fid}ms</div>
                <div class="metric-label">FID (≤${thresholds.fid}ms)</div>
            </div>
            <div class="metric-card ${metrics.cls <= thresholds.cls ? 'good' : 'warning'}">
                <div class="metric-value">${metrics.cls}</div>
                <div class="metric-label">CLS (≤${thresholds.cls})</div>
            </div>
            <div class="metric-card ${metrics.lighthouse.performance >= thresholds.lighthouse ? 'good' : 'warning'}">
                <div class="metric-value">${metrics.lighthouse.performance}/100</div>
                <div class="metric-label">Lighthouse Performance</div>
            </div>
        </div>
    `;
  }

  /**
   * Generate accessibility-specific content
   */
  private generateAccessibilityContent(results: AccessibilityResult): string {
    const criticalCount = results.violations.filter(v => v.impact === 'critical').length;
    const seriousCount = results.violations.filter(v => v.impact === 'serious').length;
    const moderateCount = results.violations.filter(v => v.impact === 'moderate').length;
    const minorCount = results.violations.filter(v => v.impact === 'minor').length;

    return `
        <div class="metrics-grid">
            <div class="metric-card ${results.compliant ? 'good' : 'error'}">
                <div class="metric-value">${results.compliant ? 'Yes' : 'No'}</div>
                <div class="metric-label">WCAG Compliant</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">${results.testedPages.length}</div>
                <div class="metric-label">Pages Tested</div>
            </div>
            <div class="metric-card ${criticalCount === 0 ? 'good' : 'error'}">
                <div class="metric-value">${criticalCount}</div>
                <div class="metric-label">Critical Violations</div>
            </div>
            <div class="metric-card ${seriousCount === 0 ? 'good' : 'error'}">
                <div class="metric-value">${seriousCount}</div>
                <div class="metric-label">Serious Violations</div>
            </div>
            <div class="metric-card ${moderateCount === 0 ? 'good' : 'warning'}">
                <div class="metric-value">${moderateCount}</div>
                <div class="metric-label">Moderate Violations</div>
            </div>
            <div class="metric-card ${minorCount === 0 ? 'good' : 'warning'}">
                <div class="metric-value">${minorCount}</div>
                <div class="metric-label">Minor Violations</div>
            </div>
        </div>
    `;
  }

  /**
   * Generate PWA-specific content
   */
  private generatePWAContent(pwa: PWAValidationResult): string {
    return `
        <div class="metrics-grid">
            <div class="metric-card ${pwa.serviceWorkerRegistered ? 'good' : 'error'}">
                <div class="metric-value">${pwa.serviceWorkerRegistered ? 'Yes' : 'No'}</div>
                <div class="metric-label">Service Worker</div>
            </div>
            <div class="metric-card ${pwa.manifestValid ? 'good' : 'error'}">
                <div class="metric-value">${pwa.manifestValid ? 'Valid' : 'Invalid'}</div>
                <div class="metric-label">Manifest</div>
            </div>
            <div class="metric-card ${pwa.offlineFunctionality ? 'good' : 'warning'}">
                <div class="metric-value">${pwa.offlineFunctionality ? 'Yes' : 'No'}</div>
                <div class="metric-label">Offline Ready</div>
            </div>
            <div class="metric-card ${pwa.installable ? 'good' : 'warning'}">
                <div class="metric-value">${pwa.installable ? 'Yes' : 'No'}</div>
                <div class="metric-label">Installable</div>
            </div>
        </div>
    `;
  }

  /**
   * Generate dependency-specific content
   */
  private generateDependencyContent(deps: ExternalDependencies): string {
    const allDeps = [deps.googleMaps, ...deps.cdnResources, ...deps.apiEndpoints];
    const available = allDeps.filter(dep => dep.available).length;

    return `
        <div class="metrics-grid">
            <div class="metric-card ${available === allDeps.length ? 'good' : 'warning'}">
                <div class="metric-value">${available}/${allDeps.length}</div>
                <div class="metric-label">Dependencies Available</div>
            </div>
            <div class="metric-card ${deps.googleMaps.available ? 'good' : 'error'}">
                <div class="metric-value">${deps.googleMaps.available ? 'Available' : 'Unavailable'}</div>
                <div class="metric-label">Google Maps API</div>
            </div>
            ${deps.googleMaps.available ? `
            <div class="metric-card">
                <div class="metric-value">${deps.googleMaps.responseTime}ms</div>
                <div class="metric-label">Response Time</div>
            </div>
            ` : ''}
        </div>
    `;
  }

  /**
   * Generate footer
   */
  private generateFooter(): string {
    return `
        <div class="footer">
            <p>Generated by Production Deployment Verification System</p>
            <p>Report generated at ${new Date().toLocaleString()}</p>
        </div>
    `;
  }

  /**
   * Generate JavaScript for interactive features
   */
  private generateJavaScript(): string {
    return `
        function toggleSection(index) {
            const content = document.getElementById('content-' + index);
            const toggle = document.getElementById('toggle-' + index);
            
            if (content.classList.contains('collapsed')) {
                content.classList.remove('collapsed');
                toggle.classList.remove('collapsed');
                toggle.textContent = '▼';
            } else {
                content.classList.add('collapsed');
                toggle.classList.add('collapsed');
                toggle.textContent = '▶';
            }
        }

        // Initialize all sections as expanded
        document.addEventListener('DOMContentLoaded', function() {
            // Add any initialization code here
            console.log('Verification report loaded');
        });
    `;
  }

  /**
   * Save HTML report to file
   */
  private async saveHTMLReport(htmlContent: string): Promise<string> {
    const timestamp = this.options.includeTimestamp ? 
      `-${new Date().toISOString().replace(/[:.]/g, '-')}` : '';
    const filename = `verification-report${timestamp}.html`;
    const filepath = path.join(this.options.outputDir, filename);
    
    await fs.writeFile(filepath, htmlContent);
    return filepath;
  }
}

/**
 * Factory function to create report generator
 */
export function createReportGenerator(options: ReportGeneratorOptions): ReportGenerator {
  return new ReportGenerator(options);
}

/**
 * Utility function to generate quick report
 */
export async function generateVerificationReport(
  report: VerificationReport,
  outputDir: string = './verification-reports'
): Promise<{
  jsonPath: string;
  htmlPath: string;
  summary: ReportSummary;
}> {
  const generator = createReportGenerator({
    outputDir,
    includeTimestamp: true,
    includeDetailedLogs: true,
  });

  return await generator.generateReport(report);
}