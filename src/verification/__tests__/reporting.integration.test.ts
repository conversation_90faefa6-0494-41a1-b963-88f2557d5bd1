/**
 * Integration tests for the comprehensive reporting system
 */

import { promises as fs } from 'fs';
import path from 'path';
import { tmpdir } from 'os';
import { 
  ReportGenerator, 
  generateVerificationReport,
  ReportGeneratorOptions 
} from '../reporting';
import { VerificationReport } from '../types';

describe('Reporting Integration Tests', () => {
  let tempDir: string;
  let mockReport: VerificationReport;

  beforeEach(async () => {
    // Create temporary directory for test outputs
    tempDir = await fs.mkdtemp(path.join(tmpdir(), 'verification-reports-'));
    
    // Create comprehensive mock report
    mockReport = {
      timestamp: new Date('2024-01-15T10:30:00Z'),
      overallStatus: 'warning',
      buildVerification: {
        success: true,
        buildTime: 12500,
        errors: [],
        warnings: [
          {
            file: 'src/components/Header.tsx',
            line: 25,
            column: 10,
            message: 'Unused variable "oldState"',
            type: 'typescript'
          },
          {
            file: 'src/utils/helpers.ts',
            line: 42,
            column: 15,
            message: 'Function parameter never used',
            type: 'typescript'
          }
        ],
        outputSize: {
          total: 3145728, // 3MB
          chunks: [
            { name: 'main', size: 1572864, modules: ['src/main.tsx', 'src/App.tsx'] },
            { name: 'vendor', size: 1048576, modules: ['node_modules/react', 'node_modules/react-dom'] },
            { name: 'utils', size: 524288, modules: ['src/utils/index.ts'] }
          ]
        }
      },
      testResults: [
        {
          passed: true,
          duration: 8500,
          testCount: 45,
          failures: [],
          coverage: {
            lines: 88,
            functions: 92,
            branches: 85,
            statements: 89
          }
        },
        {
          passed: true,
          duration: 12000,
          testCount: 23,
          failures: [],
          coverage: {
            lines: 75,
            functions: 80,
            branches: 70,
            statements: 76
          }
        },
        {
          passed: false,
          duration: 5500,
          testCount: 15,
          failures: [
            {
              testName: 'Navigation component should handle mobile menu',
              error: 'Expected element to be visible but it was hidden',
              stack: 'Error: Expected element to be visible but it was hidden\n    at navigation.test.tsx:45:12',
              duration: 250
            },
            {
              testName: 'Search functionality should filter results',
              error: 'Timeout waiting for search results',
              stack: 'Error: Timeout waiting for search results\n    at search.test.tsx:78:20',
              duration: 5000
            }
          ]
        }
      ],
      performanceMetrics: {
        lcp: 2800, // Above threshold
        fid: 120,  // Above threshold
        cls: 0.15, // Above threshold
        fcp: 2100,
        lighthouse: {
          performance: 78, // Below threshold
          accessibility: 94,
          bestPractices: 85,
          seo: 92
        }
      },
      accessibilityResults: {
        compliant: false,
        violations: [
          {
            rule: 'color-contrast',
            impact: 'serious',
            element: 'button.secondary',
            description: 'Element has insufficient color contrast of 3.2:1 (minimum required is 4.5:1)'
          },
          {
            rule: 'keyboard-navigation',
            impact: 'critical',
            element: 'div.modal-overlay',
            description: 'Modal cannot be closed with keyboard navigation'
          },
          {
            rule: 'alt-text',
            impact: 'moderate',
            element: 'img.product-thumbnail',
            description: 'Image missing alternative text'
          },
          {
            rule: 'heading-order',
            impact: 'minor',
            element: 'h4.section-title',
            description: 'Heading levels should increase by one'
          }
        ],
        warnings: [
          {
            rule: 'aria-labels',
            element: 'button.icon-only',
            description: 'Icon-only button should have aria-label'
          },
          {
            rule: 'focus-visible',
            element: 'input.search',
            description: 'Focus indicator could be more prominent'
          }
        ],
        testedPages: ['/', '/team-sales', '/harbor-city', '/products']
      },
      pwaValidation: {
        serviceWorkerRegistered: true,
        manifestValid: true,
        offlineFunctionality: false, // Issue here
        installable: true,
        cacheStrategy: {
          staticAssetsCache: true,
          apiResponseCache: false, // Issue here
          offlinePages: ['/', '/offline']
        }
      },
      dependencyStatus: {
        googleMaps: {
          service: 'Google Maps API',
          available: true,
          responseTime: 180
        },
        cdnResources: [
          {
            service: 'Google Fonts',
            available: true,
            responseTime: 95
          },
          {
            service: 'Font Awesome CDN',
            available: false, // Issue here
            responseTime: 0,
            error: 'Connection timeout'
          }
        ],
        apiEndpoints: [
          {
            service: 'Weather API',
            available: true,
            responseTime: 220
          }
        ]
      },
      recommendations: [
        'Address critical accessibility violations before deployment',
        'Optimize performance metrics to meet thresholds',
        'Fix failing test cases',
        'Implement proper offline functionality for PWA'
      ],
      deploymentReady: false
    };
  });

  afterEach(async () => {
    // Clean up temporary directory
    try {
      await fs.rm(tempDir, { recursive: true, force: true });
    } catch (error) {
      console.warn('Failed to clean up temp directory:', error);
    }
  });

  describe('End-to-End Report Generation', () => {
    it('should generate complete reports with real file system operations', async () => {
      const options: ReportGeneratorOptions = {
        outputDir: tempDir,
        includeTimestamp: true,
        includeDetailedLogs: true
      };

      const generator = new ReportGenerator(options);
      const result = await generator.generateReport(mockReport);

      // Verify files were created
      expect(await fs.access(result.jsonPath)).resolves.toBeUndefined();
      expect(await fs.access(result.htmlPath)).resolves.toBeUndefined();

      // Verify file contents
      const jsonContent = await fs.readFile(result.jsonPath, 'utf-8');
      const htmlContent = await fs.readFile(result.htmlPath, 'utf-8');

      expect(jsonContent).toBeTruthy();
      expect(htmlContent).toBeTruthy();

      // Verify JSON structure
      const jsonData = JSON.parse(jsonContent);
      expect(jsonData).toHaveProperty('metadata');
      expect(jsonData).toHaveProperty('summary');
      expect(jsonData).toHaveProperty('deploymentDecision');
      expect(jsonData).toHaveProperty('originalReport');

      // Verify HTML structure
      expect(htmlContent).toContain('<!DOCTYPE html>');
      expect(htmlContent).toContain('Production Deployment Verification Report');
      expect(htmlContent).toContain('deployment-status');
    });

    it('should handle complex deployment decision logic', async () => {
      const generator = new ReportGenerator({ outputDir: tempDir });
      const result = await generator.generateReport(mockReport);

      expect(result.summary.deploymentReady).toBe(false);
      expect(result.summary.overallStatus).toBe('warning');
      expect(result.summary.criticalIssues.length).toBeGreaterThan(0);

      // Verify JSON contains deployment decision
      const jsonContent = await fs.readFile(result.jsonPath, 'utf-8');
      const jsonData = JSON.parse(jsonContent);
      
      expect(jsonData.deploymentDecision.ready).toBe(false);
      expect(jsonData.deploymentDecision.confidence).toBeLessThan(100);
      expect(jsonData.deploymentDecision.blockers.length).toBeGreaterThan(0);
      expect(jsonData.deploymentDecision.warnings.length).toBeGreaterThan(0);
    });

    it('should generate accurate summary statistics', async () => {
      const generator = new ReportGenerator({ outputDir: tempDir });
      const result = await generator.generateReport(mockReport);

      const summary = result.summary;

      // Verify counts make sense
      expect(summary.totalChecks).toBeGreaterThan(0);
      expect(summary.passedChecks + summary.failedChecks + summary.warningChecks).toBe(summary.totalChecks);
      
      // Should have some failures due to our mock data
      expect(summary.failedChecks).toBeGreaterThan(0);
      expect(summary.warningChecks).toBeGreaterThan(0);

      // Should have critical issues
      expect(summary.criticalIssues.length).toBeGreaterThan(0);
      expect(summary.recommendations.length).toBeGreaterThan(0);
    });

    it('should create detailed sections for all verification areas', async () => {
      const generator = new ReportGenerator({ outputDir: tempDir });
      const result = await generator.generateReport(mockReport);

      const jsonContent = await fs.readFile(result.jsonPath, 'utf-8');
      const jsonData = JSON.parse(jsonContent);

      const sections = jsonData.detailedSections;
      expect(sections).toHaveLength(6); // Build, Test, Performance, Accessibility, PWA, Dependencies

      const sectionNames = sections.map((s: any) => s.name);
      expect(sectionNames).toContain('Build Verification');
      expect(sectionNames).toContain('Test Results');
      expect(sectionNames).toContain('Performance Metrics');
      expect(sectionNames).toContain('Accessibility Validation');
      expect(sectionNames).toContain('PWA Validation');
      expect(sectionNames).toContain('External Dependencies');

      // Verify each section has required properties
      sections.forEach((section: any) => {
        expect(section).toHaveProperty('name');
        expect(section).toHaveProperty('status');
        expect(section).toHaveProperty('summary');
        expect(section).toHaveProperty('details');
        expect(section).toHaveProperty('errors');
        expect(section).toHaveProperty('warnings');
        expect(section).toHaveProperty('recommendations');
      });
    });

    it('should generate responsive HTML with interactive features', async () => {
      const generator = new ReportGenerator({ outputDir: tempDir });
      const result = await generator.generateReport(mockReport);

      const htmlContent = await fs.readFile(result.htmlPath, 'utf-8');

      // Check for responsive design elements
      expect(htmlContent).toContain('viewport');
      expect(htmlContent).toContain('@media (max-width: 768px)');

      // Check for interactive JavaScript
      expect(htmlContent).toContain('function toggleSection');
      expect(htmlContent).toContain('onclick="toggleSection');

      // Check for CSS styling
      expect(htmlContent).toContain('.container');
      expect(htmlContent).toContain('.summary-dashboard');
      expect(htmlContent).toContain('.deployment-status');

      // Check for specific content from our mock data
      expect(htmlContent).toContain('2800ms'); // LCP value
      expect(htmlContent).toContain('color-contrast'); // Accessibility violation
      expect(htmlContent).toContain('Google Maps API'); // Dependency
    });

    it('should handle custom themes correctly', async () => {
      const customTheme = {
        primaryColor: '#8b5cf6',
        successColor: '#10b981',
        warningColor: '#f59e0b',
        errorColor: '#ef4444',
        backgroundColor: '#f9fafb',
        textColor: '#111827'
      };

      const options: ReportGeneratorOptions = {
        outputDir: tempDir,
        customTheme
      };

      const generator = new ReportGenerator(options);
      const result = await generator.generateReport(mockReport);

      const htmlContent = await fs.readFile(result.htmlPath, 'utf-8');

      // Verify custom colors are used in CSS
      expect(htmlContent).toContain(customTheme.primaryColor);
      expect(htmlContent).toContain(customTheme.successColor);
      expect(htmlContent).toContain(customTheme.warningColor);
      expect(htmlContent).toContain(customTheme.errorColor);
    });

    it('should preserve all original data in JSON report', async () => {
      const generator = new ReportGenerator({ outputDir: tempDir });
      const result = await generator.generateReport(mockReport);

      const jsonContent = await fs.readFile(result.jsonPath, 'utf-8');
      const jsonData = JSON.parse(jsonContent);

      // Verify original report is preserved
      const originalReport = jsonData.originalReport;
      expect(originalReport.timestamp).toBe(mockReport.timestamp.toISOString());
      expect(originalReport.overallStatus).toBe(mockReport.overallStatus);
      
      // Verify build verification data
      expect(originalReport.buildVerification.success).toBe(mockReport.buildVerification.success);
      expect(originalReport.buildVerification.buildTime).toBe(mockReport.buildVerification.buildTime);
      expect(originalReport.buildVerification.warnings).toHaveLength(mockReport.buildVerification.warnings.length);

      // Verify test results data
      expect(originalReport.testResults).toHaveLength(mockReport.testResults.length);
      expect(originalReport.testResults[2].failures).toHaveLength(2);

      // Verify performance metrics
      expect(originalReport.performanceMetrics.lcp).toBe(mockReport.performanceMetrics.lcp);
      expect(originalReport.performanceMetrics.lighthouse.performance).toBe(mockReport.performanceMetrics.lighthouse.performance);

      // Verify accessibility results
      expect(originalReport.accessibilityResults.violations).toHaveLength(4);
      expect(originalReport.accessibilityResults.warnings).toHaveLength(2);

      // Verify PWA validation
      expect(originalReport.pwaValidation.offlineFunctionality).toBe(false);
      expect(originalReport.pwaValidation.cacheStrategy.apiResponseCache).toBe(false);

      // Verify dependency status
      expect(originalReport.dependencyStatus.cdnResources[1].available).toBe(false);
    });
  });

  describe('Factory Function Integration', () => {
    it('should work with generateVerificationReport utility', async () => {
      const result = await generateVerificationReport(mockReport, tempDir);

      // Verify files exist
      expect(await fs.access(result.jsonPath)).resolves.toBeUndefined();
      expect(await fs.access(result.htmlPath)).resolves.toBeUndefined();

      // Verify timestamp is included in filenames (default behavior)
      expect(result.jsonPath).toMatch(/verification-report-\d{4}-\d{2}-\d{2}T\d{2}-\d{2}-\d{2}/);
      expect(result.htmlPath).toMatch(/verification-report-\d{4}-\d{2}-\d{2}T\d{2}-\d{2}-\d{2}/);

      // Verify content quality
      const jsonContent = await fs.readFile(result.jsonPath, 'utf-8');
      const jsonData = JSON.parse(jsonContent);
      
      expect(jsonData.metadata.reportType).toBe('production-deployment-verification');
      expect(jsonData.summary.deploymentReady).toBe(false);
      expect(jsonData.deploymentDecision.confidence).toBeLessThan(100);
    });
  });

  describe('Error Handling', () => {
    it('should handle file system errors gracefully', async () => {
      // Try to write to a non-existent directory without creating it
      const invalidDir = path.join(tempDir, 'non-existent', 'deeply', 'nested');
      
      const generator = new ReportGenerator({ outputDir: invalidDir });
      
      // Should create directories automatically
      const result = await generator.generateReport(mockReport);
      
      expect(await fs.access(result.jsonPath)).resolves.toBeUndefined();
      expect(await fs.access(result.htmlPath)).resolves.toBeUndefined();
    });

    it('should handle malformed report data', async () => {
      const malformedReport = {
        ...mockReport,
        buildVerification: {
          ...mockReport.buildVerification,
          outputSize: {
            total: NaN,
            chunks: []
          }
        },
        performanceMetrics: {
          ...mockReport.performanceMetrics,
          lcp: Infinity,
          cls: -1
        }
      };

      const generator = new ReportGenerator({ outputDir: tempDir });
      
      // Should not throw, but handle gracefully
      const result = await generator.generateReport(malformedReport);
      
      expect(result).toHaveProperty('jsonPath');
      expect(result).toHaveProperty('htmlPath');
      expect(result).toHaveProperty('summary');
    });
  });

  describe('Performance and Scalability', () => {
    it('should handle large reports efficiently', async () => {
      // Create a report with many test results and violations
      const largeReport = {
        ...mockReport,
        testResults: Array(50).fill(null).map((_, i) => ({
          passed: i % 3 !== 0, // Some failures
          duration: Math.random() * 10000,
          testCount: Math.floor(Math.random() * 100) + 10,
          failures: i % 3 === 0 ? [{
            testName: `Test suite ${i} failure`,
            error: `Error in test suite ${i}`,
            duration: Math.random() * 1000
          }] : []
        })),
        accessibilityResults: {
          ...mockReport.accessibilityResults,
          violations: Array(100).fill(null).map((_, i) => ({
            rule: `rule-${i}`,
            impact: ['critical', 'serious', 'moderate', 'minor'][i % 4] as any,
            element: `element-${i}`,
            description: `Violation ${i} description`
          }))
        }
      };

      const startTime = Date.now();
      const generator = new ReportGenerator({ outputDir: tempDir });
      const result = await generator.generateReport(largeReport);
      const endTime = Date.now();

      // Should complete within reasonable time (less than 5 seconds)
      expect(endTime - startTime).toBeLessThan(5000);

      // Verify files were created successfully
      expect(await fs.access(result.jsonPath)).resolves.toBeUndefined();
      expect(await fs.access(result.htmlPath)).resolves.toBeUndefined();

      // Verify content includes all data
      const jsonContent = await fs.readFile(result.jsonPath, 'utf-8');
      const jsonData = JSON.parse(jsonContent);
      
      expect(jsonData.originalReport.testResults).toHaveLength(50);
      expect(jsonData.originalReport.accessibilityResults.violations).toHaveLength(100);
    });
  });
});