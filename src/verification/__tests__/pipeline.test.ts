/**
 * Unit tests for the main verification pipeline
 */

import { VerificationPipeline, runVerification } from '../pipeline';
import { PipelineOptions, VerificationConfig, VerificationReport } from '../types';
import { ConfigManager } from '../config';
import { <PERSON>rror<PERSON>and<PERSON>, VerificationLogger } from '../error-handling';

// Mock all dependencies
jest.mock('../config');
jest.mock('../build');
jest.mock('../test-orchestrator');
jest.mock('../performance');
jest.mock('../accessibility');
jest.mock('../error-handling');
jest.mock('@playwright/test');

const mockConfigManager = ConfigManager as jest.MockedClass<typeof ConfigManager>;
const mockErrorHandler = {
  executeWithErrorHandling: jest.fn(),
  handleError: jest.fn(),
  createError: jest.fn(),
  getErrorStatistics: jest.fn(),
  cleanup: jest.fn(),
} as jest.Mocked<ErrorHandler>;

const mockLogger = {
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
  debug: jest.fn(),
} as jest.Mocked<VerificationLogger>;

describe('VerificationPipeline', () => {
  let pipeline: VerificationPipeline;
  let mockConfig: VerificationConfig;
  let mockOptions: PipelineOptions;

  beforeEach(() => {
    jest.clearAllMocks();

    mockConfig = {
      buildSettings: {
        mode: 'production',
        sourceMaps: false,
        minification: true,
      },
      performanceThresholds: {
        lcp: 2500,
        fid: 100,
        cls: 0.1,
        lighthousePerformance: 90,
      },
      accessibilityLevel: 'AA',
      testSuites: [
        {
          name: 'unit',
          type: 'unit',
          enabled: true,
          timeout: 30000,
          retries: 0,
        },
      ],
      externalDependencies: [
        {
          name: 'Google Maps',
          url: 'https://maps.googleapis.com/maps/api/js',
          timeout: 5000,
          critical: true,
        },
      ],
    };

    mockOptions = {
      config: mockConfig,
      verbose: false,
    };

    // Mock ConfigManager
    mockConfigManager.prototype.validateConfig = jest.fn().mockReturnValue({
      valid: true,
      errors: [],
    });
    mockConfigManager.prototype.getConfig = jest.fn().mockReturnValue(mockConfig);

    // Mock error handler and logger creation
    require('../error-handling').createErrorHandler = jest.fn().mockReturnValue(mockErrorHandler);
    require('../error-handling').createLogger = jest.fn().mockReturnValue(mockLogger);

    pipeline = new VerificationPipeline(mockOptions);
  });

  describe('constructor', () => {
    it('should initialize with provided options', () => {
      expect(pipeline).toBeInstanceOf(VerificationPipeline);
      expect(mockConfigManager).toHaveBeenCalledWith(mockConfig);
    });

    it('should create error handler with correct configuration', () => {
      const createErrorHandler = require('../error-handling').createErrorHandler;
      expect(createErrorHandler).toHaveBeenCalledWith(
        expect.objectContaining({
          level: 'info',
          enableConsole: true,
          enableFile: true,
        }),
        expect.objectContaining({
          maxRetries: 3,
          baseDelay: 1000,
          maxDelay: 30000,
        })
      );
    });

    it('should use debug level when verbose is true', () => {
      const verboseOptions = { ...mockOptions, verbose: true };
      new VerificationPipeline(verboseOptions);

      const createErrorHandler = require('../error-handling').createErrorHandler;
      expect(createErrorHandler).toHaveBeenCalledWith(
        expect.objectContaining({
          level: 'debug',
        }),
        expect.any(Object)
      );
    });
  });

  describe('execute', () => {
    beforeEach(() => {
      // Mock successful stage executions
      mockErrorHandler.executeWithErrorHandling.mockImplementation(async (fn) => {
        return await fn();
      });

      mockErrorHandler.getErrorStatistics.mockReturnValue({
        totalErrors: 0,
        errorsByType: {},
        errorsByStage: {},
        recoveryAttempts: 0,
        successfulRecoveries: 0,
      });

      mockLogger.info.mockResolvedValue(undefined);
      mockLogger.warn.mockResolvedValue(undefined);
      mockLogger.error.mockResolvedValue(undefined);
    });

    it('should execute all stages successfully', async () => {
      // Mock all stages to return successful results
      const mockStageResult = {
        success: true,
        duration: 1000,
        data: {},
        errors: [],
        warnings: [],
      };

      mockErrorHandler.executeWithErrorHandling.mockResolvedValue(mockStageResult);

      const report = await pipeline.execute();

      expect(report.overallStatus).toBe('passed');
      expect(report.deploymentReady).toBe(true);
      expect(report.timestamp).toBeInstanceOf(Date);
      expect(mockErrorHandler.executeWithErrorHandling).toHaveBeenCalledTimes(7); // 6 stages + config validation
    });

    it('should handle configuration validation failure', async () => {
      mockConfigManager.prototype.validateConfig.mockReturnValue({
        valid: false,
        errors: ['Invalid configuration'],
      });

      mockErrorHandler.executeWithErrorHandling.mockRejectedValueOnce(
        new Error('Configuration validation failed: Invalid configuration')
      );

      const report = await pipeline.execute();

      expect(report.overallStatus).toBe('failed');
      expect(report.deploymentReady).toBe(false);
    });

    it('should handle stage execution failures', async () => {
      const failedStageResult = {
        success: false,
        duration: 1000,
        data: null,
        errors: ['Stage failed'],
        warnings: [],
      };

      mockErrorHandler.executeWithErrorHandling
        .mockResolvedValueOnce(true) // Config validation
        .mockResolvedValueOnce(failedStageResult) // First stage fails
        .mockResolvedValue({ success: true, duration: 1000, data: {}, errors: [], warnings: [] }); // Other stages succeed

      const report = await pipeline.execute();

      expect(report.overallStatus).toBe('failed');
      expect(report.deploymentReady).toBe(false);
      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining('failed'),
        expect.any(Object)
      );
    });

    it('should handle stage execution warnings', async () => {
      const warningStageResult = {
        success: true,
        duration: 1000,
        data: {},
        errors: [],
        warnings: ['Stage warning'],
      };

      mockErrorHandler.executeWithErrorHandling
        .mockResolvedValueOnce(true) // Config validation
        .mockResolvedValueOnce(warningStageResult) // First stage has warnings
        .mockResolvedValue({ success: true, duration: 1000, data: {}, errors: [], warnings: [] }); // Other stages succeed

      const report = await pipeline.execute();

      expect(report.overallStatus).toBe('warning');
      expect(report.deploymentReady).toBe(true);
      expect(mockLogger.warn).toHaveBeenCalledWith(
        expect.stringContaining('warnings'),
        expect.any(Object)
      );
    });

    it('should skip stages when specified in options', async () => {
      const skipOptions = { ...mockOptions, skipStages: ['performance', 'accessibility'] };
      const skipPipeline = new VerificationPipeline(skipOptions);

      mockErrorHandler.executeWithErrorHandling.mockResolvedValue({
        success: true,
        duration: 1000,
        data: {},
        errors: [],
        warnings: [],
      });

      await skipPipeline.execute();

      // Should skip 2 stages, so fewer calls to executeWithErrorHandling
      expect(mockErrorHandler.executeWithErrorHandling).toHaveBeenCalledTimes(5); // 4 stages + config validation
      expect(mockLogger.info).toHaveBeenCalledWith('Skipping stage: performance');
      expect(mockLogger.info).toHaveBeenCalledWith('Skipping stage: accessibility');
    });

    it('should handle critical pipeline errors', async () => {
      const criticalError = new Error('Critical pipeline failure');
      mockErrorHandler.executeWithErrorHandling.mockRejectedValue(criticalError);

      mockErrorHandler.createError.mockReturnValue({
        id: 'error-1',
        timestamp: new Date(),
        stage: 'pipeline',
        type: 'system',
        severity: 'critical',
        message: 'Pipeline execution failed',
        context: {},
        recoverable: false,
      });

      const report = await pipeline.execute();

      expect(report.overallStatus).toBe('failed');
      expect(report.deploymentReady).toBe(false);
      expect(report.recommendations).toContain(
        expect.stringContaining('Pipeline execution failed')
      );
      expect(mockErrorHandler.handleError).toHaveBeenCalled();
      expect(mockErrorHandler.cleanup).toHaveBeenCalled();
    });

    it('should log error statistics when errors occur', async () => {
      mockErrorHandler.getErrorStatistics.mockReturnValue({
        totalErrors: 5,
        errorsByType: { build: 2, test: 3 },
        errorsByStage: { build: 2, tests: 3 },
        recoveryAttempts: 2,
        successfulRecoveries: 1,
      });

      mockErrorHandler.executeWithErrorHandling.mockResolvedValue({
        success: true,
        duration: 1000,
        data: {},
        errors: [],
        warnings: [],
      });

      await pipeline.execute();

      expect(mockLogger.info).toHaveBeenCalledWith(
        'Error statistics for this run',
        expect.objectContaining({
          totalErrors: 5,
          errorsByType: { build: 2, test: 3 },
        })
      );
    });

    it('should always cleanup error handler resources', async () => {
      mockErrorHandler.executeWithErrorHandling.mockResolvedValue({
        success: true,
        duration: 1000,
        data: {},
        errors: [],
        warnings: [],
      });

      await pipeline.execute();

      expect(mockErrorHandler.cleanup).toHaveBeenCalled();
    });
  });

  describe('generateRecommendations', () => {
    it('should generate recommendations for failed stages', async () => {
      const failedStageResult = {
        success: false,
        duration: 1000,
        data: null,
        errors: ['Build failed'],
        warnings: [],
      };

      const warningStageResult = {
        success: true,
        duration: 1000,
        data: {},
        errors: [],
        warnings: ['Performance warning'],
      };

      mockErrorHandler.executeWithErrorHandling
        .mockResolvedValueOnce(true) // Config validation
        .mockResolvedValueOnce(failedStageResult) // Build fails
        .mockResolvedValueOnce(warningStageResult) // Performance has warnings
        .mockResolvedValue({ success: true, duration: 1000, data: {}, errors: [], warnings: [] }); // Other stages succeed

      const report = await pipeline.execute();

      expect(report.recommendations).toContain('Fix build issues before deployment');
      expect(report.recommendations).toContain('Address performance warnings for optimal performance');
    });

    it('should generate positive recommendation when all stages pass', async () => {
      mockErrorHandler.executeWithErrorHandling.mockResolvedValue({
        success: true,
        duration: 1000,
        data: {},
        errors: [],
        warnings: [],
      });

      const report = await pipeline.execute();

      expect(report.recommendations).toContain('All verification checks passed - ready for deployment');
    });
  });

  describe('stage timeout configuration', () => {
    it('should use appropriate timeouts for different stages', async () => {
      mockErrorHandler.executeWithErrorHandling.mockResolvedValue({
        success: true,
        duration: 1000,
        data: {},
        errors: [],
        warnings: [],
      });

      await pipeline.execute();

      // Verify that executeWithErrorHandling was called with correct timeout options
      const calls = mockErrorHandler.executeWithErrorHandling.mock.calls;
      
      // Find build stage call
      const buildCall = calls.find(call => call[1]?.operationName?.includes('build'));
      expect(buildCall?.[1]?.timeout).toBe(300000); // 5 minutes

      // Find test stage call
      const testCall = calls.find(call => call[1]?.operationName?.includes('tests'));
      expect(testCall?.[1]?.timeout).toBe(600000); // 10 minutes

      // Find performance stage call
      const perfCall = calls.find(call => call[1]?.operationName?.includes('performance'));
      expect(perfCall?.[1]?.timeout).toBe(900000); // 15 minutes
    });
  });
});

describe('runVerification factory function', () => {
  beforeEach(() => {
    jest.clearAllMocks();

    // Mock all the dependencies
    mockConfigManager.prototype.validateConfig = jest.fn().mockReturnValue({
      valid: true,
      errors: [],
    });
    mockConfigManager.prototype.getConfig = jest.fn().mockReturnValue({
      buildSettings: { mode: 'production', sourceMaps: false, minification: true },
      performanceThresholds: { lcp: 2500, fid: 100, cls: 0.1, lighthousePerformance: 90 },
      accessibilityLevel: 'AA',
      testSuites: [],
      externalDependencies: [],
    });

    require('../error-handling').createErrorHandler = jest.fn().mockReturnValue(mockErrorHandler);
    require('../error-handling').createLogger = jest.fn().mockReturnValue(mockLogger);

    mockErrorHandler.executeWithErrorHandling.mockResolvedValue({
      success: true,
      duration: 1000,
      data: {},
      errors: [],
      warnings: [],
    });

    mockErrorHandler.getErrorStatistics.mockReturnValue({
      totalErrors: 0,
      errorsByType: {},
      errorsByStage: {},
      recoveryAttempts: 0,
      successfulRecoveries: 0,
    });

    mockLogger.info.mockResolvedValue(undefined);
  });

  it('should create and execute verification pipeline', async () => {
    const options: PipelineOptions = {
      config: {
        buildSettings: { mode: 'production', sourceMaps: false, minification: true },
        performanceThresholds: { lcp: 2500, fid: 100, cls: 0.1, lighthousePerformance: 90 },
        accessibilityLevel: 'AA',
        testSuites: [],
        externalDependencies: [],
      },
      verbose: true,
    };

    const report = await runVerification(options);

    expect(report).toHaveProperty('timestamp');
    expect(report).toHaveProperty('overallStatus');
    expect(report).toHaveProperty('deploymentReady');
    expect(report.overallStatus).toBe('passed');
  });

  it('should handle pipeline creation errors', async () => {
    const invalidOptions: PipelineOptions = {
      config: {} as VerificationConfig, // Invalid config
    };

    // Mock config validation to fail
    mockConfigManager.prototype.validateConfig.mockReturnValue({
      valid: false,
      errors: ['Invalid configuration'],
    });

    mockErrorHandler.executeWithErrorHandling.mockRejectedValue(
      new Error('Configuration validation failed')
    );

    const report = await runVerification(invalidOptions);

    expect(report.overallStatus).toBe('failed');
    expect(report.deploymentReady).toBe(false);
  });
});