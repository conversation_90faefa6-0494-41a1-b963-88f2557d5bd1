/**
 * Integration tests for build verification engine
 * Tests the actual build process and integration with the pipeline
 */

import { BuildVerificationEngine } from '../build';
import { VerificationPipeline, runVerification } from '../pipeline';
import { DEFAULT_CONFIG } from '../config';

describe('Build Verification Integration', () => {
  let engine: BuildVerificationEngine;

  beforeEach(() => {
    engine = new BuildVerificationEngine();
  });

  describe('BuildVerificationEngine Integration', () => {
    it('should handle real build process gracefully', async () => {
      // This test runs the actual build process
      // It may take longer and could fail if there are real build issues
      const result = await engine.verify();

      // Basic assertions that should always be true
      expect(result).toBeDefined();
      expect(typeof result.success).toBe('boolean');
      expect(typeof result.buildTime).toBe('number');
      expect(Array.isArray(result.errors)).toBe(true);
      expect(Array.isArray(result.warnings)).toBe(true);
      expect(typeof result.outputSize.total).toBe('number');
      expect(Array.isArray(result.outputSize.chunks)).toBe(true);

      // If build succeeds, we should have some output
      if (result.success) {
        expect(result.buildTime).toBeGreaterThan(0);
        // Note: outputSize might be 0 if metrics collection fails, which is acceptable
      }

      // Verify build results
        buildTime: result.buildTime,
        errorCount: result.errors.length,
        warningCount: result.warnings.length,
        outputSize: result.outputSize.total,
        chunkCount: result.outputSize.chunks.length,
      });

      if (result.errors.length > 0) {
        console.log('Build errors:', result.errors);
      }

      if (result.warnings.length > 0) {
        console.log('Build warnings:', result.warnings);
      }
    }, 30000); // 30 second timeout for build process

    it('should provide meaningful error details when build fails', async () => {
      const result = await engine.verify();

      if (!result.success) {
        const detailedErrors = engine.getDetailedErrors();
        expect(detailedErrors.length).toBeGreaterThan(0);
        
        // Each error should have meaningful information
        detailedErrors.forEach(error => {
          expect(error).toContain(':');
          expect(error.length).toBeGreaterThan(10);
        });
      }
    }, 30000);

    it('should generate useful build summary', async () => {
      const result = await engine.verify();
      const summary = engine.getBuildSummary(result);

      expect(summary).toContain('Build');
      expect(summary).toContain('Time:');
      expect(summary).toContain('Errors:');
      expect(summary).toContain('Warnings:');
      expect(summary).toContain('Output Size:');
      expect(summary).toContain('Chunks:');

      console.log('Build summary:', summary);
    }, 30000);
  });

  describe('Pipeline Integration', () => {
    it('should integrate build verification with pipeline', async () => {
      const options = {
        config: DEFAULT_CONFIG,
        skipStages: ['tests', 'performance', 'accessibility', 'pwa', 'dependencies'],
        verbose: true,
        outputFormat: 'console' as const,
      };

      const report = await runVerification(options);

      expect(report).toBeDefined();
      expect(report.buildVerification).toBeDefined();
      expect(typeof report.buildVerification.success).toBe('boolean');
      expect(report.timestamp).toBeInstanceOf(Date);
      expect(['passed', 'failed', 'warning']).toContain(report.overallStatus);

      // If build verification fails, overall status should be failed
      if (!report.buildVerification.success) {
        expect(report.overallStatus).toBe('failed');
        expect(report.deploymentReady).toBe(false);
      }

      console.log('Pipeline report:', {
        overallStatus: report.overallStatus,
        deploymentReady: report.deploymentReady,
        buildSuccess: report.buildVerification.success,
        recommendations: report.recommendations,
      });
    }, 45000); // 45 second timeout for full pipeline

    it('should handle build stage errors gracefully in pipeline', async () => {
      const options = {
        config: DEFAULT_CONFIG,
        skipStages: ['tests', 'performance', 'accessibility', 'pwa', 'dependencies'],
        verbose: false,
      };

      const report = await runVerification(options);

      // Pipeline should always return a valid report
      expect(report).toBeDefined();
      expect(report.buildVerification).toBeDefined();
      expect(Array.isArray(report.recommendations)).toBe(true);
      expect(typeof report.deploymentReady).toBe('boolean');

      // If there are build errors, they should be reflected in recommendations
      if (!report.buildVerification.success) {
        expect(report.recommendations.some(rec => 
          rec.toLowerCase().includes('build')
        )).toBe(true);
      }
    }, 45000);
  });

  describe('Error Handling', () => {
    it('should handle build process errors in real scenarios', async () => {
      // This test verifies that the engine handles real build errors appropriately
      // We'll test with the actual build process and verify error handling structure
      const result = await engine.verify();

      // The result should always be well-formed regardless of success/failure
      expect(result).toBeDefined();
      expect(typeof result.success).toBe('boolean');
      expect(Array.isArray(result.errors)).toBe(true);
      expect(Array.isArray(result.warnings)).toBe(true);
      expect(typeof result.buildTime).toBe('number');
      expect(typeof result.outputSize.total).toBe('number');

      // If there are errors, they should be properly formatted
      if (result.errors.length > 0) {
        result.errors.forEach(error => {
          expect(error).toHaveProperty('file');
          expect(error).toHaveProperty('line');
          expect(error).toHaveProperty('column');
          expect(error).toHaveProperty('message');
          expect(error).toHaveProperty('type');
          expect(['typescript', 'bundling', 'asset']).toContain(error.type);
        });
      }

      // Detailed errors should be available
      const detailedErrors = engine.getDetailedErrors();
      expect(Array.isArray(detailedErrors)).toBe(true);
      
      // If build failed, detailed errors should match error count
      if (!result.success && result.errors.length > 0) {
        expect(detailedErrors.length).toBe(result.errors.length);
      }
    }, 30000);
  });
});