/**
 * Integration tests for Production Environment Testing Module
 * 
 * These tests verify the production environment testing functionality
 * by running against a real build and server setup.
 */

import { jest } from '@jest/globals';
import { promises as fs } from 'fs';
import path from 'path';
import { ProductionEnvironmentTester, DEFAULT_PRODUCTION_CONFIG } from '../production-environment';

// Increase timeout for integration tests
jest.setTimeout(120000);

describe('ProductionEnvironmentTester Integration', () => {
  let tester: ProductionEnvironmentTester;
  const testConfig = {
    ...DEFAULT_PRODUCTION_CONFIG,
    previewPort: 4174, // Use different port to avoid conflicts
    testTimeout: 60000,
    browsers: [
      { name: 'chromium' as const, enabled: true },
      { name: 'firefox' as const, enabled: false },
      { name: 'webkit' as const, enabled: false }
    ],
    criticalPages: ['/'], // Test only homepage for integration
    networkConditions: [
      {
        name: 'Fast 3G',
        downloadThroughput: 1.5 * 1024 * 1024 / 8,
        uploadThroughput: 750 * 1024 / 8,
        latency: 150
      }
    ]
  };

  beforeAll(async () => {
    tester = new ProductionEnvironmentTester(testConfig);
  });

  describe('Full Production Environment Test', () => {
    it('should run complete production environment test suite', async () => {
      // Skip if in CI environment without display
      if (process.env.CI && !process.env.DISPLAY) {
        console.log('Skipping integration test in CI environment without display');
        return;
      }

      const result = await tester.test();

      // Verify basic structure
      expect(result).toHaveProperty('buildSuccess');
      expect(result).toHaveProperty('serverStarted');
      expect(result).toHaveProperty('pageLoadTests');
      expect(result).toHaveProperty('functionalityTests');
      expect(result).toHaveProperty('networkConditionTests');
      expect(result).toHaveProperty('overallSuccess');
      expect(result).toHaveProperty('errors');
      expect(result).toHaveProperty('warnings');

      // Verify production environment test results

      // Build should succeed (this is a basic requirement)
      expect(result.buildSuccess).toBe(true);

      // If build succeeded, we should have attempted to start server
      if (result.buildSuccess) {
        expect(typeof result.serverStarted).toBe('boolean');
      }

      // If server started, we should have test results
      if (result.serverStarted) {
        expect(result.pageLoadTests.length).toBeGreaterThan(0);
        expect(result.functionalityTests.length).toBeGreaterThan(0);
        expect(result.networkConditionTests.length).toBeGreaterThan(0);
      }
    }, 120000);

    it('should generate detailed report', async () => {
      // Skip if in CI environment without display
      if (process.env.CI && !process.env.DISPLAY) {
        console.log('Skipping integration test in CI environment without display');
        return;
      }

      const result = await tester.test();
      const report = tester.generateReport(result);

      expect(report).toContain('Production Environment Test Report');
      expect(report).toContain('Overall Success:');
      expect(report).toContain('Build Success:');
      expect(report).toContain('Server Started:');

      // Log report for debugging
      console.log('\n=== Generated Report ===');
      console.log(report);
    }, 120000);
  });

  describe('Individual Test Components', () => {
    it('should successfully build production version', async () => {
      const buildResult = await (tester as any).buildProduction();
      
      expect(typeof buildResult).toBe('boolean');
      
      if (buildResult) {
        // Verify dist directory exists
        const distPath = path.resolve(process.cwd(), 'dist');
        const distExists = await fs.access(distPath).then(() => true).catch(() => false);
        expect(distExists).toBe(true);

        // Verify index.html exists
        const indexPath = path.join(distPath, 'index.html');
        const indexExists = await fs.access(indexPath).then(() => true).catch(() => false);
        expect(indexExists).toBe(true);

        // Verify assets directory exists
        const assetsPath = path.join(distPath, 'assets');
        const assetsExists = await fs.access(assetsPath).then(() => true).catch(() => false);
        expect(assetsExists).toBe(true);
      }
    }, 60000);

    it('should handle server startup and shutdown', async () => {
      // First ensure we have a build
      const buildResult = await (tester as any).buildProduction();
      
      if (!buildResult) {
        console.log('Skipping server test - build failed');
        return;
      }

      const serverStarted = await (tester as any).startPreviewServer();
      
      if (serverStarted) {
        // Server should be running
        expect(serverStarted).toBe(true);
        
        // Clean shutdown
        await (tester as any).stopPreviewServer();
        
        // Give server time to shut down
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
    }, 60000);
  });

  describe('Error Handling', () => {
    it('should handle build failures gracefully', async () => {
      // Create a tester with invalid build command
      const invalidTester = new ProductionEnvironmentTester({
        ...testConfig,
        buildCommand: 'npm run invalid-build-command'
      });

      const result = await invalidTester.test();

      expect(result.buildSuccess).toBe(false);
      expect(result.overallSuccess).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
    }, 60000);

    it('should handle server startup failures gracefully', async () => {
      // Create a tester with invalid preview command
      const invalidTester = new ProductionEnvironmentTester({
        ...testConfig,
        previewCommand: 'npm run invalid-preview-command'
      });

      const result = await invalidTester.test();

      // Build might succeed, but server should fail
      if (result.buildSuccess) {
        expect(result.serverStarted).toBe(false);
        expect(result.overallSuccess).toBe(false);
      }
    }, 60000);
  });

  describe('Configuration Validation', () => {
    it('should work with minimal configuration', async () => {
      const minimalConfig = {
        buildCommand: 'npm run build',
        previewCommand: 'npm run preview',
        previewPort: 4175,
        testTimeout: 30000,
        networkConditions: [],
        browsers: [{ name: 'chromium' as const, enabled: true }],
        criticalPages: ['/']
      };

      const minimalTester = new ProductionEnvironmentTester(minimalConfig);
      
      // Should not throw during construction
      expect(minimalTester).toBeInstanceOf(ProductionEnvironmentTester);
    });

    it('should handle custom network conditions', async () => {
      const customConfig = {
        ...testConfig,
        networkConditions: [
          {
            name: 'Custom Slow',
            downloadThroughput: 100 * 1024 / 8, // 100 Kbps
            uploadThroughput: 50 * 1024 / 8,    // 50 Kbps
            latency: 500
          }
        ]
      };

      const customTester = new ProductionEnvironmentTester(customConfig);
      expect(customTester).toBeInstanceOf(ProductionEnvironmentTester);
    });

    it('should handle multiple browsers configuration', async () => {
      const multiBrowserConfig = {
        ...testConfig,
        browsers: [
          { name: 'chromium' as const, enabled: true },
          { name: 'firefox' as const, enabled: true },
          { name: 'webkit' as const, enabled: false }
        ]
      };

      const multiBrowserTester = new ProductionEnvironmentTester(multiBrowserConfig);
      expect(multiBrowserTester).toBeInstanceOf(ProductionEnvironmentTester);
    });
  });

  describe('Performance Metrics', () => {
    it('should collect meaningful performance metrics', async () => {
      // Skip if in CI environment without display
      if (process.env.CI && !process.env.DISPLAY) {
        console.log('Skipping performance metrics test in CI environment');
        return;
      }

      const result = await tester.test();

      if (result.serverStarted && result.pageLoadTests.length > 0) {
        const pageLoadTest = result.pageLoadTests[0];
        
        expect(pageLoadTest.metrics).toBeDefined();
        expect(typeof pageLoadTest.metrics.domContentLoaded).toBe('number');
        expect(typeof pageLoadTest.metrics.resourceCount).toBe('number');
        expect(typeof pageLoadTest.metrics.totalSize).toBe('number');
        
        // Performance metrics should be reasonable
        expect(pageLoadTest.loadTime).toBeGreaterThan(0);
        expect(pageLoadTest.loadTime).toBeLessThan(30000); // Should load within 30 seconds
      }
    }, 120000);
  });

  describe('Cross-Browser Testing', () => {
    it('should test across enabled browsers', async () => {
      // Skip if in CI environment without display
      if (process.env.CI && !process.env.DISPLAY) {
        console.log('Skipping cross-browser test in CI environment');
        return;
      }

      const multiBrowserConfig = {
        ...testConfig,
        browsers: [
          { name: 'chromium' as const, enabled: true }
          // Only test Chromium in CI to avoid complexity
        ]
      };

      const multiBrowserTester = new ProductionEnvironmentTester(multiBrowserConfig);
      const result = await multiBrowserTester.test();

      if (result.serverStarted) {
        // Should have results for each enabled browser
        const enabledBrowsers = multiBrowserConfig.browsers.filter(b => b.enabled);
        const expectedTestCount = enabledBrowsers.length * multiBrowserConfig.criticalPages.length;
        
        expect(result.pageLoadTests.length).toBe(expectedTestCount);
        
        // Each test should specify which browser was used
        result.pageLoadTests.forEach(test => {
          expect(test.browser).toBeDefined();
          expect(['chromium', 'firefox', 'webkit']).toContain(test.browser);
        });
      }
    }, 120000);
  });
});