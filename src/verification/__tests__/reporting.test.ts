/**
 * Tests for the comprehensive reporting system
 */

import { promises as fs } from 'fs';
import path from 'path';
import { 
  ReportGenerator, 
  createReportGenerator, 
  generateVerificationReport,
  ReportGeneratorOptions,
  ReportSummary 
} from '../reporting';
import { VerificationReport } from '../types';

// Mock fs module
jest.mock('fs', () => ({
  promises: {
    mkdir: jest.fn(),
    writeFile: jest.fn(),
  },
}));

const mockFs = fs as jest.Mocked<typeof fs>;

describe('ReportGenerator', () => {
  let reportGenerator: ReportGenerator;
  let mockReport: VerificationReport;
  let options: ReportGeneratorOptions;

  beforeEach(() => {
    jest.clearAllMocks();
    
    options = {
      outputDir: './test-reports',
      includeTimestamp: false,
      includeDetailedLogs: true,
    };

    reportGenerator = new ReportGenerator(options);

    // Create comprehensive mock report
    mockReport = {
      timestamp: new Date('2024-01-15T10:30:00Z'),
      overallStatus: 'passed',
      buildVerification: {
        success: true,
        buildTime: 15000,
        errors: [],
        warnings: [
          {
            file: 'src/components/Header.tsx',
            line: 25,
            column: 10,
            message: 'Unused variable detected',
            type: 'typescript'
          }
        ],
        outputSize: {
          total: 2048000, // 2MB
          chunks: [
            { name: 'main', size: 1024000, modules: ['src/main.tsx'] },
            { name: 'vendor', size: 1024000, modules: ['node_modules/react'] }
          ]
        }
      },
      testResults: [
        {
          passed: true,
          duration: 5000,
          testCount: 25,
          failures: [],
          coverage: {
            lines: 85,
            functions: 90,
            branches: 80,
            statements: 85
          }
        },
        {
          passed: false,
          duration: 3000,
          testCount: 10,
          failures: [
            {
              testName: 'should render header correctly',
              error: 'Expected element to be visible',
              stack: 'Error: Expected element to be visible\n    at test.spec.ts:15',
              duration: 100
            }
          ]
        }
      ],
      performanceMetrics: {
        lcp: 2200,
        fid: 80,
        cls: 0.08,
        fcp: 1800,
        lighthouse: {
          performance: 92,
          accessibility: 95,
          bestPractices: 88,
          seo: 90
        }
      },
      accessibilityResults: {
        compliant: false,
        violations: [
          {
            rule: 'color-contrast',
            impact: 'serious',
            element: 'button.primary',
            description: 'Insufficient color contrast ratio'
          },
          {
            rule: 'alt-text',
            impact: 'minor',
            element: 'img.logo',
            description: 'Missing alt text for decorative image'
          }
        ],
        warnings: [
          {
            rule: 'heading-order',
            element: 'h3.subtitle',
            description: 'Heading levels should increase by one'
          }
        ],
        testedPages: ['/', '/team-sales', '/harbor-city']
      },
      pwaValidation: {
        serviceWorkerRegistered: true,
        manifestValid: true,
        offlineFunctionality: true,
        installable: true,
        cacheStrategy: {
          staticAssetsCache: true,
          apiResponseCache: false,
          offlinePages: ['/', '/offline']
        }
      },
      dependencyStatus: {
        googleMaps: {
          service: 'Google Maps API',
          available: true,
          responseTime: 150
        },
        cdnResources: [
          {
            service: 'Google Fonts',
            available: true,
            responseTime: 100
          }
        ],
        apiEndpoints: []
      },
      recommendations: [
        'Address accessibility violations for better compliance',
        'Fix failing test cases before deployment'
      ],
      deploymentReady: false
    };
  });

  describe('constructor', () => {
    it('should initialize with provided options', () => {
      expect(reportGenerator).toBeInstanceOf(ReportGenerator);
    });

    it('should use default theme when no custom theme provided', () => {
      const generator = new ReportGenerator({ outputDir: './test' });
      expect(generator).toBeInstanceOf(ReportGenerator);
    });

    it('should use custom theme when provided', () => {
      const customTheme = {
        primaryColor: '#ff0000',
        successColor: '#00ff00',
        warningColor: '#ffff00',
        errorColor: '#ff0000',
        backgroundColor: '#ffffff',
        textColor: '#000000'
      };

      const generator = new ReportGenerator({
        outputDir: './test',
        customTheme
      });

      expect(generator).toBeInstanceOf(ReportGenerator);
    });
  });

  describe('generateReport', () => {
    beforeEach(() => {
      mockFs.mkdir.mockResolvedValue(undefined);
      mockFs.writeFile.mockResolvedValue(undefined);
    });

    it('should generate both JSON and HTML reports', async () => {
      const result = await reportGenerator.generateReport(mockReport);

      expect(result).toHaveProperty('jsonPath');
      expect(result).toHaveProperty('htmlPath');
      expect(result).toHaveProperty('summary');
      expect(result.jsonPath).toContain('verification-report.json');
      expect(result.htmlPath).toContain('verification-report.html');
    });

    it('should create output directory if it does not exist', async () => {
      await reportGenerator.generateReport(mockReport);

      expect(mockFs.mkdir).toHaveBeenCalledWith(
        options.outputDir,
        { recursive: true }
      );
    });

    it('should write JSON report to file', async () => {
      await reportGenerator.generateReport(mockReport);

      expect(mockFs.writeFile).toHaveBeenCalledWith(
        expect.stringContaining('.json'),
        expect.stringContaining('"reportType": "production-deployment-verification"')
      );
    });

    it('should write HTML report to file', async () => {
      await reportGenerator.generateReport(mockReport);

      expect(mockFs.writeFile).toHaveBeenCalledWith(
        expect.stringContaining('.html'),
        expect.stringContaining('<!DOCTYPE html>')
      );
    });

    it('should include timestamp in filename when option is enabled', async () => {
      const timestampOptions = { ...options, includeTimestamp: true };
      const timestampGenerator = new ReportGenerator(timestampOptions);

      const result = await timestampGenerator.generateReport(mockReport);

      expect(result.jsonPath).toMatch(/verification-report-\d{4}-\d{2}-\d{2}T\d{2}-\d{2}-\d{2}/);
      expect(result.htmlPath).toMatch(/verification-report-\d{4}-\d{2}-\d{2}T\d{2}-\d{2}-\d{2}/);
    });
  });

  describe('generateDeploymentDecision', () => {
    it('should mark as ready when all checks pass', () => {
      const passingReport = {
        ...mockReport,
        overallStatus: 'passed' as const,
        buildVerification: {
          ...mockReport.buildVerification,
          success: true,
          errors: [],
          warnings: []
        },
        testResults: [
          {
            passed: true,
            duration: 5000,
            testCount: 25,
            failures: []
          }
        ],
        accessibilityResults: {
          ...mockReport.accessibilityResults,
          compliant: true,
          violations: []
        }
      };

      const decision = reportGenerator.generateDeploymentDecision(passingReport);

      expect(decision.ready).toBe(true);
      expect(decision.confidence).toBeGreaterThan(70);
      expect(decision.blockers).toHaveLength(0);
    });

    it('should mark as not ready when build fails', () => {
      const failingReport = {
        ...mockReport,
        buildVerification: {
          ...mockReport.buildVerification,
          success: false,
          errors: [
            {
              file: 'src/App.tsx',
              line: 10,
              column: 5,
              message: 'Type error',
              type: 'typescript' as const
            }
          ]
        }
      };

      const decision = reportGenerator.generateDeploymentDecision(failingReport);

      expect(decision.ready).toBe(false);
      expect(decision.blockers).toContain('Build verification failed');
      expect(decision.confidence).toBeLessThan(100);
    });

    it('should mark as not ready when tests fail', () => {
      const failingTestReport = {
        ...mockReport,
        testResults: [
          {
            passed: false,
            duration: 5000,
            testCount: 25,
            failures: [
              {
                testName: 'critical test',
                error: 'Test failed',
                duration: 100
              }
            ]
          }
        ]
      };

      const decision = reportGenerator.generateDeploymentDecision(failingTestReport);

      expect(decision.ready).toBe(false);
      expect(decision.blockers).toContain('1 test suites failed');
    });

    it('should add warnings for performance issues', () => {
      const slowReport = {
        ...mockReport,
        performanceMetrics: {
          ...mockReport.performanceMetrics,
          lcp: 3000, // Above threshold
          lighthouse: {
            ...mockReport.performanceMetrics.lighthouse,
            performance: 70 // Below threshold
          }
        }
      };

      const decision = reportGenerator.generateDeploymentDecision(slowReport);

      expect(decision.warnings).toContain(
        expect.stringContaining('LCP (3000ms) exceeds threshold')
      );
      expect(decision.warnings).toContain(
        expect.stringContaining('Lighthouse performance score (70) below threshold')
      );
    });

    it('should add blockers for critical accessibility violations', () => {
      const a11yReport = {
        ...mockReport,
        accessibilityResults: {
          ...mockReport.accessibilityResults,
          violations: [
            {
              rule: 'color-contrast',
              impact: 'critical' as const,
              element: 'button',
              description: 'Critical accessibility issue'
            }
          ]
        }
      };

      const decision = reportGenerator.generateDeploymentDecision(a11yReport);

      expect(decision.blockers).toContain(
        expect.stringContaining('critical accessibility violations found')
      );
    });

    it('should generate appropriate recommendations', () => {
      const decision = reportGenerator.generateDeploymentDecision(mockReport);

      expect(decision.recommendations).toContain(
        expect.stringContaining('Fix critical issues before deployment')
      );
    });
  });

  describe('generateSummary', () => {
    it('should generate accurate summary statistics', () => {
      const summary = reportGenerator['generateSummary'](mockReport);

      expect(summary).toHaveProperty('overallStatus', 'passed');
      expect(summary).toHaveProperty('deploymentReady');
      expect(summary).toHaveProperty('totalChecks');
      expect(summary).toHaveProperty('passedChecks');
      expect(summary).toHaveProperty('failedChecks');
      expect(summary).toHaveProperty('warningChecks');
      expect(summary.totalChecks).toBeGreaterThan(0);
    });

    it('should count checks correctly', () => {
      const summary = reportGenerator['generateSummary'](mockReport);

      // Should count build, tests, performance, accessibility, PWA, and dependencies
      expect(summary.totalChecks).toBeGreaterThan(10);
      expect(summary.passedChecks + summary.failedChecks + summary.warningChecks).toBe(summary.totalChecks);
    });

    it('should identify critical issues', () => {
      const summary = reportGenerator['generateSummary'](mockReport);

      expect(summary.criticalIssues).toBeInstanceOf(Array);
      expect(summary.recommendations).toBeInstanceOf(Array);
      expect(summary.recommendations.length).toBeGreaterThan(0);
    });
  });

  describe('HTML generation', () => {
    it('should generate valid HTML structure', async () => {
      const result = await reportGenerator.generateReport(mockReport);

      // Check that writeFile was called with HTML content
      const htmlCall = mockFs.writeFile.mock.calls.find(call => 
        call[0].toString().includes('.html')
      );
      
      expect(htmlCall).toBeDefined();
      const htmlContent = htmlCall![1] as string;
      
      expect(htmlContent).toContain('<!DOCTYPE html>');
      expect(htmlContent).toContain('<html lang="en">');
      expect(htmlContent).toContain('<head>');
      expect(htmlContent).toContain('<body>');
      expect(htmlContent).toContain('Production Deployment Verification Report');
    });

    it('should include CSS styles', async () => {
      await reportGenerator.generateReport(mockReport);

      const htmlCall = mockFs.writeFile.mock.calls.find(call => 
        call[0].toString().includes('.html')
      );
      
      const htmlContent = htmlCall![1] as string;
      expect(htmlContent).toContain('<style>');
      expect(htmlContent).toContain('font-family:');
      expect(htmlContent).toContain('.container');
    });

    it('should include JavaScript for interactivity', async () => {
      await reportGenerator.generateReport(mockReport);

      const htmlCall = mockFs.writeFile.mock.calls.find(call => 
        call[0].toString().includes('.html')
      );
      
      const htmlContent = htmlCall![1] as string;
      expect(htmlContent).toContain('<script>');
      expect(htmlContent).toContain('function toggleSection');
    });

    it('should display deployment status correctly', async () => {
      await reportGenerator.generateReport(mockReport);

      const htmlCall = mockFs.writeFile.mock.calls.find(call => 
        call[0].toString().includes('.html')
      );
      
      const htmlContent = htmlCall![1] as string;
      expect(htmlContent).toContain('deployment-status');
      expect(htmlContent).toContain('Confidence:');
    });

    it('should show section-specific metrics', async () => {
      await reportGenerator.generateReport(mockReport);

      const htmlCall = mockFs.writeFile.mock.calls.find(call => 
        call[0].toString().includes('.html')
      );
      
      const htmlContent = htmlCall![1] as string;
      
      // Check for build metrics
      expect(htmlContent).toContain('Build Time');
      expect(htmlContent).toContain('15000ms');
      
      // Check for performance metrics
      expect(htmlContent).toContain('LCP');
      expect(htmlContent).toContain('2200ms');
      
      // Check for accessibility metrics
      expect(htmlContent).toContain('WCAG Compliant');
    });
  });

  describe('JSON generation', () => {
    it('should generate valid JSON structure', async () => {
      await reportGenerator.generateReport(mockReport);

      const jsonCall = mockFs.writeFile.mock.calls.find(call => 
        call[0].toString().includes('.json')
      );
      
      expect(jsonCall).toBeDefined();
      const jsonContent = jsonCall![1] as string;
      
      // Should be valid JSON
      expect(() => JSON.parse(jsonContent)).not.toThrow();
      
      const parsed = JSON.parse(jsonContent);
      expect(parsed).toHaveProperty('metadata');
      expect(parsed).toHaveProperty('summary');
      expect(parsed).toHaveProperty('deploymentDecision');
      expect(parsed).toHaveProperty('originalReport');
      expect(parsed).toHaveProperty('detailedSections');
    });

    it('should include all required metadata', async () => {
      await reportGenerator.generateReport(mockReport);

      const jsonCall = mockFs.writeFile.mock.calls.find(call => 
        call[0].toString().includes('.json')
      );
      
      const jsonContent = jsonCall![1] as string;
      const parsed = JSON.parse(jsonContent);
      
      expect(parsed.metadata).toHaveProperty('generatedAt');
      expect(parsed.metadata).toHaveProperty('version');
      expect(parsed.metadata).toHaveProperty('reportType', 'production-deployment-verification');
    });

    it('should preserve original report data', async () => {
      await reportGenerator.generateReport(mockReport);

      const jsonCall = mockFs.writeFile.mock.calls.find(call => 
        call[0].toString().includes('.json')
      );
      
      const jsonContent = jsonCall![1] as string;
      const parsed = JSON.parse(jsonContent);
      
      expect(parsed.originalReport.timestamp).toBe(mockReport.timestamp.toISOString());
      expect(parsed.originalReport.overallStatus).toBe(mockReport.overallStatus);
      expect(parsed.originalReport.buildVerification.success).toBe(mockReport.buildVerification.success);
    });
  });
});

describe('Factory functions', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockFs.mkdir.mockResolvedValue(undefined);
    mockFs.writeFile.mockResolvedValue(undefined);
  });

  describe('createReportGenerator', () => {
    it('should create ReportGenerator instance', () => {
      const options: ReportGeneratorOptions = {
        outputDir: './test-reports'
      };

      const generator = createReportGenerator(options);
      expect(generator).toBeInstanceOf(ReportGenerator);
    });
  });

  describe('generateVerificationReport', () => {
    it('should generate report with default options', async () => {
      const mockReport: VerificationReport = {
        timestamp: new Date(),
        overallStatus: 'passed',
        buildVerification: {
          success: true,
          buildTime: 5000,
          errors: [],
          warnings: [],
          outputSize: { total: 1024000, chunks: [] }
        },
        testResults: [],
        performanceMetrics: {
          lcp: 2000,
          fid: 50,
          cls: 0.05,
          fcp: 1500,
          lighthouse: { performance: 95, accessibility: 98, bestPractices: 90, seo: 95 }
        },
        accessibilityResults: {
          compliant: true,
          violations: [],
          warnings: [],
          testedPages: ['/']
        },
        pwaValidation: {
          serviceWorkerRegistered: true,
          manifestValid: true,
          offlineFunctionality: true,
          installable: true,
          cacheStrategy: { staticAssetsCache: true, apiResponseCache: true, offlinePages: [] }
        },
        dependencyStatus: {
          googleMaps: { service: 'Google Maps', available: true, responseTime: 100 },
          cdnResources: [],
          apiEndpoints: []
        },
        recommendations: [],
        deploymentReady: true
      };

      const result = await generateVerificationReport(mockReport);

      expect(result).toHaveProperty('jsonPath');
      expect(result).toHaveProperty('htmlPath');
      expect(result).toHaveProperty('summary');
      expect(result.jsonPath).toContain('./verification-reports');
    });

    it('should use custom output directory', async () => {
      const mockReport: VerificationReport = {
        timestamp: new Date(),
        overallStatus: 'passed',
        buildVerification: {
          success: true,
          buildTime: 5000,
          errors: [],
          warnings: [],
          outputSize: { total: 1024000, chunks: [] }
        },
        testResults: [],
        performanceMetrics: {
          lcp: 2000,
          fid: 50,
          cls: 0.05,
          fcp: 1500,
          lighthouse: { performance: 95, accessibility: 98, bestPractices: 90, seo: 95 }
        },
        accessibilityResults: {
          compliant: true,
          violations: [],
          warnings: [],
          testedPages: ['/']
        },
        pwaValidation: {
          serviceWorkerRegistered: true,
          manifestValid: true,
          offlineFunctionality: true,
          installable: true,
          cacheStrategy: { staticAssetsCache: true, apiResponseCache: true, offlinePages: [] }
        },
        dependencyStatus: {
          googleMaps: { service: 'Google Maps', available: true, responseTime: 100 },
          cdnResources: [],
          apiEndpoints: []
        },
        recommendations: [],
        deploymentReady: true
      };

      const customDir = './custom-reports';
      const result = await generateVerificationReport(mockReport, customDir);

      expect(mockFs.mkdir).toHaveBeenCalledWith(customDir, { recursive: true });
      expect(result.jsonPath).toContain(customDir);
      expect(result.htmlPath).toContain(customDir);
    });
  });
});