/**
 * Unit tests for External Dependencies Checker
 */

import {
  HttpClient,
  GoogleMapsApiTester,
  CdnResourceChecker,
  ExternalDependencyChecker,
  createExternalDependencyChecker,
  testSingleUrl,
} from '../external-dependencies';
import { ExternalDependency } from '../types';

// Mock fetch globally
global.fetch = jest.fn();
const mockFetch = global.fetch as jest.MockedFunction<typeof fetch>;

// Mock setTimeout and clearTimeout
global.setTimeout = jest.fn().mockImplementation((callback) => {
  if (typeof callback === 'function') {
    callback();
  }
  return 1 as any;
}) as any;
global.clearTimeout = jest.fn();

describe('HttpClient', () => {
  let httpClient: HttpClient;

  beforeEach(() => {
    httpClient = new HttpClient();
    mockFetch.mockClear();
    jest.clearAllMocks();
  });

  describe('testUrl', () => {
    it('should return successful status for available URL', async () => {
      const mockResponse = {
        ok: true,
        status: 200,
        statusText: 'OK',
      };
      mockFetch.mockResolvedValueOnce(mockResponse as Response);

      const result = await httpClient.testUrl('https://example.com');

      expect(result.service).toBe('https://example.com');
      expect(result.available).toBe(true);
      expect(result.responseTime).toBeGreaterThan(0);
      expect(result.error).toBeUndefined();
    });

    it('should return failed status for unavailable URL', async () => {
      const mockResponse = {
        ok: false,
        status: 404,
        statusText: 'Not Found',
      };
      mockFetch.mockResolvedValueOnce(mockResponse as Response);

      const result = await httpClient.testUrl('https://example.com/notfound');

      expect(result.service).toBe('https://example.com/notfound');
      expect(result.available).toBe(false);
      expect(result.responseTime).toBeGreaterThan(0);
      expect(result.error).toBe('HTTP 404: Not Found');
    });

    it('should handle network errors', async () => {
      // Mock all retry attempts to fail
      mockFetch
        .mockRejectedValueOnce(new Error('Network error'))
        .mockRejectedValueOnce(new Error('Network error'))
        .mockRejectedValueOnce(new Error('Network error'))
        .mockRejectedValueOnce(new Error('Network error'));

      const result = await httpClient.testUrl('https://example.com');

      expect(result.service).toBe('https://example.com');
      expect(result.available).toBe(false);
      expect(result.error).toBe('Network error');
      expect(result.responseTime).toBeGreaterThan(0);
    });

    it('should handle timeout errors', async () => {
      // Mock all retry attempts to fail with timeout
      mockFetch
        .mockRejectedValueOnce(new Error('The operation was aborted'))
        .mockRejectedValueOnce(new Error('The operation was aborted'))
        .mockRejectedValueOnce(new Error('The operation was aborted'))
        .mockRejectedValueOnce(new Error('The operation was aborted'));

      const result = await httpClient.testUrl('https://example.com', 1000);

      expect(result.service).toBe('https://example.com');
      expect(result.available).toBe(false);
      expect(result.error).toBe('The operation was aborted');
      expect(result.responseTime).toBeGreaterThan(0);
    });

    it('should use custom timeout', async () => {
      const mockResponse = {
        ok: true,
        status: 200,
        statusText: 'OK',
      };
      mockFetch.mockResolvedValueOnce(mockResponse as Response);

      await httpClient.testUrl('https://example.com', 5000);

      expect(mockFetch).toHaveBeenCalledWith('https://example.com', {
        signal: expect.any(AbortSignal),
        headers: {
          'User-Agent': 'IceBoxHockey-VerificationBot/1.0',
        },
        mode: 'no-cors',
      });
    });
  });

  describe('retry logic', () => {
    it('should retry on failure and eventually succeed', async () => {
      const httpClientWithRetries = new HttpClient({ retries: 2, retryDelay: 10 });
      
      mockFetch
        .mockRejectedValueOnce(new Error('Network error'))
        .mockRejectedValueOnce(new Error('Network error'))
        .mockResolvedValueOnce({
          ok: true,
          status: 200,
          statusText: 'OK',
        } as Response);

      const result = await httpClientWithRetries.testUrl('https://example.com');

      expect(result.available).toBe(true);
      expect(mockFetch).toHaveBeenCalledTimes(3);
    });

    it('should fail after all retries are exhausted', async () => {
      const httpClientWithRetries = new HttpClient({ retries: 2, retryDelay: 10 });
      
      mockFetch.mockRejectedValue(new Error('Persistent network error'));

      const result = await httpClientWithRetries.testUrl('https://example.com');

      expect(result.available).toBe(false);
      expect(result.error).toBe('Persistent network error');
      expect(mockFetch).toHaveBeenCalledTimes(3); // Initial + 2 retries
    });
  });
});

describe('GoogleMapsApiTester', () => {
  let httpClient: HttpClient;
  let googleMapsApiTester: GoogleMapsApiTester;

  beforeEach(() => {
    httpClient = new HttpClient();
    googleMapsApiTester = new GoogleMapsApiTester(httpClient);
    mockFetch.mockClear();
  });

  describe('testGoogleMapsApi', () => {
    it('should test Google Maps API without API key', async () => {
      const mockResponse = {
        ok: true,
        status: 200,
        statusText: 'OK',
      };
      mockFetch.mockResolvedValueOnce(mockResponse as Response);

      const result = await googleMapsApiTester.testGoogleMapsApi();

      expect(result.service).toBe('Google Maps API');
      expect(result.available).toBe(true);
      expect(mockFetch).toHaveBeenCalledWith(
        'https://maps.googleapis.com/maps/api/js',
        expect.any(Object)
      );
    });

    it('should test Google Maps API with API key', async () => {
      const googleMapsApiTesterWithKey = new GoogleMapsApiTester(httpClient, 'test-api-key');
      
      const mockResponse = {
        ok: true,
        status: 200,
        statusText: 'OK',
      };
      mockFetch
        .mockResolvedValueOnce(mockResponse as Response) // Base URL test
        .mockResolvedValueOnce(mockResponse as Response); // API key test

      const result = await googleMapsApiTesterWithKey.testGoogleMapsApi();

      expect(result.service).toBe('Google Maps API');
      expect(result.available).toBe(true);
      expect(mockFetch).toHaveBeenCalledWith(
        'https://maps.googleapis.com/maps/api/js?key=test-api-key',
        expect.any(Object)
      );
    });

    it('should handle Google Maps API failure', async () => {
      // Mock all retry attempts to fail
      mockFetch
        .mockRejectedValueOnce(new Error('API unavailable'))
        .mockRejectedValueOnce(new Error('API unavailable'))
        .mockRejectedValueOnce(new Error('API unavailable'))
        .mockRejectedValueOnce(new Error('API unavailable'));

      const result = await googleMapsApiTester.testGoogleMapsApi();

      expect(result.service).toBe('Google Maps API');
      expect(result.available).toBe(false);
      expect(result.error).toBe('Base API unavailable: API unavailable');
    });
  });

  describe('testGoogleMapsServices', () => {
    it('should test multiple Google Maps services', async () => {
      const mockResponse = {
        ok: true,
        status: 200,
        statusText: 'OK',
      };
      mockFetch.mockResolvedValue(mockResponse as Response);

      const results = await googleMapsApiTester.testGoogleMapsServices();

      expect(results).toHaveLength(3);
      expect(results.every(result => result.available)).toBe(true);
      expect(mockFetch).toHaveBeenCalledTimes(3);
    });
  });
});

describe('CdnResourceChecker', () => {
  let httpClient: HttpClient;
  let cdnResourceChecker: CdnResourceChecker;

  beforeEach(() => {
    httpClient = new HttpClient();
    cdnResourceChecker = new CdnResourceChecker(httpClient);
    mockFetch.mockClear();
  });

  describe('testCdnResources', () => {
    it('should test multiple CDN resources', async () => {
      const mockResponse = {
        ok: true,
        status: 200,
        statusText: 'OK',
      };
      mockFetch.mockResolvedValue(mockResponse as Response);

      const resources = [
        'https://fonts.googleapis.com/css2?family=Inter',
        'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css',
      ];

      const results = await cdnResourceChecker.testCdnResources(resources);

      expect(results).toHaveLength(2);
      expect(results.every(result => result.available)).toBe(true);
      expect(mockFetch).toHaveBeenCalledTimes(2);
    });

    it('should handle CDN resource failures', async () => {
      // Mock the HttpClient.testUrl method directly to control the behavior
      const httpClient = new HttpClient();
      const cdnResourceChecker = new CdnResourceChecker(httpClient);
      
      jest.spyOn(httpClient, 'testUrl')
        .mockResolvedValueOnce({
          service: 'https://fonts.googleapis.com/css2?family=Inter',
          available: true,
          responseTime: 50,
        })
        .mockResolvedValueOnce({
          service: 'https://unavailable-cdn.com/resource.css',
          available: false,
          responseTime: 1,
          error: 'CDN unavailable',
        });

      const resources = [
        'https://fonts.googleapis.com/css2?family=Inter',
        'https://unavailable-cdn.com/resource.css',
      ];

      const results = await cdnResourceChecker.testCdnResources(resources);

      expect(results).toHaveLength(2);
      expect(results[0].available).toBe(true);
      expect(results[1].available).toBe(false);
      expect(results[1].error).toBe('CDN unavailable');
    });
  });

  describe('testCommonCdnResources', () => {
    it('should test common CDN resources', async () => {
      const mockResponse = {
        ok: true,
        status: 200,
        statusText: 'OK',
      };
      mockFetch.mockResolvedValue(mockResponse as Response);

      const results = await cdnResourceChecker.testCommonCdnResources();

      expect(results).toHaveLength(3);
      expect(results.every(result => result.available)).toBe(true);
      expect(mockFetch).toHaveBeenCalledTimes(3);
    });
  });
});

describe('ExternalDependencyChecker', () => {
  let externalDependencyChecker: ExternalDependencyChecker;

  beforeEach(() => {
    externalDependencyChecker = new ExternalDependencyChecker();
    mockFetch.mockClear();
  });

  describe('checkAllDependencies', () => {
    it('should check all types of dependencies', async () => {
      const mockResponse = {
        ok: true,
        status: 200,
        statusText: 'OK',
      };
      mockFetch.mockResolvedValue(mockResponse as Response);

      const dependencies: ExternalDependency[] = [
        {
          name: 'Google Maps API',
          url: 'https://maps.googleapis.com/maps/api/js',
          timeout: 10000,
          critical: true,
        },
        {
          name: 'CDN Resource',
          url: 'https://fonts.googleapis.com/css2?family=Inter',
          timeout: 5000,
          critical: false,
        },
        {
          name: 'API Endpoint',
          url: 'https://api.example.com/health',
          timeout: 5000,
          critical: true,
        },
      ];

      const result = await externalDependencyChecker.checkAllDependencies(dependencies);

      expect(result.googleMaps?.available).toBe(true);
      expect(result.cdnResources).toHaveLength(1);
      expect(result.apiEndpoints).toHaveLength(1);
      expect(result.cdnResources?.[0]?.available).toBe(true);
      expect(result.apiEndpoints?.[0]?.available).toBe(true);
    });

    it('should handle errors gracefully', async () => {
      mockFetch.mockRejectedValue(new Error('Network error'));

      const dependencies: ExternalDependency[] = [
        {
          name: 'Test API',
          url: 'https://api.example.com/test',
          timeout: 5000,
          critical: true,
        },
      ];

      const result = await externalDependencyChecker.checkAllDependencies(dependencies);

      expect(result.googleMaps?.available).toBe(false);
      expect(result.apiEndpoints).toHaveLength(1);
      expect(result.apiEndpoints?.[0]?.available).toBe(false);
    });
  });

  describe('checkCriticalDependencies', () => {
    it('should only check critical dependencies', async () => {
      const mockResponse = {
        ok: true,
        status: 200,
        statusText: 'OK',
      };
      mockFetch.mockResolvedValue(mockResponse as Response);

      const dependencies: ExternalDependency[] = [
        {
          name: 'Critical API',
          url: 'https://api.example.com/critical',
          timeout: 5000,
          critical: true,
        },
        {
          name: 'Non-Critical API',
          url: 'https://api.example.com/optional',
          timeout: 5000,
          critical: false,
        },
      ];

      const result = await externalDependencyChecker.checkCriticalDependencies(dependencies);

      expect(result.apiEndpoints).toHaveLength(1);
      expect(result.apiEndpoints?.[0]?.service).toBe('https://api.example.com/critical');
    });
  });

  describe('validateDependencyConfig', () => {
    it('should validate correct dependency configuration', () => {
      const dependencies: ExternalDependency[] = [
        {
          name: 'Test API',
          url: 'https://api.example.com/test',
          timeout: 5000,
          critical: true,
        },
      ];

      const result = externalDependencyChecker.validateDependencyConfig(dependencies);

      expect(result.valid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should detect invalid dependency configuration', () => {
      const dependencies: ExternalDependency[] = [
        {
          name: '',
          url: 'invalid-url',
          timeout: -1000,
          critical: true,
        },
      ];

      const result = externalDependencyChecker.validateDependencyConfig(dependencies);

      expect(result.valid).toBe(false);
      expect(result.errors).toContain('Dependency 0 must have a name');
      expect(result.errors).toContain('Dependency  has invalid URL: invalid-url');
      expect(result.errors).toContain('Dependency  timeout must be positive');
    });
  });

  describe('getDependencySummary', () => {
    it('should provide correct dependency summary', () => {
      const mockResult = {
        googleMaps: {
          service: 'Google Maps API',
          available: true,
          responseTime: 100,
        },
        cdnResources: [
          {
            service: 'CDN Resource 1',
            available: true,
            responseTime: 50,
          },
          {
            service: 'CDN Resource 2',
            available: false,
            responseTime: 0,
            error: 'Timeout',
          },
        ],
        apiEndpoints: [
          {
            service: 'API Endpoint',
            available: true,
            responseTime: 200,
          },
        ],
      };

      const summary = externalDependencyChecker.getDependencySummary(mockResult);

      expect(summary.total).toBe(4);
      expect(summary.available).toBe(3);
      expect(summary.unavailable).toBe(1);
      expect(summary.criticalFailures).toHaveLength(1);
      expect(summary.criticalFailures[0]).toBe('CDN Resource 2: Timeout');
    });
  });
});

describe('Factory functions', () => {
  beforeEach(() => {
    mockFetch.mockClear();
    jest.clearAllMocks();
  });

  describe('createExternalDependencyChecker', () => {
    it('should create ExternalDependencyChecker with default dependencies', () => {
      const checker = createExternalDependencyChecker();
      expect(checker).toBeInstanceOf(ExternalDependencyChecker);
    });

    it('should create ExternalDependencyChecker with custom dependencies', () => {
      const customDependencies: ExternalDependency[] = [
        {
          name: 'Custom API',
          url: 'https://custom.api.com',
          timeout: 3000,
          critical: true,
        },
      ];

      const checker = createExternalDependencyChecker();
       expect(checker).toBeInstanceOf(ExternalDependencyChecker);
    });
  });

  describe('testSingleUrl', () => {
    it('should test single URL with default timeout', async () => {
      const mockResponse = {
        ok: true,
        status: 200,
        statusText: 'OK',
      };
      mockFetch.mockResolvedValueOnce(mockResponse as Response);

      const result = await testSingleUrl('https://example.com');

      expect(result.service).toBe('https://example.com');
      expect(result.available).toBe(true);
      expect(mockFetch).toHaveBeenCalledWith('https://example.com', {
        signal: expect.any(AbortSignal),
        headers: {
          'User-Agent': 'IceBoxHockey-VerificationBot/1.0',
        },
        mode: 'no-cors',
      });
    });

    it('should test single URL with custom timeout', async () => {
      const mockResponse = {
        ok: true,
        status: 200,
        statusText: 'OK',
      };
      mockFetch.mockResolvedValueOnce(mockResponse as Response);

      await testSingleUrl('https://example.com', 5000);

      expect(mockFetch).toHaveBeenCalledWith('https://example.com', {
        signal: expect.any(AbortSignal),
        headers: {
          'User-Agent': 'IceBoxHockey-VerificationBot/1.0',
        },
        mode: 'no-cors',
      });
    });
  });
});

// Mock scenarios and edge cases
describe('ExternalDependencyChecker Mock Scenarios', () => {
  let checker: ExternalDependencyChecker;
  let mockDependencies: ExternalDependency[];

  beforeEach(() => {
    jest.clearAllMocks();
    
    mockDependencies = [
      {
        name: 'Google Maps API',
        url: 'https://maps.googleapis.com/maps/api/js?key=test',
        timeout: 5000,
        critical: true,
      },
      {
        name: 'Google Fonts',
        url: 'https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap',
        timeout: 3000,
        critical: false,
      },
      {
        name: 'CDN Assets',
        url: 'https://cdn.example.com/assets/bundle.js',
        timeout: 4000,
        critical: true,
      },
      {
        name: 'Analytics API',
        url: 'https://analytics.example.com/api/track',
        timeout: 2000,
        critical: false,
      },
    ];

    checker = createExternalDependencyChecker();
  });

  describe('timeout scenarios', () => {
    it('should handle timeout for slow dependencies', async () => {
      // Mock a slow response that exceeds timeout
      mockFetch.mockImplementationOnce(() => 
        new Promise(resolve => setTimeout(() => resolve({
          ok: true,
          status: 200,
          statusText: 'OK',
          headers: new Headers(),
        } as Response), 6000))
      );

      const result = await checker.checkAllDependencies(mockDependencies);

       expect(result.googleMaps?.available).toBe(false);
    });

    it('should handle network errors gracefully', async () => {
      mockFetch.mockRejectedValueOnce(new Error('Network error'));

      const result = await checker.checkAllDependencies(mockDependencies);

       expect(result.googleMaps?.available).toBe(false);
    });
  });

  describe('performance and optimization', () => {
    it('should handle large numbers of dependencies efficiently', async () => {
      const manyDependencies: ExternalDependency[] = Array.from({ length: 50 }, (_, i) => ({
        name: `Dependency ${i}`,
        url: `https://api${i}.example.com`,
        timeout: 5000,
        critical: i < 10,
      }));

      const largeDependencyChecker = createExternalDependencyChecker();

      mockFetch.mockResolvedValue({
        ok: true,
        status: 200,
        statusText: 'OK',
        headers: new Headers(),
      } as Response);

      const startTime = Date.now();
      const results = await largeDependencyChecker.checkAllDependencies(manyDependencies);
      const endTime = Date.now();

      expect((results.cdnResources?.length || 0) + (results.apiEndpoints?.length || 0)).toBe(50);
      expect(endTime - startTime).toBeLessThan(10000);
      expect(mockFetch).toHaveBeenCalledTimes(50);
    });
  });
});