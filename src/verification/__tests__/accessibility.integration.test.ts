/**
 * Integration tests for the accessibility validation system
 * Tests all implemented features including axe-core integration,
 * keyboard navigation testing, color contrast validation, and screen reader compatibility
 */

import { AccessibilityValidator, AccessibilityTestSuite } from '../accessibility';
import { ConfigManager } from '../config';

describe('Accessibility Validation System Integration', () => {
  describe('AccessibilityValidator', () => {
    test('should instantiate with correct default configuration', () => {
      const validator = new AccessibilityValidator();
      
      expect(validator).toBeDefined();
      expect((validator as any).config.wcagLevel).toBe('AA');
      expect((validator as any).config.keyboardNavigation).toBe(true);
      expect((validator as any).config.colorContrast).toBe(true);
      expect((validator as any).config.screenReader).toBe(true);
    });

    test('should accept custom configuration', () => {
      const customConfig = {
        baseUrl: 'http://localhost:3000',
        pages: ['/custom'],
        wcagLevel: 'AAA' as const,
        timeout: 60000,
        keyboardNavigation: false,
        colorContrast: false,
        screenReader: false,
      };

      const validator = new AccessibilityValidator(customConfig);
      const config = (validator as any).config;

      expect(config.baseUrl).toBe('http://localhost:3000');
      expect(config.pages).toEqual(['/custom']);
      expect(config.wcagLevel).toBe('AAA');
      expect(config.timeout).toBe(60000);
      expect(config.keyboardNavigation).toBe(false);
      expect(config.colorContrast).toBe(false);
      expect(config.screenReader).toBe(false);
    });

    test('should support all WCAG levels', () => {
      const levels: Array<'A' | 'AA' | 'AAA'> = ['A', 'AA', 'AAA'];
      
      levels.forEach(level => {
        const validator = new AccessibilityValidator({ wcagLevel: level });
        expect((validator as any).config.wcagLevel).toBe(level);
      });
    });

    test('should generate axe tags correctly for different WCAG levels', () => {
      const validatorA = new AccessibilityValidator({ wcagLevel: 'A' });
      const validatorAA = new AccessibilityValidator({ wcagLevel: 'AA' });
      const validatorAAA = new AccessibilityValidator({ wcagLevel: 'AAA' });

      const tagsA = (validatorA as any).getAxeTags();
      const tagsAA = (validatorAA as any).getAxeTags();
      const tagsAAA = (validatorAAA as any).getAxeTags();

      expect(tagsA).toEqual(['wcag2a']);
      expect(tagsAA).toEqual(['wcag2a', 'wcag2aa']);
      expect(tagsAAA).toEqual(['wcag2a', 'wcag2aa', 'wcag2aaa']);
    });

    test('should calculate color contrast ratios correctly', () => {
      const validator = new AccessibilityValidator();
      
      // Test black on white (should be 21:1)
      const blackOnWhite = (validator as any).calculateContrastRatio('rgb(0, 0, 0)', 'rgb(255, 255, 255)');
      expect(blackOnWhite).toBeCloseTo(21, 0);

      // Test hex colors
      const hexContrast = (validator as any).calculateContrastRatio('#000000', '#ffffff');
      expect(hexContrast).toBeCloseTo(21, 0);

      // Test named colors
      const namedContrast = (validator as any).calculateContrastRatio('black', 'white');
      expect(namedContrast).toBeCloseTo(21, 0);

      // Test same colors (should be 1:1)
      const sameColor = (validator as any).calculateContrastRatio('rgb(128, 128, 128)', 'rgb(128, 128, 128)');
      expect(sameColor).toBeCloseTo(1, 0);
    });

    test('should handle invalid color values gracefully', () => {
      const validator = new AccessibilityValidator();
      
      // Test invalid color strings
      const invalidContrast = (validator as any).calculateContrastRatio('invalid-color', 'another-invalid');
      expect(typeof invalidContrast).toBe('number');
      expect(invalidContrast).toBeGreaterThan(0);
    });

    test('should provide comprehensive remediation guidance', () => {
      const validator = new AccessibilityValidator();
      
      const testRules = [
        'color-contrast',
        'image-alt',
        'label',
        'button-name',
        'keyboard-navigation',
        'screen-reader-compatibility',
        'heading-order',
        'page-has-heading-one',
        'aria-valid-attr',
        'html-has-lang',
      ];

      testRules.forEach(rule => {
        const guidance = (validator as any).getRemediationGuidance(rule);
        expect(guidance).toBeDefined();
        expect(typeof guidance).toBe('string');
        expect(guidance.length).toBeGreaterThan(20);
        expect(guidance).not.toContain('Review WCAG 2.1 guidelines for this rule');
      });
    });

    test('should provide fallback guidance for unknown rules', () => {
      const validator = new AccessibilityValidator();
      const guidance = (validator as any).getRemediationGuidance('unknown-rule');
      
      expect(guidance).toContain('Review WCAG 2.1 guidelines');
      expect(guidance).toContain('https://www.w3.org/WAI/WCAG21/quickref/');
    });

    test('should generate comprehensive accessibility reports', () => {
      const validator = new AccessibilityValidator();
      
      const mockResult = {
        compliant: false,
        violations: [
          {
            rule: 'color-contrast',
            impact: 'serious' as const,
            element: 'div.text',
            description: 'Insufficient color contrast ratio',
          },
          {
            rule: 'image-alt',
            impact: 'critical' as const,
            element: 'img.hero',
            description: 'Image missing alt text',
          },
        ],
        warnings: [
          {
            rule: 'manual-check',
            element: 'button',
            description: 'Manual review needed for complex interaction',
          },
        ],
        testedPages: ['/', '/team-sales'],
      };

      const report = validator.generateReport(mockResult);
      
      expect(report).toContain('Accessibility Validation Report');
      expect(report).toContain('Overall Status: ❌ FAILED');
      expect(report).toContain('Pages Tested: /, /team-sales');
      expect(report).toContain('Violations (Must Fix)');
      expect(report).toContain('Warnings (Should Fix)');
      expect(report).toContain('color-contrast (serious)');
      expect(report).toContain('image-alt (critical)');
      expect(report).toContain('Remediation:');
    });

    test('should generate success report when compliant', () => {
      const validator = new AccessibilityValidator();
      
      const mockResult = {
        compliant: true,
        violations: [],
        warnings: [],
        testedPages: ['/'],
      };

      const report = validator.generateReport(mockResult);
      
      expect(report).toContain('Overall Status: ✅ PASSED');
      expect(report).toContain('All Tests Passed');
      expect(report).toContain('No accessibility violations or warnings found');
    });
  });

  describe('AccessibilityTestSuite', () => {
    test('should integrate with ConfigManager correctly', () => {
      const configManager = new ConfigManager({
        accessibilityLevel: 'AA',
        testSuites: [{
          name: 'accessibility-tests',
          type: 'accessibility',
          enabled: true,
          timeout: 60000,
          retries: 2,
        }],
      });

      const testSuite = new AccessibilityTestSuite(configManager);
      expect(testSuite).toBeDefined();
    });

    test('should handle different WCAG levels from config', () => {
      const configAAA = new ConfigManager({
        accessibilityLevel: 'AAA',
        testSuites: [{
          name: 'accessibility-tests',
          type: 'accessibility',
          enabled: true,
          timeout: 30000,
          retries: 1,
        }],
      });

      const testSuite = new AccessibilityTestSuite(configAAA);
      expect(testSuite).toBeDefined();
      
      // Verify the validator is configured with AAA level
      const validator = (testSuite as any).validator;
      expect(validator.config.wcagLevel).toBe('AAA');
    });

    test('should use timeout from test suite configuration', () => {
      const configManager = new ConfigManager({
        accessibilityLevel: 'AA',
        testSuites: [{
          name: 'accessibility-tests',
          type: 'accessibility',
          enabled: true,
          timeout: 120000,
          retries: 3,
        }],
      });

      const testSuite = new AccessibilityTestSuite(configManager);
      const validator = (testSuite as any).validator;
      
      expect(validator.config.timeout).toBe(120000);
    });

    test('should use default timeout when not specified in config', () => {
      const configManager = new ConfigManager({
        accessibilityLevel: 'AA',
        testSuites: [{
          name: 'other-tests',
          type: 'unit',
          enabled: true,
          timeout: 30000,
          retries: 1,
        }],
      });

      const testSuite = new AccessibilityTestSuite(configManager);
      const validator = (testSuite as any).validator;
      
      expect(validator.config.timeout).toBe(90000); // Default timeout
    });
  });

  describe('Error Handling', () => {
    test('should handle browser launch failures gracefully', async () => {
      const validator = new AccessibilityValidator({
        baseUrl: 'http://invalid-url:99999',
        pages: ['/'],
        timeout: 1000,
      });

      // Mock browser launch failure
      const originalChromium = require('@playwright/test').chromium;
      const mockChromium = {
        launch: jest.fn().mockRejectedValue(new Error('Browser launch failed')),
      };
      
      // This test verifies the error handling structure exists
      expect(validator.validate).toBeDefined();
      expect(typeof validator.validate).toBe('function');
    });

    test('should handle page navigation failures', () => {
      const validator = new AccessibilityValidator();
      
      // Test the private method that handles page validation errors
      const mockPageResult = (validator as any).validatePage;
      expect(mockPageResult).toBeDefined();
      expect(typeof mockPageResult).toBe('function');
    });

    test('should handle axe-core analysis failures', () => {
      const validator = new AccessibilityValidator();
      
      // Mock axe results with missing impact
      const mockAxeResults = {
        violations: [{
          id: 'test-rule',
          description: 'Test violation',
          help: 'Test help',
          helpUrl: 'http://test.com',
          nodes: [{
            target: ['div'],
            html: '<div></div>',
          }],
        }],
        incomplete: [],
        passes: [],
      };

      const processed = (validator as any).processAxeResults(mockAxeResults);
      
      expect(processed.violations).toHaveLength(1);
      expect(processed.violations[0].impact).toBe('moderate'); // Default impact
    });
  });

  describe('Feature Coverage', () => {
    test('should implement all required task features', () => {
      const validator = new AccessibilityValidator();
      
      // Verify axe-core integration
      expect((validator as any).runAxeAnalysis).toBeDefined();
      expect((validator as any).processAxeResults).toBeDefined();
      
      // Verify WCAG 2.1 AA compliance checking
      expect((validator as any).getAxeTags).toBeDefined();
      
      // Verify violation reporting with remediation guidance
      expect(validator.generateReport).toBeDefined();
      expect((validator as any).getRemediationGuidance).toBeDefined();
      
      // Verify keyboard navigation testing
      expect((validator as any).testKeyboardNavigation).toBeDefined();
      
      // Verify screen reader compatibility tests
      expect((validator as any).testScreenReaderCompatibility).toBeDefined();
      
      // Verify color contrast validation
      expect((validator as any).testColorContrast).toBeDefined();
      expect((validator as any).calculateContrastRatio).toBeDefined();
    });

    test('should support comprehensive accessibility testing configuration', () => {
      const fullConfig = {
        baseUrl: 'http://localhost:4175',
        pages: ['/', '/team-sales', '/harbor-city'],
        wcagLevel: 'AA' as const,
        includeRules: ['color-contrast', 'image-alt'],
        excludeRules: ['bypass'],
        timeout: 30000,
        retries: 2,
        keyboardNavigation: true,
        colorContrast: true,
        screenReader: true,
      };

      const validator = new AccessibilityValidator(fullConfig);
      const config = (validator as any).config;
      
      expect(config.baseUrl).toBe(fullConfig.baseUrl);
      expect(config.pages).toEqual(fullConfig.pages);
      expect(config.wcagLevel).toBe(fullConfig.wcagLevel);
      expect(config.includeRules).toEqual(fullConfig.includeRules);
      expect(config.excludeRules).toEqual(fullConfig.excludeRules);
      expect(config.timeout).toBe(fullConfig.timeout);
      expect(config.retries).toBe(fullConfig.retries);
      expect(config.keyboardNavigation).toBe(fullConfig.keyboardNavigation);
      expect(config.colorContrast).toBe(fullConfig.colorContrast);
      expect(config.screenReader).toBe(fullConfig.screenReader);
    });
  });
});