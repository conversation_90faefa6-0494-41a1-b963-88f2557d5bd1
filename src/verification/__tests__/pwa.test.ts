/**
 * Unit tests for PWA validation module
 */

import { describe, it, expect, jest, beforeEach, afterEach } from '@jest/globals';
import { PWAValidator, PWATestSuite, validatePWA } from '../pwa';
import { PWAValidationResult } from '../types';

// Mock fetch for manifest validation
global.fetch = jest.fn() as jest.MockedFunction<typeof fetch>;

describe('PWAValidator', () => {
  let validator: PWAValidator;
  const mockConfig = {
    baseUrl: 'http://localhost:3000',
    manifestPath: '/manifest.json',
    serviceWorkerPath: '/sw.js',
    timeout: 10000,
    offlinePages: ['/', '/test'],
    criticalResources: ['/icon.png', '/manifest.json']
  };

  beforeEach(() => {
    validator = new PWAValidator(mockConfig);
    jest.clearAllMocks();
  });

  afterEach(() => {
    jest.resetAllMocks();
  });

  describe('Service Worker Registration Testing', () => {
    it('should detect successful service worker registration', async () => {
      // Mock the individual validation methods
      jest.spyOn(validator as any, 'testServiceWorkerRegistration').mockResolvedValueOnce(true);
      jest.spyOn(validator as any, 'validateManifest').mockResolvedValueOnce(true);
      jest.spyOn(validator as any, 'testOfflineFunctionality').mockResolvedValueOnce(true);
      jest.spyOn(validator as any, 'testInstallability').mockResolvedValueOnce(true);
      jest.spyOn(validator as any, 'validateCacheStrategy').mockResolvedValueOnce({
        staticAssetsCache: true,
        apiResponseCache: true,
        offlinePages: ['/']
      });

      const result = await validator.validate();

      expect(result?.serviceWorkerRegistered).toBe(true);
    });

    it('should detect failed service worker registration', async () => {
      // Mock failed service worker registration
      jest.spyOn(validator as any, 'testServiceWorkerRegistration').mockResolvedValueOnce(false);
      jest.spyOn(validator as any, 'validateManifest').mockResolvedValueOnce(true);
      jest.spyOn(validator as any, 'testOfflineFunctionality').mockResolvedValueOnce(true);
      jest.spyOn(validator as any, 'testInstallability').mockResolvedValueOnce(true);
      jest.spyOn(validator as any, 'validateCacheStrategy').mockResolvedValueOnce({
        staticAssetsCache: true,
        apiResponseCache: true,
        offlinePages: ['/']
      });

      const result = await validator.validate();

      expect(result?.serviceWorkerRegistered).toBe(false);
    });

    it('should handle service worker registration errors', async () => {
      // Mock error during service worker check
      jest.spyOn(validator as any, 'testServiceWorkerRegistration').mockRejectedValueOnce(new Error('Navigation failed'));
      jest.spyOn(validator as any, 'validateManifest').mockResolvedValueOnce(true);
      jest.spyOn(validator as any, 'testOfflineFunctionality').mockResolvedValueOnce(true);
      jest.spyOn(validator as any, 'testInstallability').mockResolvedValueOnce(true);
      jest.spyOn(validator as any, 'validateCacheStrategy').mockResolvedValueOnce({
        staticAssetsCache: true,
        apiResponseCache: true,
        offlinePages: ['/']
      });

      const result = await validator.validate();

      expect(result.serviceWorkerRegistered).toBe(false);
    });
  });

  describe('PWA Manifest Validation', () => {
    it('should validate a correct PWA manifest', async () => {
      // Mock all validation methods
      jest.spyOn(validator as any, 'testServiceWorkerRegistration').mockResolvedValueOnce(true);
      jest.spyOn(validator as any, 'validateManifest').mockResolvedValueOnce(true);
      jest.spyOn(validator as any, 'testOfflineFunctionality').mockResolvedValueOnce(true);
      jest.spyOn(validator as any, 'testInstallability').mockResolvedValueOnce(true);
      jest.spyOn(validator as any, 'validateCacheStrategy').mockResolvedValueOnce({
        staticAssetsCache: true,
        apiResponseCache: true,
        offlinePages: ['/']
      });

      const result = await validator.validate();

      expect(result?.manifestValid).toBe(true);
    });

    it('should detect invalid PWA manifest', async () => {
      // Mock failed manifest validation
      jest.spyOn(validator as any, 'testServiceWorkerRegistration').mockResolvedValueOnce(true);
      jest.spyOn(validator as any, 'validateManifest').mockResolvedValueOnce(false);
      jest.spyOn(validator as any, 'testOfflineFunctionality').mockResolvedValueOnce(true);
      jest.spyOn(validator as any, 'testInstallability').mockResolvedValueOnce(true);
      jest.spyOn(validator as any, 'validateCacheStrategy').mockResolvedValueOnce({
        staticAssetsCache: true,
        apiResponseCache: true,
        offlinePages: ['/']
      });

      const result = await validator.validate();

      expect(result?.manifestValid).toBe(false);
    });

    it('should handle missing manifest file', async () => {
      // Mock failed manifest validation
      jest.spyOn(validator as any, 'testServiceWorkerRegistration').mockResolvedValueOnce(true);
      jest.spyOn(validator as any, 'validateManifest').mockResolvedValueOnce(false);
      jest.spyOn(validator as any, 'testOfflineFunctionality').mockResolvedValueOnce(true);
      jest.spyOn(validator as any, 'testInstallability').mockResolvedValueOnce(true);
      jest.spyOn(validator as any, 'validateCacheStrategy').mockResolvedValueOnce({
        staticAssetsCache: true,
        apiResponseCache: true,
        offlinePages: ['/']
      });

      const result = await validator.validate();

      expect(result.manifestValid).toBe(false);
    });

    it('should handle manifest fetch errors', async () => {
      // Mock failed manifest validation
      jest.spyOn(validator as any, 'testServiceWorkerRegistration').mockResolvedValueOnce(true);
      jest.spyOn(validator as any, 'validateManifest').mockResolvedValueOnce(false);
      jest.spyOn(validator as any, 'testOfflineFunctionality').mockResolvedValueOnce(true);
      jest.spyOn(validator as any, 'testInstallability').mockResolvedValueOnce(true);
      jest.spyOn(validator as any, 'validateCacheStrategy').mockResolvedValueOnce({
        staticAssetsCache: true,
        apiResponseCache: true,
        offlinePages: ['/']
      });

      const result = await validator.validate();

      expect(result.manifestValid).toBe(false);
    });
  });

  describe('Offline Functionality Testing', () => {
    it('should validate offline functionality for all pages', async () => {
      // Mock all validation methods
      jest.spyOn(validator as any, 'testServiceWorkerRegistration').mockResolvedValueOnce(true);
      jest.spyOn(validator as any, 'validateManifest').mockResolvedValueOnce(true);
      jest.spyOn(validator as any, 'testOfflineFunctionality').mockResolvedValueOnce(true);
      jest.spyOn(validator as any, 'testInstallability').mockResolvedValueOnce(true);
      jest.spyOn(validator as any, 'validateCacheStrategy').mockResolvedValueOnce({
        staticAssetsCache: true,
        apiResponseCache: true,
        offlinePages: ['/']
      });

      const result = await validator.validate();

      expect(result?.offlineFunctionality).toBe(true);
    });

    it('should detect offline functionality failures', async () => {
      // Mock failed offline functionality
      jest.spyOn(validator as any, 'testServiceWorkerRegistration').mockResolvedValueOnce(true);
      jest.spyOn(validator as any, 'validateManifest').mockResolvedValueOnce(true);
      jest.spyOn(validator as any, 'testOfflineFunctionality').mockResolvedValueOnce(false);
      jest.spyOn(validator as any, 'testInstallability').mockResolvedValueOnce(true);
      jest.spyOn(validator as any, 'validateCacheStrategy').mockResolvedValueOnce({
        staticAssetsCache: true,
        apiResponseCache: true,
        offlinePages: ['/']
      });

      const result = await validator.validate();

      expect(result?.offlineFunctionality).toBe(false);
    });

    it('should handle offline testing errors', async () => {
      // Mock error during offline functionality check
      jest.spyOn(validator as any, 'testServiceWorkerRegistration').mockResolvedValueOnce(true);
      jest.spyOn(validator as any, 'validateManifest').mockResolvedValueOnce(true);
      jest.spyOn(validator as any, 'testOfflineFunctionality').mockRejectedValueOnce(new Error('Offline navigation failed'));
      jest.spyOn(validator as any, 'testInstallability').mockResolvedValueOnce(true);
      jest.spyOn(validator as any, 'validateCacheStrategy').mockResolvedValueOnce({
        staticAssetsCache: true,
        apiResponseCache: true,
        offlinePages: ['/']
      });

      const result = await validator.validate();

      expect(result.offlineFunctionality).toBe(false);
    });
  });

  describe('PWA Installability Testing', () => {
    it('should detect installable PWA', async () => {
      // Mock all validation methods
      jest.spyOn(validator as any, 'testServiceWorkerRegistration').mockResolvedValueOnce(true);
      jest.spyOn(validator as any, 'validateManifest').mockResolvedValueOnce(true);
      jest.spyOn(validator as any, 'testOfflineFunctionality').mockResolvedValueOnce(true);
      jest.spyOn(validator as any, 'testInstallability').mockResolvedValueOnce(true);
      jest.spyOn(validator as any, 'validateCacheStrategy').mockResolvedValueOnce({
        staticAssetsCache: true,
        apiResponseCache: true,
        offlinePages: ['/']
      });

      const result = await validator.validate();

      expect(result?.installable).toBe(true);
    });

    it('should detect non-installable PWA', async () => {
      // Mock non-installable PWA
      jest.spyOn(validator as any, 'testServiceWorkerRegistration').mockResolvedValueOnce(true);
      jest.spyOn(validator as any, 'validateManifest').mockResolvedValueOnce(true);
      jest.spyOn(validator as any, 'testOfflineFunctionality').mockResolvedValueOnce(true);
      jest.spyOn(validator as any, 'testInstallability').mockResolvedValueOnce(false);
      jest.spyOn(validator as any, 'validateCacheStrategy').mockResolvedValueOnce({
        staticAssetsCache: true,
        apiResponseCache: true,
        offlinePages: ['/']
      });

      const result = await validator.validate();

      expect(result?.installable).toBe(false);
    });

    it('should handle installability testing errors', async () => {
      // Mock error during installability check
      jest.spyOn(validator as any, 'testServiceWorkerRegistration').mockResolvedValueOnce(true);
      jest.spyOn(validator as any, 'validateManifest').mockResolvedValueOnce(true);
      jest.spyOn(validator as any, 'testOfflineFunctionality').mockResolvedValueOnce(true);
      jest.spyOn(validator as any, 'testInstallability').mockRejectedValueOnce(new Error('Installability check failed'));
      jest.spyOn(validator as any, 'validateCacheStrategy').mockResolvedValueOnce({
        staticAssetsCache: true,
        apiResponseCache: true,
        offlinePages: ['/']
      });

      const result = await validator.validate();

      expect(result.installable).toBe(false);
    });
  });

  describe('Cache Strategy Validation', () => {
    it('should validate proper cache strategy', async () => {
      const mockCacheValidation = {
        staticAssetsCache: true,
        apiResponseCache: true,
        offlinePages: ['/', '/test']
      };

      // Mock all validation methods
      jest.spyOn(validator as any, 'testServiceWorkerRegistration').mockResolvedValueOnce(true);
      jest.spyOn(validator as any, 'validateManifest').mockResolvedValueOnce(true);
      jest.spyOn(validator as any, 'testOfflineFunctionality').mockResolvedValueOnce(true);
      jest.spyOn(validator as any, 'testInstallability').mockResolvedValueOnce(true);
      jest.spyOn(validator as any, 'validateCacheStrategy').mockResolvedValueOnce(mockCacheValidation);

      const result = await validator.validate();

      expect(result?.cacheStrategy).toEqual(mockCacheValidation);
    });

    it('should detect missing cache strategy', async () => {
      const mockCacheValidation = {
        staticAssetsCache: false,
        apiResponseCache: false,
        offlinePages: []
      };

      // Mock all validation methods
      jest.spyOn(validator as any, 'testServiceWorkerRegistration').mockResolvedValueOnce(true);
      jest.spyOn(validator as any, 'validateManifest').mockResolvedValueOnce(true);
      jest.spyOn(validator as any, 'testOfflineFunctionality').mockResolvedValueOnce(true);
      jest.spyOn(validator as any, 'testInstallability').mockResolvedValueOnce(true);
      jest.spyOn(validator as any, 'validateCacheStrategy').mockResolvedValueOnce(mockCacheValidation);

      const result = await validator.validate();

      expect(result?.cacheStrategy?.staticAssetsCache).toBe(false);
      expect(result?.cacheStrategy?.apiResponseCache).toBe(false);
    });

    it('should handle cache validation errors', async () => {
      // Mock error during cache validation
      jest.spyOn(validator as any, 'testServiceWorkerRegistration').mockResolvedValueOnce(true);
      jest.spyOn(validator as any, 'validateManifest').mockResolvedValueOnce(true);
      jest.spyOn(validator as any, 'testOfflineFunctionality').mockResolvedValueOnce(true);
      jest.spyOn(validator as any, 'testInstallability').mockResolvedValueOnce(true);
      jest.spyOn(validator as any, 'validateCacheStrategy').mockRejectedValueOnce(new Error('Cache check failed'));

      const result = await validator.validate();

      expect(result?.cacheStrategy?.staticAssetsCache).toBe(false);
      expect(result?.cacheStrategy?.apiResponseCache).toBe(false);
      expect(result?.cacheStrategy?.offlinePages).toEqual([]);
    });
  });
});

describe('PWATestSuite', () => {
  let testSuite: PWATestSuite;

  beforeEach(() => {
    testSuite = new PWATestSuite();
    jest.clearAllMocks();
  });

  it('should execute PWA validation and return test results', async () => {
    // Mock successful PWA validation
    const mockValidationResult: PWAValidationResult = {
      serviceWorkerRegistered: true,
      manifestValid: true,
      offlineFunctionality: true,
      installable: true,
      cacheStrategy: {
        staticAssetsCache: true,
        apiResponseCache: true,
        offlinePages: ['/', '/test']
      }
    };

    // Mock the validator
    jest.spyOn(PWAValidator.prototype, 'validate').mockResolvedValueOnce(mockValidationResult);

    const result = await testSuite.execute();

    expect(result?.passed).toBe(true);
    expect(result?.testCount).toBe(5);
    expect(result?.failures).toHaveLength(0);
    expect(result?.duration).toBeGreaterThan(0);
  });

  it('should report failures for failed PWA validation', async () => {
    // Mock failed PWA validation
    const mockValidationResult: PWAValidationResult = {
      serviceWorkerRegistered: false,
      manifestValid: false,
      offlineFunctionality: true,
      installable: true,
      cacheStrategy: {
        staticAssetsCache: false,
        apiResponseCache: true,
        offlinePages: ['/', '/test']
      }
    };

    jest.spyOn(PWAValidator.prototype, 'validate').mockResolvedValueOnce(mockValidationResult);

    const result = await testSuite.execute();

    expect(result?.passed).toBe(false);
    expect(result?.testCount).toBe(5);
    expect(result?.failures).toHaveLength(3); // service worker, manifest, static cache
    
    const failureNames = result?.failures?.map(f => f.testName);
    expect(failureNames).toContain('Service Worker Registration');
    expect(failureNames).toContain('PWA Manifest Validation');
    expect(failureNames).toContain('Static Assets Caching');
  });

  it('should handle validation errors gracefully', async () => {
    jest.spyOn(PWAValidator.prototype, 'validate').mockRejectedValueOnce(
      new Error('PWA validation failed')
    );

    const result = await testSuite.execute();

    expect(result?.passed).toBe(false);
    expect(result?.testCount).toBe(1);
    expect(result?.failures).toHaveLength(1);
    expect(result?.failures?.[0]?.testName).toBe('PWA Validation Suite');
    expect(result?.failures?.[0]?.error).toBe('PWA validation failed');
  });

  it('should have correct test suite properties', () => {
    expect(testSuite?.name).toBe('pwa');
    expect(testSuite?.type).toBe('e2e');
  });
});

describe('validatePWA standalone function', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should use default configuration when no config provided', async () => {
    const mockResult: PWAValidationResult = {
      serviceWorkerRegistered: true,
      manifestValid: true,
      offlineFunctionality: true,
      installable: true,
      cacheStrategy: {
        staticAssetsCache: true,
        apiResponseCache: true,
        offlinePages: ['/']
      }
    };

    jest.spyOn(PWAValidator.prototype, 'validate').mockResolvedValueOnce(mockResult);

    const result = await validatePWA();

    expect(result).toEqual(mockResult);
  });

  it('should merge custom configuration with defaults', async () => {
    const customConfig = {
      baseUrl: 'http://localhost:8080',
      timeout: 5000
    };

    const mockResult: PWAValidationResult = {
      serviceWorkerRegistered: true,
      manifestValid: true,
      offlineFunctionality: true,
      installable: true,
      cacheStrategy: {
        staticAssetsCache: true,
        apiResponseCache: true,
        offlinePages: ['/']
      }
    };

    jest.spyOn(PWAValidator.prototype, 'validate').mockResolvedValueOnce(mockResult);

    const result = await validatePWA(customConfig);

    expect(result).toEqual(mockResult);
  });
});

describe('Manifest Content Validation', () => {
  let validator: PWAValidator;

  beforeEach(() => {
    validator = new PWAValidator({
      baseUrl: 'http://localhost:3000',
      manifestPath: '/manifest.json',
      serviceWorkerPath: '/sw.js',
      timeout: 10000,
      offlinePages: ['/'],
      criticalResources: []
    });
  });

  it('should validate manifest with all required fields', async () => {
    const validManifest = {
      name: 'Test App',
      short_name: 'Test',
      start_url: '/',
      display: 'standalone',
      icons: [
        {
          src: '/icon-192.png',
          sizes: '192x192',
          type: 'image/png'
        }
      ],
      theme_color: '#000000',
      background_color: '#ffffff',
      description: 'Test application'
    };

    (global.fetch as jest.MockedFunction<typeof fetch>).mockResolvedValueOnce({
      ok: true,
      json: () => Promise.resolve(validManifest)
    } as Response);

    const result = await validator.validate();
    expect(result?.manifestValid).toBe(true);
  });

  it('should detect missing required fields', async () => {
    const invalidManifest = {
      description: 'Test app without required fields'
    };

    (global.fetch as jest.MockedFunction<typeof fetch>).mockResolvedValueOnce({
      ok: true,
      json: () => Promise.resolve(invalidManifest)
    } as Response);

    const result = await validator.validate();
    expect(result?.manifestValid).toBe(false);
  });

  it('should validate icon requirements', async () => {
    const manifestWithSmallIcons = {
      name: 'Test App',
      start_url: '/',
      display: 'standalone',
      icons: [
        {
          src: '/icon-48.png',
          sizes: '48x48',
          type: 'image/png'
        }
      ]
    };

    (global.fetch as jest.MockedFunction<typeof fetch>).mockResolvedValueOnce({
      ok: true,
      json: () => Promise.resolve(manifestWithSmallIcons)
    } as Response);

    const result = await validator.validate();
    expect(result.manifestValid).toBe(false);
  });
});