/**
 * Integration tests for the verification pipeline
 * Tests the complete pipeline orchestration with real components
 */

import { VerificationPipeline, runVerification } from '../pipeline';
import { PipelineOptions, VerificationConfig } from '../types';
import { promises as fs } from 'fs';
import { spawn } from 'child_process';
import path from 'path';

// Mock external dependencies but allow internal components to work
jest.mock('child_process');
jest.mock('fs', () => ({
  promises: {
    rm: jest.fn(),
    readdir: jest.fn(),
    stat: jest.fn(),
    mkdir: jest.fn(),
    writeFile: jest.fn(),
  },
}));

jest.mock('@playwright/test', () => ({
  chromium: {
    launch: jest.fn().mockResolvedValue({
      newPage: jest.fn().mockResolvedValue({
        goto: jest.fn(),
        evaluate: jest.fn(),
        close: jest.fn(),
      }),
      close: jest.fn(),
    }),
  },
}));

const mockSpawn = spawn as jest.MockedFunction<typeof spawn>;
const mockFs = fs as jest.Mocked<typeof fs>;

describe('VerificationPipeline Integration Tests', () => {
  let mockConfig: VerificationConfig;
  let mockOptions: PipelineOptions;

  beforeEach(() => {
    jest.clearAllMocks();

    mockConfig = {
      buildSettings: {
        mode: 'production',
        sourceMaps: false,
        minification: true,
      },
      performanceThresholds: {
        lcp: 2500,
        fid: 100,
        cls: 0.1,
        lighthousePerformance: 90,
        fcp: 1800,
        ttfb: 600,
        speedIndex: 3000,
      },
      accessibilityLevel: 'AA',
      accessibilityRules: {
        include: ['color-contrast', 'alt-text'],
        exclude: ['duplicate-id'],
        customRules: [
          {
            id: 'custom-focus',
            enabled: true,
            severity: 'error',
            options: { checkTabIndex: true },
          },
        ],
      },
      testSuites: [
        {
          name: 'unit-tests',
          type: 'unit',
          enabled: true,
          timeout: 30000,
          retries: 1,
          tags: ['critical'],
          description: 'Unit tests for core functionality',
          priority: 'high',
        },
        {
          name: 'integration-tests',
          type: 'integration',
          enabled: true,
          timeout: 60000,
          retries: 2,
          tags: ['integration'],
          description: 'Integration tests for component interactions',
          priority: 'medium',
        },
        {
          name: 'e2e-tests',
          type: 'e2e',
          enabled: false, // Disabled for faster testing
          timeout: 120000,
          retries: 3,
          tags: ['e2e', 'critical'],
          description: 'End-to-end user journey tests',
          priority: 'critical',
        },
      ],
      externalDependencies: [
        {
          name: 'Google Maps API',
          url: 'https://maps.googleapis.com/maps/api/js?key=test',
          timeout: 5000,
          critical: true,
        },
        {
          name: 'Google Fonts',
          url: 'https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap',
          timeout: 3000,
          critical: false,
        },
      ],
      customThresholds: {
        bundleSize: 2 * 1024 * 1024, // 2MB
        chunkSize: 500 * 1024, // 500KB
        imageSize: 100 * 1024, // 100KB
      },
      reportingOptions: {
        includeScreenshots: true,
        includeNetworkLogs: true,
        includeConsoleErrors: true,
        detailLevel: 'detailed',
      },
    };

    mockOptions = {
      config: mockConfig,
      verbose: true,
      outputFormat: 'json',
    };

    // Setup default successful mocks
    setupSuccessfulMocks();
  });

  function setupSuccessfulMocks() {
    // Mock successful build process
    const mockBuildProcess = {
      stdout: {
        on: jest.fn((event, callback) => {
          if (event === 'data') {
            callback('Build completed successfully');
          }
        }),
      },
      stderr: {
        on: jest.fn(),
      },
      on: jest.fn((event, callback) => {
        if (event === 'close') {
          setTimeout(() => callback(0), 10); // Success exit code
        }
      }),
    };

    // Mock successful test process
    const mockTestProcess = {
      stdout: {
        on: jest.fn((event, callback) => {
          if (event === 'data') {
            callback(JSON.stringify({
              numTotalTests: 25,
              numPassedTests: 25,
              numFailedTests: 0,
              runTime: 5000,
              success: true,
            }));
          }
        }),
      },
      stderr: {
        on: jest.fn(),
      },
      on: jest.fn((event, callback) => {
        if (event === 'close') {
          setTimeout(() => callback(0), 10);
        }
      }),
    };

    mockSpawn.mockImplementation((command, args) => {
      if (command === 'npm' && args?.includes('run') && args?.includes('build')) {
        return mockBuildProcess as any;
      }
      if (command === 'npm' && args?.includes('run') && args?.includes('test')) {
        return mockTestProcess as any;
      }
      return mockBuildProcess as any; // Default to build process
    });

    // Mock file system operations
    mockFs.rm.mockResolvedValue(undefined);
    mockFs.readdir.mockResolvedValue([
      'index.abc123.js',
      'vendor.def456.js',
      'styles.ghi789.css',
    ] as any);
    mockFs.stat.mockResolvedValue({ size: 1024 * 500 } as any); // 500KB files
    mockFs.mkdir.mockResolvedValue(undefined);
    mockFs.writeFile.mockResolvedValue(undefined);
  }

  describe('Complete Pipeline Execution', () => {
    it('should execute all enabled stages in correct order', async () => {
      const pipeline = new VerificationPipeline(mockOptions);
      const report = await pipeline.execute();

      expect(report.overallStatus).toBe('passed');
      expect(report.deploymentReady).toBe(true);
      expect(report.timestamp).toBeInstanceOf(Date);

      // Verify all report sections are populated
      expect(report.buildVerification).toBeDefined();
      expect(report.testResults).toBeDefined();
      expect(report.performanceMetrics).toBeDefined();
      expect(report.accessibilityResults).toBeDefined();
      expect(report.pwaValidation).toBeDefined();
      expect(report.dependencyStatus).toBeDefined();
      expect(report.recommendations).toBeInstanceOf(Array);
    });

    it('should handle mixed success and failure scenarios', async () => {
      // Mock build success but test failure
      const mockFailedTestProcess = {
        stdout: {
          on: jest.fn((event, callback) => {
            if (event === 'data') {
              callback(JSON.stringify({
                numTotalTests: 25,
                numPassedTests: 20,
                numFailedTests: 5,
                runTime: 5000,
                success: false,
              }));
            }
          }),
        },
        stderr: {
          on: jest.fn((event, callback) => {
            if (event === 'data') {
              callback('Test failures detected');
            }
          }),
        },
        on: jest.fn((event, callback) => {
          if (event === 'close') {
            setTimeout(() => callback(1), 10); // Failure exit code
          }
        }),
      };

      mockSpawn.mockImplementation((command, args) => {
        if (command === 'npm' && args?.includes('test')) {
          return mockFailedTestProcess as any;
        }
        // Return successful build process for other commands
        return {
          stdout: { on: jest.fn((event, cb) => event === 'data' && cb('Build success')) },
          stderr: { on: jest.fn() },
          on: jest.fn((event, cb) => event === 'close' && setTimeout(() => cb(0), 10)),
        } as any;
      });

      const pipeline = new VerificationPipeline(mockOptions);
      const report = await pipeline.execute();

      expect(report.overallStatus).toBe('failed');
      expect(report.deploymentReady).toBe(false);
      expect(report.recommendations).toContain(
        expect.stringContaining('Fix tests issues before deployment')
      );
    });

    it('should respect stage skipping configuration', async () => {
      const skipOptions = {
        ...mockOptions,
        skipStages: ['performance', 'accessibility'],
      };

      const pipeline = new VerificationPipeline(skipOptions);
      const report = await pipeline.execute();

      expect(report.overallStatus).toBe('passed');
      
      // Performance and accessibility should have empty/default results
      expect(report.performanceMetrics.lcp).toBe(0);
      expect(report.accessibilityResults.testedPages).toHaveLength(0);
    });

    it('should handle configuration validation errors', async () => {
      const invalidConfig = {
        ...mockConfig,
        performanceThresholds: {
          lcp: -1, // Invalid negative threshold
          fid: 100,
          cls: 0.1,
          lighthousePerformance: 90,
        },
      };

      const invalidOptions = {
        ...mockOptions,
        config: invalidConfig,
      };

      const pipeline = new VerificationPipeline(invalidOptions);
      const report = await pipeline.execute();

      // Should still complete but may have warnings or errors
      expect(report).toBeDefined();
      expect(report.timestamp).toBeInstanceOf(Date);
    });

    it('should generate comprehensive recommendations', async () => {
      // Mock various warning conditions
      const mockWarningBuildProcess = {
        stdout: {
          on: jest.fn((event, callback) => {
            if (event === 'data') {
              callback('src/test.ts(10,5): warning TS6133: Variable is declared but never used');
            }
          }),
        },
        stderr: { on: jest.fn() },
        on: jest.fn((event, callback) => {
          if (event === 'close') {
            setTimeout(() => callback(0), 10); // Success but with warnings
          }
        }),
      };

      mockSpawn.mockReturnValue(mockWarningBuildProcess as any);

      const pipeline = new VerificationPipeline(mockOptions);
      const report = await pipeline.execute();

      expect(report.recommendations).toBeInstanceOf(Array);
      expect(report.recommendations.length).toBeGreaterThan(0);
      
      if (report.overallStatus === 'warning') {
        expect(report.recommendations).toContain(
          expect.stringContaining('warnings')
        );
      }
    });
  });

  describe('Error Recovery and Resilience', () => {
    it('should recover from transient build failures', async () => {
      let buildAttempts = 0;
      const mockUnreliableBuildProcess = {
        stdout: { on: jest.fn() },
        stderr: { on: jest.fn() },
        on: jest.fn((event, callback) => {
          if (event === 'close') {
            buildAttempts++;
            // Fail first attempt, succeed on retry
            const exitCode = buildAttempts === 1 ? 1 : 0;
            setTimeout(() => callback(exitCode), 10);
          }
        }),
      };

      mockSpawn.mockReturnValue(mockUnreliableBuildProcess as any);

      const pipeline = new VerificationPipeline(mockOptions);
      const report = await pipeline.execute();

      // Should eventually succeed after retry
      expect(report).toBeDefined();
      expect(buildAttempts).toBeGreaterThan(1);
    });

    it('should handle critical system errors gracefully', async () => {
      // Mock spawn to throw system error
      mockSpawn.mockImplementation(() => {
        throw new Error('System error: Command not found');
      });

      const pipeline = new VerificationPipeline(mockOptions);
      const report = await pipeline.execute();

      expect(report.overallStatus).toBe('failed');
      expect(report.deploymentReady).toBe(false);
      expect(report.recommendations).toContain(
        expect.stringContaining('Pipeline execution failed')
      );
    });

    it('should handle file system errors during metrics collection', async () => {
      // Mock file system operations to fail
      mockFs.readdir.mockRejectedValue(new Error('Permission denied'));
      mockFs.stat.mockRejectedValue(new Error('File not found'));

      const pipeline = new VerificationPipeline(mockOptions);
      const report = await pipeline.execute();

      // Should still complete pipeline execution
      expect(report).toBeDefined();
      expect(report.buildVerification.outputSize.total).toBe(0);
    });
  });

  describe('Performance and Scalability', () => {
    it('should handle large numbers of test suites efficiently', async () => {
      const manyTestSuites = Array.from({ length: 20 }, (_, i) => ({
        name: `test-suite-${i}`,
        type: 'unit' as const,
        enabled: true,
        timeout: 30000,
        retries: 0,
      }));

      const scalabilityConfig = {
        ...mockConfig,
        testSuites: manyTestSuites,
      };

      const scalabilityOptions = {
        ...mockOptions,
        config: scalabilityConfig,
      };

      const startTime = Date.now();
      const pipeline = new VerificationPipeline(scalabilityOptions);
      const report = await pipeline.execute();
      const executionTime = Date.now() - startTime;

      expect(report).toBeDefined();
      expect(executionTime).toBeLessThan(30000); // Should complete within 30 seconds
    });

    it('should handle concurrent stage execution when possible', async () => {
      // This test verifies that the pipeline can handle multiple operations
      // without blocking unnecessarily
      const pipeline = new VerificationPipeline(mockOptions);
      
      const startTime = Date.now();
      const report = await pipeline.execute();
      const executionTime = Date.now() - startTime;

      expect(report).toBeDefined();
      expect(executionTime).toBeLessThan(10000); // Should be reasonably fast with mocks
    });
  });

  describe('Reporting and Output', () => {
    it('should generate detailed reports with all sections', async () => {
      const pipeline = new VerificationPipeline(mockOptions);
      const report = await pipeline.execute();

      // Verify report structure completeness
      expect(report).toHaveProperty('timestamp');
      expect(report).toHaveProperty('overallStatus');
      expect(report).toHaveProperty('buildVerification');
      expect(report).toHaveProperty('testResults');
      expect(report).toHaveProperty('performanceMetrics');
      expect(report).toHaveProperty('accessibilityResults');
      expect(report).toHaveProperty('pwaValidation');
      expect(report).toHaveProperty('dependencyStatus');
      expect(report).toHaveProperty('recommendations');
      expect(report).toHaveProperty('deploymentReady');

      // Verify nested structure
      expect(report.buildVerification).toHaveProperty('success');
      expect(report.buildVerification).toHaveProperty('buildTime');
      expect(report.buildVerification).toHaveProperty('errors');
      expect(report.buildVerification).toHaveProperty('warnings');
      expect(report.buildVerification).toHaveProperty('outputSize');

      expect(report.performanceMetrics).toHaveProperty('lcp');
      expect(report.performanceMetrics).toHaveProperty('fid');
      expect(report.performanceMetrics).toHaveProperty('cls');
      expect(report.performanceMetrics).toHaveProperty('lighthouse');

      expect(report.accessibilityResults).toHaveProperty('compliant');
      expect(report.accessibilityResults).toHaveProperty('violations');
      expect(report.accessibilityResults).toHaveProperty('warnings');
      expect(report.accessibilityResults).toHaveProperty('testedPages');
    });

    it('should include custom threshold violations in recommendations', async () => {
      // Mock large bundle size
      mockFs.stat.mockResolvedValue({ size: 3 * 1024 * 1024 } as any); // 3MB > 2MB threshold

      const pipeline = new VerificationPipeline(mockOptions);
      const report = await pipeline.execute();

      // Should detect bundle size threshold violation
      expect(report.buildVerification.outputSize.total).toBeGreaterThan(
        mockConfig.customThresholds!.bundleSize!
      );
    });
  });
});

describe('runVerification Integration', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    setupSuccessfulMocks();
  });

  function setupSuccessfulMocks() {
    const mockProcess = {
      stdout: { on: jest.fn((event, cb) => event === 'data' && cb('Success')) },
      stderr: { on: jest.fn() },
      on: jest.fn((event, cb) => event === 'close' && setTimeout(() => cb(0), 10)),
    };

    mockSpawn.mockReturnValue(mockProcess as any);
    mockFs.rm.mockResolvedValue(undefined);
    mockFs.readdir.mockResolvedValue(['index.js'] as any);
    mockFs.stat.mockResolvedValue({ size: 1024 } as any);
    mockFs.mkdir.mockResolvedValue(undefined);
    mockFs.writeFile.mockResolvedValue(undefined);
  }

  it('should execute complete verification workflow', async () => {
    const options: PipelineOptions = {
      config: {
        buildSettings: { mode: 'production', sourceMaps: false, minification: true },
        performanceThresholds: { lcp: 2500, fid: 100, cls: 0.1, lighthousePerformance: 90 },
        accessibilityLevel: 'AA',
        testSuites: [
          {
            name: 'unit',
            type: 'unit',
            enabled: true,
            timeout: 30000,
            retries: 0,
          },
        ],
        externalDependencies: [],
      },
      verbose: false,
      outputFormat: 'json',
    };

    const report = await runVerification(options);

    expect(report).toBeDefined();
    expect(report.overallStatus).toMatch(/^(passed|warning|failed)$/);
    expect(report.deploymentReady).toEqual(expect.any(Boolean));
    expect(report.timestamp).toBeInstanceOf(Date);
  });

  it('should handle different output formats', async () => {
    const jsonOptions: PipelineOptions = {
      config: {
        buildSettings: { mode: 'production', sourceMaps: false, minification: true },
        performanceThresholds: { lcp: 2500, fid: 100, cls: 0.1, lighthousePerformance: 90 },
        accessibilityLevel: 'AA',
        testSuites: [],
        externalDependencies: [],
      },
      outputFormat: 'json',
    };

    const htmlOptions: PipelineOptions = {
      ...jsonOptions,
      outputFormat: 'html',
    };

    const consoleOptions: PipelineOptions = {
      ...jsonOptions,
      outputFormat: 'console',
    };

    // All should execute successfully regardless of output format
    const jsonReport = await runVerification(jsonOptions);
    const htmlReport = await runVerification(htmlOptions);
    const consoleReport = await runVerification(consoleOptions);

    expect(jsonReport).toBeDefined();
    expect(htmlReport).toBeDefined();
    expect(consoleReport).toBeDefined();

    // Reports should have same structure regardless of output format
    expect(jsonReport.overallStatus).toBeDefined();
    expect(htmlReport.overallStatus).toBeDefined();
    expect(consoleReport.overallStatus).toBeDefined();
  });
});