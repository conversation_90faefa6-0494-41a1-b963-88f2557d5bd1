/**
 * End-to-end tests for the complete verification workflow
 * Tests the entire verification system from CLI to report generation
 */

import { runVerification } from '../pipeline';
import { generateVerificationReport } from '../reporting';
import { ConfigManager } from '../config';
import { PipelineOptions, VerificationConfig, VerificationReport } from '../types';
import { promises as fs } from 'fs';
import { spawn } from 'child_process';
import path from 'path';

// Mock external dependencies
jest.mock('child_process');
jest.mock('fs', () => ({
  promises: {
    rm: jest.fn(),
    readdir: jest.fn(),
    stat: jest.fn(),
    mkdir: jest.fn(),
    writeFile: jest.fn(),
    readFile: jest.fn(),
  },
}));

jest.mock('@playwright/test', () => ({
  chromium: {
    launch: jest.fn().mockResolvedValue({
      newPage: jest.fn().mockResolvedValue({
        goto: jest.fn(),
        evaluate: jest.fn().mockResolvedValue({
          lcp: 2000,
          fid: 50,
          cls: 0.05,
          fcp: 1500,
        }),
        close: jest.fn(),
      }),
      close: jest.fn(),
    }),
  },
}));

// Mock lighthouse
jest.mock('lighthouse', () => ({
  default: jest.fn().mockResolvedValue({
    lhr: {
      categories: {
        performance: { score: 0.95 },
        accessibility: { score: 0.98 },
        'best-practices': { score: 0.90 },
        seo: { score: 0.95 },
      },
      audits: {
        'largest-contentful-paint': { numericValue: 2000 },
        'first-input-delay': { numericValue: 50 },
        'cumulative-layout-shift': { numericValue: 0.05 },
        'first-contentful-paint': { numericValue: 1500 },
      },
    },
  }),
}));

// Mock axe-core
jest.mock('axe-core', () => ({
  run: jest.fn().mockResolvedValue({
    violations: [],
    incomplete: [],
    passes: [
      {
        id: 'color-contrast',
        description: 'Color contrast is sufficient',
        nodes: [{ target: ['body'] }],
      },
    ],
  }),
}));

const mockSpawn = spawn as jest.MockedFunction<typeof spawn>;
const mockFs = fs as jest.Mocked<typeof fs>;

describe('Complete Verification Workflow E2E Tests', () => {
  let testConfig: VerificationConfig;
  let testOptions: PipelineOptions;

  beforeEach(() => {
    jest.clearAllMocks();

    testConfig = {
      buildSettings: {
        mode: 'production',
        sourceMaps: false,
        minification: true,
      },
      performanceThresholds: {
        lcp: 2500,
        fid: 100,
        cls: 0.1,
        lighthousePerformance: 90,
        fcp: 1800,
        ttfb: 600,
        speedIndex: 3000,
      },
      accessibilityLevel: 'AA',
      accessibilityRules: {
        include: ['color-contrast', 'alt-text', 'heading-order'],
        exclude: ['duplicate-id'],
        customRules: [
          {
            id: 'custom-focus-management',
            enabled: true,
            severity: 'error',
            options: { checkTabIndex: true },
          },
        ],
      },
      testSuites: [
        {
          name: 'unit-tests',
          type: 'unit',
          enabled: true,
          timeout: 30000,
          retries: 1,
          tags: ['critical', 'fast'],
          description: 'Core unit tests for business logic',
          priority: 'high',
        },
        {
          name: 'integration-tests',
          type: 'integration',
          enabled: true,
          timeout: 60000,
          retries: 2,
          tags: ['integration', 'api'],
          description: 'Integration tests for component interactions',
          priority: 'medium',
        },
        {
          name: 'e2e-critical',
          type: 'e2e',
          enabled: true,
          timeout: 120000,
          retries: 3,
          tags: ['e2e', 'critical', 'user-journey'],
          description: 'Critical user journey end-to-end tests',
          priority: 'critical',
        },
      ],
      externalDependencies: [
        {
          name: 'Google Maps API',
          url: 'https://maps.googleapis.com/maps/api/js?key=test',
          timeout: 5000,
          critical: true,
        },
        {
          name: 'Google Fonts',
          url: 'https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap',
          timeout: 3000,
          critical: false,
        },
        {
          name: 'Analytics Service',
          url: 'https://analytics.example.com/api/health',
          timeout: 2000,
          critical: false,
        },
      ],
      customThresholds: {
        bundleSize: 2 * 1024 * 1024, // 2MB
        chunkSize: 500 * 1024, // 500KB
        imageSize: 100 * 1024, // 100KB
        cssSize: 200 * 1024, // 200KB
      },
      reportingOptions: {
        includeScreenshots: true,
        includeNetworkLogs: true,
        includeConsoleErrors: true,
        detailLevel: 'detailed',
      },
    };

    testOptions = {
      config: testConfig,
      verbose: true,
      outputFormat: 'json',
    };

    setupSuccessfulMocks();
  });

  function setupSuccessfulMocks() {
    // Mock successful build process
    const mockBuildProcess = {
      stdout: {
        on: jest.fn((event, callback) => {
          if (event === 'data') {
            callback('✓ Build completed successfully\n✓ Assets optimized\n✓ Bundle size: 1.8MB');
          }
        }),
      },
      stderr: {
        on: jest.fn((event, callback) => {
          if (event === 'data') {
            callback('warning: Some unused imports detected');
          }
        }),
      },
      on: jest.fn((event, callback) => {
        if (event === 'close') {
          setTimeout(() => callback(0), 50); // Success exit code
        }
      }),
    };

    // Mock successful test processes
    const mockUnitTestProcess = {
      stdout: {
        on: jest.fn((event, callback) => {
          if (event === 'data') {
            callback(JSON.stringify({
              numTotalTests: 45,
              numPassedTests: 45,
              numFailedTests: 0,
              runTime: 8000,
              success: true,
              testResults: [
                {
                  testFilePath: 'src/components/__tests__/Header.test.tsx',
                  numPassingTests: 8,
                  numFailingTests: 0,
                },
                {
                  testFilePath: 'src/utils/__tests__/helpers.test.ts',
                  numPassingTests: 12,
                  numFailingTests: 0,
                },
              ],
            }));
          }
        }),
      },
      stderr: { on: jest.fn() },
      on: jest.fn((event, callback) => {
        if (event === 'close') {
          setTimeout(() => callback(0), 30);
        }
      }),
    };

    const mockIntegrationTestProcess = {
      stdout: {
        on: jest.fn((event, callback) => {
          if (event === 'data') {
            callback(JSON.stringify({
              numTotalTests: 25,
              numPassedTests: 24,
              numFailedTests: 1,
              runTime: 15000,
              success: false,
              testResults: [
                {
                  testFilePath: 'src/integration/__tests__/api.test.ts',
                  numPassingTests: 10,
                  numFailingTests: 1,
                  failureMessage: 'API timeout in test environment',
                },
              ],
            }));
          }
        }),
      },
      stderr: { on: jest.fn() },
      on: jest.fn((event, callback) => {
        if (event === 'close') {
          setTimeout(() => callback(1), 40); // Failure exit code
        }
      }),
    };

    const mockE2ETestProcess = {
      stdout: {
        on: jest.fn((event, callback) => {
          if (event === 'data') {
            callback(JSON.stringify({
              numTotalTests: 15,
              numPassedTests: 15,
              numFailedTests: 0,
              runTime: 45000,
              success: true,
              testResults: [
                {
                  testFilePath: 'tests/user-journey.spec.ts',
                  numPassingTests: 8,
                  numFailingTests: 0,
                },
                {
                  testFilePath: 'tests/checkout-flow.spec.ts',
                  numPassingTests: 7,
                  numFailingTests: 0,
                },
              ],
            }));
          }
        }),
      },
      stderr: { on: jest.fn() },
      on: jest.fn((event, callback) => {
        if (event === 'close') {
          setTimeout(() => callback(0), 60);
        }
      }),
    };

    // Mock spawn to return appropriate process based on command
    mockSpawn.mockImplementation((command, args) => {
      if (command === 'npm' && args?.includes('build')) {
        return mockBuildProcess as any;
      }
      if (command === 'npm' && args?.includes('test') && args?.includes('unit')) {
        return mockUnitTestProcess as any;
      }
      if (command === 'npm' && args?.includes('test') && args?.includes('integration')) {
        return mockIntegrationTestProcess as any;
      }
      if (command === 'npx' && args?.includes('playwright')) {
        return mockE2ETestProcess as any;
      }
      return mockBuildProcess as any; // Default fallback
    });

    // Mock file system operations
    mockFs.rm.mockResolvedValue(undefined);
    mockFs.mkdir.mockResolvedValue(undefined);
    mockFs.writeFile.mockResolvedValue(undefined);
    mockFs.readFile.mockResolvedValue('{}');

    // Mock build output files
    mockFs.readdir.mockResolvedValue([
      'index.abc123def.js',
      'vendor.xyz789ghi.js',
      'styles.jkl456mno.css',
      'assets/logo.png',
      'assets/hero-image.webp',
    ] as any);

    // Mock file sizes
    mockFs.stat.mockImplementation((filePath: string) => {
      const fileName = path.basename(filePath.toString());
      let size = 1024 * 100; // Default 100KB

      if (fileName.includes('vendor')) {
        size = 1024 * 800; // 800KB for vendor bundle
      } else if (fileName.includes('index')) {
        size = 1024 * 600; // 600KB for main bundle
      } else if (fileName.includes('styles')) {
        size = 1024 * 150; // 150KB for styles
      } else if (fileName.includes('.png') || fileName.includes('.webp')) {
        size = 1024 * 80; // 80KB for images
      }

      return Promise.resolve({ size } as any);
    });

    // Mock fetch for external dependencies
    global.fetch = jest.fn().mockImplementation((url: string) => {
      if (url.includes('maps.googleapis.com')) {
        return Promise.resolve({
          ok: true,
          status: 200,
          statusText: 'OK',
          headers: new Headers(),
        });
      }
      if (url.includes('fonts.googleapis.com')) {
        return Promise.resolve({
          ok: true,
          status: 200,
          statusText: 'OK',
          headers: new Headers(),
        });
      }
      if (url.includes('analytics.example.com')) {
        return Promise.resolve({
          ok: false,
          status: 503,
          statusText: 'Service Unavailable',
          headers: new Headers(),
        });
      }
      return Promise.resolve({
        ok: true,
        status: 200,
        statusText: 'OK',
        headers: new Headers(),
      });
    });
  }

  describe('Complete Workflow Scenarios', () => {
    it('should execute complete verification workflow with mixed results', async () => {
      const report = await runVerification(testOptions);

      // Verify report structure
      expect(report).toBeDefined();
      expect(report.timestamp).toBeInstanceOf(Date);
      expect(report.overallStatus).toMatch(/^(passed|warning|failed)$/);
      expect(typeof report.deploymentReady).toBe('boolean');

      // Verify all sections are populated
      expect(report.buildVerification).toBeDefined();
      expect(report.buildVerification.success).toBe(true);
      expect(report.buildVerification.buildTime).toBeGreaterThan(0);
      expect(report.buildVerification.outputSize.total).toBeGreaterThan(0);

      expect(report.testResults).toBeInstanceOf(Array);
      expect(report.testResults.length).toBeGreaterThan(0);

      expect(report.performanceMetrics).toBeDefined();
      expect(report.performanceMetrics.lcp).toBeGreaterThan(0);
      expect(report.performanceMetrics.lighthouse).toBeDefined();

      expect(report.accessibilityResults).toBeDefined();
      expect(report.accessibilityResults.testedPages).toBeInstanceOf(Array);

      expect(report.pwaValidation).toBeDefined();
      expect(report.dependencyStatus).toBeDefined();
      expect(report.recommendations).toBeInstanceOf(Array);

      // Should have mixed results due to integration test failure
      expect(report.overallStatus).toBe('failed');
      expect(report.deploymentReady).toBe(false);
    });

    it('should handle all stages passing scenario', async () => {
      // Mock all tests to pass
      const mockAllPassingProcess = {
        stdout: {
          on: jest.fn((event, callback) => {
            if (event === 'data') {
              callback(JSON.stringify({
                numTotalTests: 25,
                numPassedTests: 25,
                numFailedTests: 0,
                runTime: 10000,
                success: true,
              }));
            }
          }),
        },
        stderr: { on: jest.fn() },
        on: jest.fn((event, callback) => {
          if (event === 'close') {
            setTimeout(() => callback(0), 30);
          }
        }),
      };

      mockSpawn.mockReturnValue(mockAllPassingProcess as any);

      // Mock analytics service as available
      (global.fetch as jest.Mock).mockImplementation((url: string) => {
        return Promise.resolve({
          ok: true,
          status: 200,
          statusText: 'OK',
          headers: new Headers(),
        });
      });

      const report = await runVerification(testOptions);

      expect(report.overallStatus).toBe('passed');
      expect(report.deploymentReady).toBe(true);
      expect(report.recommendations).toContain(
        expect.stringContaining('ready for deployment')
      );
    });

    it('should handle critical build failure scenario', async () => {
      // Mock build failure
      const mockFailedBuildProcess = {
        stdout: { on: jest.fn() },
        stderr: {
          on: jest.fn((event, callback) => {
            if (event === 'data') {
              callback('ERROR: TypeScript compilation failed\nsrc/App.tsx(15,23): error TS2345: Type error');
            }
          }),
        },
        on: jest.fn((event, callback) => {
          if (event === 'close') {
            setTimeout(() => callback(1), 20); // Failure exit code
          }
        }),
      };

      mockSpawn.mockImplementation((command, args) => {
        if (command === 'npm' && args?.includes('build')) {
          return mockFailedBuildProcess as any;
        }
        return mockFailedBuildProcess as any; // All processes fail
      });

      const report = await runVerification(testOptions);

      expect(report.overallStatus).toBe('failed');
      expect(report.deploymentReady).toBe(false);
      expect(report.buildVerification.success).toBe(false);
      expect(report.buildVerification.errors.length).toBeGreaterThan(0);
      expect(report.recommendations).toContain(
        expect.stringContaining('Fix build issues')
      );
    });

    it('should handle performance threshold violations', async () => {
      // Mock poor performance metrics
      const mockChromium = require('@playwright/test').chromium;
      mockChromium.launch.mockResolvedValue({
        newPage: jest.fn().mockResolvedValue({
          goto: jest.fn(),
          evaluate: jest.fn().mockResolvedValue({
            lcp: 4000, // Exceeds 2500ms threshold
            fid: 150,  // Exceeds 100ms threshold
            cls: 0.15, // Exceeds 0.1 threshold
            fcp: 2500, // Exceeds 1800ms threshold
          }),
          close: jest.fn(),
        }),
        close: jest.fn(),
      });

      // Mock poor Lighthouse scores
      const mockLighthouse = require('lighthouse').default;
      mockLighthouse.mockResolvedValue({
        lhr: {
          categories: {
            performance: { score: 0.75 }, // Below 90 threshold
            accessibility: { score: 0.85 }, // Below expectations
            'best-practices': { score: 0.80 },
            seo: { score: 0.88 },
          },
          audits: {
            'largest-contentful-paint': { numericValue: 4000 },
            'first-input-delay': { numericValue: 150 },
            'cumulative-layout-shift': { numericValue: 0.15 },
            'first-contentful-paint': { numericValue: 2500 },
          },
        },
      });

      const report = await runVerification(testOptions);

      expect(report.performanceMetrics.lcp).toBeGreaterThan(testConfig.performanceThresholds.lcp);
      expect(report.performanceMetrics.fid).toBeGreaterThan(testConfig.performanceThresholds.fid);
      expect(report.performanceMetrics.cls).toBeGreaterThan(testConfig.performanceThresholds.cls);
      expect(report.performanceMetrics.lighthouse.performance).toBeLessThan(
        testConfig.performanceThresholds.lighthousePerformance
      );

      // Should have performance-related recommendations
      expect(report.recommendations).toContain(
        expect.stringContaining('performance')
      );
    });

    it('should handle accessibility violations', async () => {
      // Mock accessibility violations
      const mockAxe = require('axe-core');
      mockAxe.run.mockResolvedValue({
        violations: [
          {
            id: 'color-contrast',
            impact: 'serious',
            description: 'Elements must have sufficient color contrast',
            nodes: [
              {
                target: ['.btn-primary'],
                failureSummary: 'Fix any of the following:\n  Element has insufficient color contrast',
              },
            ],
          },
          {
            id: 'alt-text',
            impact: 'critical',
            description: 'Images must have alternate text',
            nodes: [
              {
                target: ['img.hero-image'],
                failureSummary: 'Fix any of the following:\n  Element does not have an alt attribute',
              },
            ],
          },
        ],
        incomplete: [
          {
            id: 'heading-order',
            impact: 'moderate',
            description: 'Heading levels should only increase by one',
            nodes: [{ target: ['h3.subtitle'] }],
          },
        ],
        passes: [],
      });

      const report = await runVerification(testOptions);

      expect(report.accessibilityResults.compliant).toBe(false);
      expect(report.accessibilityResults.violations.length).toBeGreaterThan(0);
      
      const criticalViolations = report.accessibilityResults.violations.filter(
        v => v.impact === 'critical'
      );
      expect(criticalViolations.length).toBeGreaterThan(0);

      expect(report.recommendations).toContain(
        expect.stringContaining('accessibility')
      );
    });

    it('should handle external dependency failures', async () => {
      // Mock all external dependencies as failing
      (global.fetch as jest.Mock).mockImplementation((url: string) => {
        if (url.includes('maps.googleapis.com')) {
          return Promise.reject(new Error('Network timeout'));
        }
        if (url.includes('fonts.googleapis.com')) {
          return Promise.resolve({
            ok: false,
            status: 404,
            statusText: 'Not Found',
            headers: new Headers(),
          });
        }
        return Promise.resolve({
          ok: false,
          status: 503,
          statusText: 'Service Unavailable',
          headers: new Headers(),
        });
      });

      const report = await runVerification(testOptions);

      expect(report.dependencyStatus.googleMaps.available).toBe(false);
      expect(report.dependencyStatus.cdnResources.some(r => !r.available)).toBe(true);
      expect(report.dependencyStatus.apiEndpoints.some(r => !r.available)).toBe(true);

      // Critical dependency failure should affect deployment readiness
      expect(report.deploymentReady).toBe(false);
    });
  });

  describe('Report Generation Integration', () => {
    it('should generate comprehensive HTML and JSON reports', async () => {
      const report = await runVerification(testOptions);
      
      // Generate reports
      const reportResult = await generateVerificationReport(report, './test-reports');

      expect(mockFs.mkdir).toHaveBeenCalledWith('./test-reports', { recursive: true });
      expect(mockFs.writeFile).toHaveBeenCalledTimes(2); // JSON and HTML

      // Verify JSON report structure
      const jsonWriteCall = mockFs.writeFile.mock.calls.find(call => 
        call[0].toString().includes('.json')
      );
      expect(jsonWriteCall).toBeDefined();
      
      const jsonContent = jsonWriteCall![1] as string;
      const parsedJson = JSON.parse(jsonContent);
      
      expect(parsedJson).toHaveProperty('metadata');
      expect(parsedJson).toHaveProperty('summary');
      expect(parsedJson).toHaveProperty('deploymentDecision');
      expect(parsedJson).toHaveProperty('originalReport');
      expect(parsedJson).toHaveProperty('detailedSections');

      // Verify HTML report structure
      const htmlWriteCall = mockFs.writeFile.mock.calls.find(call => 
        call[0].toString().includes('.html')
      );
      expect(htmlWriteCall).toBeDefined();
      
      const htmlContent = htmlWriteCall![1] as string;
      expect(htmlContent).toContain('<!DOCTYPE html>');
      expect(htmlContent).toContain('Production Deployment Verification Report');
      expect(htmlContent).toContain('deployment-status');
    });

    it('should include detailed deployment decision logic', async () => {
      const report = await runVerification(testOptions);
      const reportResult = await generateVerificationReport(report);

      const jsonWriteCall = mockFs.writeFile.mock.calls.find(call => 
        call[0].toString().includes('.json')
      );
      const jsonContent = jsonWriteCall![1] as string;
      const parsedJson = JSON.parse(jsonContent);

      expect(parsedJson.deploymentDecision).toHaveProperty('ready');
      expect(parsedJson.deploymentDecision).toHaveProperty('confidence');
      expect(parsedJson.deploymentDecision).toHaveProperty('blockers');
      expect(parsedJson.deploymentDecision).toHaveProperty('warnings');
      expect(parsedJson.deploymentDecision).toHaveProperty('recommendations');

      // Verify decision logic
      if (!parsedJson.deploymentDecision.ready) {
        expect(parsedJson.deploymentDecision.blockers.length).toBeGreaterThan(0);
      }
    });

    it('should handle report generation errors gracefully', async () => {
      // Mock file system error
      mockFs.writeFile.mockRejectedValueOnce(new Error('Disk full'));

      const report = await runVerification(testOptions);
      
      // Should not throw, but handle gracefully
      await expect(generateVerificationReport(report)).resolves.toBeDefined();
    });
  });

  describe('Configuration Management Integration', () => {
    it('should validate and use custom configuration', async () => {
      const customConfig: VerificationConfig = {
        ...testConfig,
        performanceThresholds: {
          lcp: 3000, // More lenient
          fid: 150,
          cls: 0.15,
          lighthousePerformance: 85,
        },
        customThresholds: {
          bundleSize: 5 * 1024 * 1024, // 5MB - very lenient
          chunkSize: 1024 * 1024, // 1MB
        },
      };

      const customOptions: PipelineOptions = {
        config: customConfig,
        verbose: false,
      };

      const report = await runVerification(customOptions);

      expect(report).toBeDefined();
      // With more lenient thresholds, should be more likely to pass
    });

    it('should handle invalid configuration gracefully', async () => {
      const invalidConfig = {
        ...testConfig,
        performanceThresholds: {
          lcp: -1000, // Invalid negative value
          fid: 'invalid' as any, // Invalid type
          cls: null as any, // Invalid null value
          lighthousePerformance: 150, // Invalid > 100 value
        },
      };

      const invalidOptions: PipelineOptions = {
        config: invalidConfig,
      };

      // Should handle invalid config without crashing
      const report = await runVerification(invalidOptions);
      expect(report).toBeDefined();
      expect(report.overallStatus).toBeDefined();
    });

    it('should support configuration overrides', async () => {
      const baseConfig = { ...testConfig };
      const overrideOptions: PipelineOptions = {
        config: baseConfig,
        skipStages: ['performance', 'accessibility'],
        verbose: true,
      };

      const report = await runVerification(overrideOptions);

      expect(report).toBeDefined();
      // Performance and accessibility should have default/empty values
      expect(report.performanceMetrics.lcp).toBe(0);
      expect(report.accessibilityResults.testedPages).toHaveLength(0);
    });
  });

  describe('Error Recovery and Resilience', () => {
    it('should recover from transient failures', async () => {
      let buildAttempts = 0;
      
      // Mock unreliable build process
      mockSpawn.mockImplementation((command, args) => {
        if (command === 'npm' && args?.includes('build')) {
          buildAttempts++;
          
          const mockProcess = {
            stdout: { on: jest.fn() },
            stderr: { on: jest.fn() },
            on: jest.fn((event, callback) => {
              if (event === 'close') {
                // Fail first two attempts, succeed on third
                const exitCode = buildAttempts <= 2 ? 1 : 0;
                setTimeout(() => callback(exitCode), 20);
              }
            }),
          };
          
          return mockProcess as any;
        }
        
        // Return successful process for other commands
        return {
          stdout: { on: jest.fn() },
          stderr: { on: jest.fn() },
          on: jest.fn((event, callback) => {
            if (event === 'close') {
              setTimeout(() => callback(0), 10);
            }
          }),
        } as any;
      });

      const report = await runVerification(testOptions);

      expect(report).toBeDefined();
      expect(buildAttempts).toBeGreaterThan(1); // Should have retried
    });

    it('should handle complete system failure gracefully', async () => {
      // Mock all processes to fail
      mockSpawn.mockImplementation(() => {
        throw new Error('System error: Command not found');
      });

      // Mock file system failures
      mockFs.rm.mockRejectedValue(new Error('Permission denied'));
      mockFs.readdir.mockRejectedValue(new Error('Directory not found'));
      mockFs.stat.mockRejectedValue(new Error('File not accessible'));

      const report = await runVerification(testOptions);

      expect(report).toBeDefined();
      expect(report.overallStatus).toBe('failed');
      expect(report.deploymentReady).toBe(false);
      expect(report.recommendations).toContain(
        expect.stringContaining('Pipeline execution failed')
      );
    });
  });
});