/**
 * Unit tests for Production Environment Testing Module
 */

import { jest } from '@jest/globals';

// Mock child_process
jest.mock('child_process', () => ({
  spawn: jest.fn()
}));

import { ProductionEnvironmentTester, DEFAULT_PRODUCTION_CONFIG } from '../production-environment';
import { spawn } from 'child_process';

const mockSpawn = spawn as jest.MockedFunction<typeof spawn>;

// Mock playwright
jest.mock('@playwright/test', () => ({
  chromium: {
    launch: jest.fn()
  },
  firefox: {
    launch: jest.fn()
  },
  webkit: {
    launch: jest.fn()
  }
}));

describe('ProductionEnvironmentTester', () => {
  let tester: ProductionEnvironmentTester;
  let mockBrowser: any;
  let mockPage: any;
  let mockContext: any;

  beforeEach(async () => {
    jest.clearAllMocks();
    
    // Setup mock objects
    mockPage = {
      goto: jest.fn(),
      close: jest.fn(),
      on: jest.fn(),
      locator: jest.fn(),
      setViewportSize: jest.fn(),
      waitForTimeout: jest.fn(),
      waitForLoadState: jest.fn(),
      title: jest.fn(),
      evaluate: jest.fn()
    };

    mockContext = {
      newPage: jest.fn().mockResolvedValue(mockPage),
      newCDPSession: jest.fn(),
      close: jest.fn()
    };

    mockBrowser = {
      newPage: jest.fn().mockResolvedValue(mockPage),
      newContext: jest.fn().mockResolvedValue(mockContext),
      close: jest.fn()
    };

    // Setup playwright mocks
    const { chromium, firefox, webkit } = await import('@playwright/test');
    (chromium.launch as jest.MockedFunction<any>).mockResolvedValue(mockBrowser);
    (firefox.launch as jest.MockedFunction<any>).mockResolvedValue(mockBrowser);
    (webkit.launch as jest.MockedFunction<any>).mockResolvedValue(mockBrowser);
    
    tester = new ProductionEnvironmentTester(DEFAULT_PRODUCTION_CONFIG);
    
    // Setup default mock implementations
    mockPage.locator.mockReturnValue({
      all: jest.fn().mockResolvedValue([]),
      first: jest.fn().mockReturnValue({
        fill: jest.fn(),
        press: jest.fn(),
        click: jest.fn()
      }),
      count: jest.fn().mockResolvedValue(0)
    });
  });

  describe('constructor', () => {
    it('should initialize with provided configuration', () => {
      const customConfig = {
        ...DEFAULT_PRODUCTION_CONFIG,
        previewPort: 5000,
        testTimeout: 60000
      };

      const customTester = new ProductionEnvironmentTester(customConfig);
      expect(customTester).toBeInstanceOf(ProductionEnvironmentTester);
    });
  });

  describe('buildProduction', () => {
    it('should return true when build succeeds', async () => {
      const mockProcess = {
        stderr: { on: jest.fn() },
        on: jest.fn((event, callback) => {
          if (event === 'close') {
            callback(0); // Success exit code
          }
        })
      };

      mockSpawn.mockReturnValue(mockProcess);

      // Access private method for testing
      const result = await (tester as any).buildProduction();
      expect(result).toBe(true);
      expect(mockSpawn).toHaveBeenCalledWith('npm', ['run', 'build'], {
        stdio: ['pipe', 'pipe', 'pipe'],
        shell: true
      });
    });

    it('should return false when build fails', async () => {
      const mockProcess = {
        stderr: { on: jest.fn() },
        on: jest.fn((event, callback) => {
          if (event === 'close') {
            callback(1); // Failure exit code
          }
        })
      };

      mockSpawn.mockReturnValue(mockProcess);

      const result = await (tester as any).buildProduction();
      expect(result).toBe(false);
    });

    it('should handle build process errors', async () => {
      const mockProcess = {
        stderr: { on: jest.fn() },
        on: jest.fn((event, callback) => {
          if (event === 'error') {
            callback(new Error('Process failed'));
          }
        })
      };

      mockSpawn.mockReturnValue(mockProcess);

      const result = await (tester as any).buildProduction();
      expect(result).toBe(false);
    });
  });

  describe('startPreviewServer', () => {
    it('should return true when server starts successfully', async () => {
      const mockProcess = {
        stdout: { on: jest.fn() },
        on: jest.fn()
      };

      mockProcess.stdout.on.mockImplementation((event, callback) => {
        if (event === 'data') {
          // Simulate server ready message
          setTimeout(() => callback('Local: http://localhost:4173'), 100);
        }
      });

      mockSpawn.mockReturnValue(mockProcess);

      const result = await (tester as any).startPreviewServer();
      expect(result).toBe(true);
    });

    it('should return false when server fails to start', async () => {
      const mockProcess = {
        stdout: { on: jest.fn() },
        on: jest.fn((event, callback) => {
          if (event === 'error') {
            callback(new Error('Server failed'));
          }
        })
      };

      mockSpawn.mockReturnValue(mockProcess);

      const result = await (tester as any).startPreviewServer();
      expect(result).toBe(false);
    });

    it('should timeout if server takes too long to start', async () => {
      const mockProcess = {
        stdout: { on: jest.fn() },
        on: jest.fn()
      };

      mockSpawn.mockReturnValue(mockProcess);

      // Mock timer to speed up test
      jest.useFakeTimers();
      
      const resultPromise = (tester as any).startPreviewServer();
      
      // Fast forward past timeout
      jest.advanceTimersByTime(31000);
      
      const result = await resultPromise;
      expect(result).toBe(false);
      
      jest.useRealTimers();
    });
  });

  describe('testPageLoad', () => {
    it('should successfully test page loading', async () => {
      const mockResponse = {
        ok: jest.fn().mockReturnValue(true),
        status: jest.fn().mockReturnValue(200)
      };

      mockPage.goto.mockImplementation(() => {
        // Simulate some loading time
        return new Promise(resolve => setTimeout(() => resolve(mockResponse), 10));
      });
      mockPage.evaluate.mockResolvedValue({
        domContentLoaded: 1000,
        firstContentfulPaint: 1200,
        largestContentfulPaint: 1500,
        resourceCount: 10,
        totalSize: 500000
      });

      const result = await (tester as any).testPageLoad(mockBrowser, 'chromium', '/');

      expect(result.success).toBe(true);
      expect(result.page).toBe('/');
      expect(result.browser).toBe('chromium');
      expect(result.loadTime).toBeGreaterThan(0);
      expect(result.metrics.domContentLoaded).toBe(1000);
    });

    it('should handle page load failures', async () => {
      const mockResponse = {
        ok: jest.fn().mockReturnValue(false),
        status: jest.fn().mockReturnValue(404)
      };

      mockPage.goto.mockResolvedValue(mockResponse);

      const result = await (tester as any).testPageLoad(mockBrowser, 'chromium', '/nonexistent');

      expect(result.success).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
      expect(result.errors[0]).toContain('HTTP 404');
    });

    it('should capture console errors', async () => {
      const mockResponse = {
        ok: jest.fn().mockReturnValue(true),
        status: jest.fn().mockReturnValue(200)
      };

      mockPage.goto.mockResolvedValue(mockResponse);
      mockPage.evaluate.mockResolvedValue({
        domContentLoaded: 1000,
        firstContentfulPaint: 1200,
        largestContentfulPaint: 1500,
        resourceCount: 10,
        totalSize: 500000
      });

      // Simulate console error
      mockPage.on.mockImplementation((event, callback) => {
        if (event === 'console') {
          const mockMsg = {
            type: jest.fn().mockReturnValue('error'),
            text: jest.fn().mockReturnValue('JavaScript error occurred')
          };
          callback(mockMsg);
        }
      });

      const result = await (tester as any).testPageLoad(mockBrowser, 'chromium', '/');

      expect(result.success).toBe(false);
      expect(result.errors).toContain('Console error: JavaScript error occurred');
    });
  });

  describe('testNavigation', () => {
    it('should successfully test navigation', async () => {
      mockPage.goto.mockResolvedValue({});
      mockPage.locator.mockReturnValue({
        all: jest.fn().mockResolvedValue([
          { click: jest.fn() },
          { click: jest.fn() },
          { click: jest.fn() }
        ])
      });
      mockPage.title.mockResolvedValue('Test Page');

      const result = await (tester as any).testNavigation(mockBrowser, 'chromium');

      expect(result.success).toBe(true);
      expect(result.testName).toBe('Navigation');
      expect(result.browser).toBe('chromium');
    });

    it('should handle navigation failures', async () => {
      mockPage.goto.mockRejectedValue(new Error('Navigation failed'));

      const result = await (tester as any).testNavigation(mockBrowser, 'chromium');

      expect(result.success).toBe(false);
      expect(result.error).toContain('Navigation failed');
    });
  });

  describe('testSearch', () => {
    it('should successfully test search functionality', async () => {
      mockPage.goto.mockResolvedValue({});
      
      // Mock search input found and results found
      mockPage.locator.mockImplementation((selector) => {
        if (selector.includes('input')) {
          // First call - looking for search input
          const mockSearchInput = {
            fill: jest.fn(),
            press: jest.fn(),
            count: jest.fn().mockResolvedValue(1)
          };
          return {
            first: jest.fn().mockReturnValue(mockSearchInput),
            count: jest.fn().mockResolvedValue(1)
          };
        } else {
          // Second call - looking for search results
          return {
            count: jest.fn().mockResolvedValue(1)
          };
        }
      });

      const result = await (tester as any).testSearch(mockBrowser, 'chromium');

      expect(result.success).toBe(true);
      expect(result.testName).toBe('Search');
    });

    it('should handle missing search functionality', async () => {
      mockPage.goto.mockResolvedValue({});
      mockPage.locator.mockImplementation((selector) => {
        if (selector.includes('input')) {
          const mockSearchInput = {
            fill: jest.fn(),
            press: jest.fn(),
            count: jest.fn().mockResolvedValue(0)
          };
          return {
            first: jest.fn().mockReturnValue(mockSearchInput),
            count: jest.fn().mockResolvedValue(0)
          };
        } else {
          return {
            count: jest.fn().mockResolvedValue(0)
          };
        }
      });

      const result = await (tester as any).testSearch(mockBrowser, 'chromium');

      expect(result.success).toBe(true); // Should pass even without search
    });
  });

  describe('testResponsiveDesign', () => {
    it('should successfully test responsive design', async () => {
      mockPage.goto.mockResolvedValue({});
      mockPage.evaluate.mockResolvedValue(1000); // Body height

      const result = await (tester as any).testResponsiveDesign(mockBrowser, 'chromium');

      expect(result.success).toBe(true);
      expect(result.testName).toBe('Responsive Design');
      expect(mockPage.setViewportSize).toHaveBeenCalledTimes(3);
    });

    it('should detect responsive design issues', async () => {
      mockPage.goto.mockResolvedValue({});
      mockPage.evaluate.mockResolvedValue(50); // Very small body height

      const result = await (tester as any).testResponsiveDesign(mockBrowser, 'chromium');

      expect(result.success).toBe(false);
      expect(result.error).toContain('Content not properly rendered');
    });
  });

  describe('testPWAFeatures', () => {
    it('should successfully test PWA features', async () => {
      mockPage.goto.mockResolvedValue({});
      mockPage.evaluate.mockResolvedValue(true); // Service worker supported
      mockPage.locator.mockReturnValue({
        count: jest.fn().mockResolvedValue(1) // Manifest found
      });

      const result = await (tester as any).testPWAFeatures(mockBrowser, 'chromium');

      expect(result.success).toBe(true);
      expect(result.testName).toBe('PWA Features');
    });

    it('should detect missing PWA features', async () => {
      mockPage.goto.mockResolvedValue({});
      mockPage.evaluate.mockResolvedValue(false); // No service worker

      const result = await (tester as any).testPWAFeatures(mockBrowser, 'chromium');

      expect(result.success).toBe(false);
      expect(result.error).toContain('Service Worker not supported');
    });
  });

  describe('evaluateOverallSuccess', () => {
    it('should return false if build failed', () => {
      const result = {
        buildSuccess: false,
        serverStarted: true,
        pageLoadTests: [],
        functionalityTests: [],
        networkConditionTests: [],
        overallSuccess: false,
        errors: [],
        warnings: []
      };

      const success = (tester as any).evaluateOverallSuccess(result);
      expect(success).toBe(false);
    });

    it('should return false if server failed to start', () => {
      const result = {
        buildSuccess: true,
        serverStarted: false,
        pageLoadTests: [],
        functionalityTests: [],
        networkConditionTests: [],
        overallSuccess: false,
        errors: [],
        warnings: []
      };

      const success = (tester as any).evaluateOverallSuccess(result);
      expect(success).toBe(false);
    });

    it('should return true if critical tests pass', () => {
      const result = {
        buildSuccess: true,
        serverStarted: true,
        pageLoadTests: [{ success: true }],
        functionalityTests: [{ success: true }],
        networkConditionTests: [{ overallSuccess: true }],
        overallSuccess: false,
        errors: [],
        warnings: []
      };

      const success = (tester as any).evaluateOverallSuccess(result);
      expect(success).toBe(true);
    });

    it('should add warnings for test failures', () => {
      const result = {
        buildSuccess: true,
        serverStarted: true,
        pageLoadTests: [{ success: false }],
        functionalityTests: [{ success: false }],
        networkConditionTests: [{ overallSuccess: false }],
        overallSuccess: false,
        errors: [],
        warnings: []
      };

      (tester as any).evaluateOverallSuccess(result);
      
      expect(result.warnings.length).toBe(3);
      expect(result.warnings[0]).toContain('page load tests failed');
      expect(result.warnings[1]).toContain('functionality tests failed');
      expect(result.warnings[2]).toContain('network condition tests failed');
    });
  });

  describe('generateReport', () => {
    it('should generate comprehensive report', () => {
      const result = {
        buildSuccess: true,
        serverStarted: true,
        pageLoadTests: [
          {
            page: '/',
            browser: 'chromium',
            loadTime: 1500,
            success: true,
            errors: [],
            metrics: {
              domContentLoaded: 1000,
              firstContentfulPaint: 1200,
              largestContentfulPaint: 1500,
              resourceCount: 10,
              totalSize: 500000
            }
          }
        ],
        functionalityTests: [
          {
            testName: 'Navigation',
            browser: 'chromium',
            success: true,
            duration: 2000
          }
        ],
        networkConditionTests: [
          {
            condition: 'Fast 3G',
            browser: 'chromium',
            pages: [
              {
                page: '/',
                loadTime: 3000,
                success: true
              }
            ],
            overallSuccess: true
          }
        ],
        overallSuccess: true,
        errors: [],
        warnings: []
      };

      const report = tester.generateReport(result);

      expect(report).toContain('Production Environment Test Report');
      expect(report).toContain('Overall Success: PASS');
      expect(report).toContain('Build Success: PASS');
      expect(report).toContain('Server Started: PASS');
      expect(report).toContain('Page Load Tests:');
      expect(report).toContain('Functionality Tests:');
      expect(report).toContain('Network Condition Tests:');
    });

    it('should include errors and warnings in report', () => {
      const result = {
        buildSuccess: false,
        serverStarted: false,
        pageLoadTests: [],
        functionalityTests: [],
        networkConditionTests: [],
        overallSuccess: false,
        errors: ['Build failed', 'Server failed'],
        warnings: ['Performance warning']
      };

      const report = tester.generateReport(result);

      expect(report).toContain('Overall Success: FAIL');
      expect(report).toContain('Errors:');
      expect(report).toContain('- Build failed');
      expect(report).toContain('- Server failed');
      expect(report).toContain('Warnings:');
      expect(report).toContain('- Performance warning');
    });
  });
});