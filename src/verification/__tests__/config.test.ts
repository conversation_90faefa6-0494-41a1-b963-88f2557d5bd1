/**
 * Tests for the configuration system
 */

import { ConfigManager, ConfigurationOptions, CONFIG_PRESETS } from '../config';
import { VerificationConfig, TestSuiteConfig } from '../types';
import { ConfigTemplateGenerator, ConfigValidator, ConfigComparison } from '../config-utils';
import * as fs from 'fs/promises';
import * as path from 'path';

// Mock fs for testing
jest.mock('fs/promises');
const mockFs = fs as jest.Mocked<typeof fs>;

describe('ConfigManager', () => {
  let configManager: ConfigManager;
  
  beforeEach(() => {
    configManager = new ConfigManager();
    jest.clearAllMocks();
  });

  describe('Basic Configuration', () => {
    it('should create a config manager with default configuration', () => {
      const config = configManager.getConfig();
      
      expect(config.buildSettings.mode).toBe('production');
      expect(config.performanceThresholds.lcp).toBe(2500);
      expect(config.accessibilityLevel).toBe('AA');
      expect(config.testSuites.length).toBeGreaterThan(0);
    });

    it('should merge custom configuration with defaults', () => {
      const customConfig: Partial<VerificationConfig> = {
        performanceThresholds: {
          lcp: 3000,
          fid: 150,
          cls: 0.15,
          lighthousePerformance: 85,
        },
      };
      
      const manager = new ConfigManager(customConfig);
      const config = manager.getConfig();
      
      expect(config.performanceThresholds.lcp).toBe(3000);
      expect(config.performanceThresholds.fid).toBe(150);
      expect(config.buildSettings.mode).toBe('production'); // Should keep default
    });
  });

  describe('Environment Configuration', () => {
    const mockConfigFile = {
      default: {
        buildSettings: {
          mode: 'production' as const,
          sourceMaps: false,
          minification: true,
        },
        performanceThresholds: {
          lcp: 2500,
          fid: 100,
          cls: 0.1,
          lighthousePerformance: 90,
        },
        accessibilityLevel: 'AA' as const,
        testSuites: [
          {
            name: 'unit-tests',
            type: 'unit' as const,
            enabled: true,
            timeout: 30000,
            retries: 2,
          },
        ],
        externalDependencies: [],
      },
      environments: {
        development: {
          name: 'development',
          overrides: {
            buildSettings: {
              mode: 'development' as const,
              sourceMaps: true,
            },
            performanceThresholds: {
              lcp: 4000,
            },
          },
        },
        staging: {
          name: 'staging',
          extends: 'development',
          overrides: {
            performanceThresholds: {
              lcp: 3000,
            },
          },
        },
      },
    };

    it('should load environment-specific configuration', async () => {
      const manager = new ConfigManager();
      await manager.loadConfigFile(mockConfigFile, 'development');
      
      const config = manager.getConfig();
      
      expect(config.buildSettings.mode).toBe('development');
      expect(config.buildSettings.sourceMaps).toBe(true);
      expect(config.performanceThresholds.lcp).toBe(4000);
      expect(config.performanceThresholds.fid).toBe(100); // Should keep default
    });

    it('should handle environment inheritance', async () => {
      const manager = new ConfigManager();
      await manager.loadConfigFile(mockConfigFile, 'staging');
      
      const config = manager.getConfig();
      
      // Should inherit from development
      expect(config.buildSettings.mode).toBe('development');
      expect(config.buildSettings.sourceMaps).toBe(true);
      
      // Should override with staging values
      expect(config.performanceThresholds.lcp).toBe(3000);
    });

    it('should list available environments', async () => {
      const manager = new ConfigManager();
      await manager.loadConfigFile(mockConfigFile);
      
      const environments = manager.getAvailableEnvironments();
      
      expect(environments).toContain('default');
      expect(environments).toContain('development');
      expect(environments).toContain('staging');
    });

    it('should switch environments', async () => {
      const manager = new ConfigManager();
      await manager.loadConfigFile(mockConfigFile, 'development');
      
      expect(manager.getCurrentEnvironment()).toBe('development');
      
      await manager.switchEnvironment('staging');
      
      expect(manager.getCurrentEnvironment()).toBe('staging');
      const config = manager.getConfig();
      expect(config.performanceThresholds.lcp).toBe(3000);
    });
  });

  describe('Configuration Options', () => {
    it('should apply test suite filtering', () => {
      const options: ConfigurationOptions = {
        testSuiteFilter: {
          include: ['unit-tests'],
          exclude: ['e2e-tests'],
          types: ['unit', 'integration'],
        },
      };
      
      const manager = new ConfigManager(undefined, options);
      const config = manager.getConfig();
      
      const enabledSuites = config.testSuites.filter(suite => suite.enabled);
      const suiteNames = enabledSuites.map(suite => suite.name);
      
      expect(suiteNames).toContain('unit-tests');
      expect(suiteNames).not.toContain('e2e-tests');
    });

    it('should apply performance threshold overrides', () => {
      const options: ConfigurationOptions = {
        performanceOverrides: {
          lcp: 3500,
          fid: 200,
        },
      };
      
      const manager = new ConfigManager(undefined, options);
      const config = manager.getConfig();
      
      expect(config.performanceThresholds.lcp).toBe(3500);
      expect(config.performanceThresholds.fid).toBe(200);
      expect(config.performanceThresholds.cls).toBe(0.1); // Should keep default
    });

    it('should apply accessibility overrides', () => {
      const options: ConfigurationOptions = {
        accessibilityOverrides: {
          level: 'AAA',
        },
      };
      
      const manager = new ConfigManager(undefined, options);
      const config = manager.getConfig();
      
      expect(config.accessibilityLevel).toBe('AAA');
    });

    it('should apply custom thresholds', () => {
      const options: ConfigurationOptions = {
        customThresholds: {
          'performance.lcp': 2000,
          'performance.fid': 50,
        },
      };
      
      const manager = new ConfigManager(undefined, options);
      const config = manager.getConfig();
      
      expect(config.performanceThresholds.lcp).toBe(2000);
      expect(config.performanceThresholds.fid).toBe(50);
    });
  });

  describe('Configuration Management', () => {
    it('should update performance thresholds', () => {
      configManager.updatePerformanceThresholds({
        lcp: 3000,
        fid: 150,
      });
      
      const config = configManager.getConfig();
      
      expect(config.performanceThresholds.lcp).toBe(3000);
      expect(config.performanceThresholds.fid).toBe(150);
      expect(config.performanceThresholds.cls).toBe(0.1); // Should keep existing
    });

    it('should update accessibility settings', () => {
      configManager.updateAccessibilitySettings({ level: 'AAA' });
      
      const config = configManager.getConfig();
      
      expect(config.accessibilityLevel).toBe('AAA');
    });

    it('should update test suite configuration', () => {
      configManager.updateTestSuite('unit-tests', {
        timeout: 45000,
        retries: 3,
      });
      
      const config = configManager.getConfig();
      const unitSuite = config.testSuites.find(suite => suite.name === 'unit-tests');
      
      expect(unitSuite?.timeout).toBe(45000);
      expect(unitSuite?.retries).toBe(3);
    });

    it('should add new test suite', () => {
      configManager.updateTestSuite('custom-tests', {
        type: 'integration',
        enabled: true,
        timeout: 60000,
        retries: 1,
      });
      
      const config = configManager.getConfig();
      const customSuite = config.testSuites.find(suite => suite.name === 'custom-tests');
      
      expect(customSuite).toBeDefined();
      expect(customSuite?.type).toBe('integration');
    });

    it('should remove test suite', () => {
      const initialCount = configManager.getConfig().testSuites.length;
      
      configManager.removeTestSuite('unit-tests');
      
      const config = configManager.getConfig();
      const unitSuite = config.testSuites.find(suite => suite.name === 'unit-tests');
      
      expect(unitSuite).toBeUndefined();
      expect(config.testSuites.length).toBe(initialCount - 1);
    });

    it('should toggle test suites by type', () => {
      configManager.toggleTestSuitesByType('e2e', false);
      
      const config = configManager.getConfig();
      const e2eSuites = config.testSuites.filter(suite => suite.type === 'e2e');
      
      e2eSuites.forEach(suite => {
        expect(suite.enabled).toBe(false);
      });
    });
  });

  describe('Configuration Validation', () => {
    it('should validate valid configuration', () => {
      const validation = configManager.validateConfig();
      
      expect(validation.valid).toBe(true);
      expect(validation.errors).toHaveLength(0);
    });

    it('should detect invalid performance thresholds', () => {
      configManager.updatePerformanceThresholds({
        lcp: -100, // Invalid
        cls: 2, // Invalid (should be 0-1)
      });
      
      const validation = configManager.validateConfig();
      
      expect(validation.valid).toBe(false);
      expect(validation.errors).toContain('LCP threshold must be positive');
      expect(validation.errors).toContain('CLS threshold must be between 0 and 1');
    });

    it('should detect invalid test suite configuration', () => {
      configManager.updateTestSuite('invalid-suite', {
        name: '', // Invalid
        timeout: -1000, // Invalid
        retries: -1, // Invalid
      });
      
      const validation = configManager.validateConfig();
      
      expect(validation.valid).toBe(false);
      expect(validation.errors.some(error => error.includes('timeout must be positive'))).toBe(true);
      expect(validation.errors.some(error => error.includes('retries must be non-negative'))).toBe(true);
    });
  });

  describe('Configuration Summary', () => {
    it('should provide configuration summary', () => {
      const summary = configManager.getConfigSummary();
      
      expect(summary).toHaveProperty('enabledTestSuites');
      expect(summary).toHaveProperty('totalTestSuites');
      expect(summary).toHaveProperty('performanceThresholds');
      expect(summary).toHaveProperty('accessibilityLevel');
      expect(summary).toHaveProperty('criticalDependencies');
      
      expect(typeof summary.enabledTestSuites).toBe('number');
      expect(typeof summary.totalTestSuites).toBe('number');
      expect(summary.accessibilityLevel).toBe('AA');
    });
  });

  describe('File Operations', () => {
    it('should load configuration from file', async () => {
      const mockConfig = {
        default: {
          buildSettings: { mode: 'development', sourceMaps: true, minification: false },
          performanceThresholds: { lcp: 4000, fid: 200, cls: 0.2, lighthousePerformance: 70 },
          accessibilityLevel: 'AA',
          testSuites: [],
          externalDependencies: [],
        },
      };
      
      mockFs.readFile.mockResolvedValue(JSON.stringify(mockConfig));
      
      const manager = await ConfigManager.loadFromFile('test-config.json');
      const config = manager.getConfig();
      
      expect(config.buildSettings.mode).toBe('development');
      expect(config.performanceThresholds.lcp).toBe(4000);
    });

    it('should handle file loading errors gracefully', async () => {
      mockFs.readFile.mockRejectedValue(new Error('File not found'));
      
      const manager = await ConfigManager.loadFromFile('nonexistent.json');
      const config = manager.getConfig();
      
      // Should fall back to defaults
      expect(config.buildSettings.mode).toBe('production');
    });

    it('should save configuration to file', async () => {
      mockFs.mkdir.mockResolvedValue(undefined);
      mockFs.writeFile.mockResolvedValue(undefined);
      
      await configManager.saveToFile('output-config.json');
      
      expect(mockFs.writeFile).toHaveBeenCalledWith(
        'output-config.json',
        expect.stringContaining('"buildSettings"')
      );
    });
  });
});

describe('ConfigTemplateGenerator', () => {
  it('should generate basic template', () => {
    const template = ConfigTemplateGenerator.generateBasicTemplate();
    
    expect(template.buildSettings).toBeDefined();
    expect(template.performanceThresholds).toBeDefined();
    expect(template.testSuites.length).toBeGreaterThan(0);
  });

  it('should generate environment-specific config', () => {
    const config = ConfigTemplateGenerator.generateEnvironmentConfig('development');
    
    expect(config.default).toBeDefined();
    expect(config.environments.development).toBeDefined();
    expect(config.environments.development.overrides).toBeDefined();
  });

  it('should generate comprehensive config', () => {
    const config = ConfigTemplateGenerator.generateComprehensiveConfig();
    
    expect(config.default).toBeDefined();
    expect(config.environments).toBeDefined();
    expect(config.environments.development).toBeDefined();
    expect(config.environments.staging).toBeDefined();
    expect(config.environments.ci).toBeDefined();
    expect(config.environments.production).toBeDefined();
  });
});

describe('ConfigValidator', () => {
  it('should validate configuration with suggestions', async () => {
    const mockConfig = {
      buildSettings: { mode: 'production', sourceMaps: false, minification: true },
      performanceThresholds: { lcp: 4000, fid: 400, cls: 0.3, lighthousePerformance: 60 },
      accessibilityLevel: 'AA',
      testSuites: [],
      externalDependencies: [],
    };
    
    mockFs.readFile.mockResolvedValue(JSON.stringify({ default: mockConfig }));
    
    const result = await ConfigValidator.validateWithSuggestions('test-config.json');
    
    expect(result.warnings.length).toBeGreaterThan(0);
    expect(result.suggestions.length).toBeGreaterThan(0);
    expect(result.warnings.some(w => w.includes('LCP threshold is quite high'))).toBe(true);
  });

  it('should generate performance recommendations', () => {
    const thresholds = {
      lcp: 3000,
      fid: 200,
      cls: 0.2,
      lighthousePerformance: 80,
    };
    
    const recommendations = ConfigValidator.generatePerformanceRecommendations(thresholds);
    
    expect(recommendations.length).toBeGreaterThan(0);
    expect(recommendations.some(r => r.includes('LCP'))).toBe(true);
    expect(recommendations.some(r => r.includes('FID'))).toBe(true);
  });
});

describe('ConfigComparison', () => {
  it('should compare configurations and find differences', () => {
    const config1: VerificationConfig = {
      buildSettings: { mode: 'production', sourceMaps: false, minification: true },
      performanceThresholds: { lcp: 2500, fid: 100, cls: 0.1, lighthousePerformance: 90 },
      accessibilityLevel: 'AA',
      testSuites: [
        { name: 'unit', type: 'unit', enabled: true, timeout: 30000, retries: 2 },
      ],
      externalDependencies: [],
    };
    
    const config2: VerificationConfig = {
      buildSettings: { mode: 'production', sourceMaps: false, minification: true },
      performanceThresholds: { lcp: 3000, fid: 100, cls: 0.1, lighthousePerformance: 90 },
      accessibilityLevel: 'AA',
      testSuites: [
        { name: 'unit', type: 'unit', enabled: true, timeout: 30000, retries: 2 },
        { name: 'e2e', type: 'e2e', enabled: true, timeout: 60000, retries: 1 },
      ],
      externalDependencies: [],
    };
    
    const comparison = ConfigComparison.compareConfigs(config1, config2);
    
    expect(comparison.differences.length).toBeGreaterThan(0);
    expect(comparison.summary.performanceChanges).toBe(1);
    expect(comparison.summary.testSuiteChanges).toBe(1);
  });
});