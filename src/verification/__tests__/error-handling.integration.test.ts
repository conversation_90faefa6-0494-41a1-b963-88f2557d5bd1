/**
 * Integration tests for error handling system with the verification pipeline
 */

import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';
import { ErrorHandler, createErrorHandler } from '../error-handling';
import { VerificationConfig, PipelineOptions } from '../types';

describe('Error Handling Integration Tests', () => {
  let errorHandler: ErrorHandler;

  beforeEach(() => {
    errorHandler = createErrorHandler(
      { enableConsole: false, enableFile: false },
      { maxRetries: 1, baseDelay: 10 }
    );
  });

  afterEach(async () => {
    await errorHandler.cleanup();
  });

  it('should create and handle errors properly', async () => {
    // Test error creation
    const error = errorHandler.createError(
      'test',
      'test',
      'error',
      'Test error message'
    );

    expect(error.stage).toBe('test');
    expect(error.type).toBe('test');
    expect(error.message).toBe('Test error message');

    // Test error handling
    const result = await errorHandler.handleError(error);
    expect(result.success).toBe(false);
    expect(result.recoveryActions.length).toBeGreaterThan(0);
  });

  it('should collect and provide error statistics', () => {
    // Create various types of errors
    errorHandler.createError('build', 'build', 'error', 'Build failed');
    errorHandler.createError('test', 'test', 'warning', 'Test warning');
    errorHandler.createError('performance', 'performance', 'error', 'Performance issue');
    errorHandler.createError('build', 'build', 'critical', 'Critical build error');

    const stats = errorHandler.getErrorStatistics();

    expect(stats.totalErrors).toBe(4);
    expect(stats.errorsByStage).toEqual({
      build: 2,
      test: 1,
      performance: 1
    });
    expect(stats.errorsByType).toEqual({
      build: 2,
      test: 1,
      performance: 1
    });
    expect(stats.errorsBySeverity).toEqual({
      error: 2,
      warning: 1,
      critical: 1
    });
  });

  it('should determine error recoverability based on error patterns', () => {
    const recoverableErrors = [
      'Network timeout occurred',
      'ECONNRESET connection reset',
      'Browser launch failed temporarily',
      'Intermittent test failure'
    ];

    const nonRecoverableErrors = [
      'Syntax error in source code',
      'Configuration error detected',
      'Permission denied access',
      'File not found error'
    ];

    recoverableErrors.forEach(message => {
      const error = errorHandler.createError('test', 'test', 'error', message);
      expect(error.recoverable).toBe(true);
    });

    nonRecoverableErrors.forEach(message => {
      const error = errorHandler.createError('test', 'test', 'error', message);
      expect(error.recoverable).toBe(false);
    });
  });

  it('should set appropriate retry limits based on error type and severity', () => {
    const testCases = [
      { type: 'build' as const, severity: 'critical' as const, expectedRetries: 0 },
      { type: 'build' as const, severity: 'error' as const, expectedRetries: 1 },
      { type: 'test' as const, severity: 'error' as const, expectedRetries: 3 },
      { type: 'dependency' as const, severity: 'error' as const, expectedRetries: 5 },
      { type: 'performance' as const, severity: 'warning' as const, expectedRetries: 2 }
    ];

    testCases.forEach(({ type, severity, expectedRetries }) => {
      const error = errorHandler.createError('test-stage', type, severity, 'Test error');
      expect(error.maxRetries).toBe(expectedRetries);
    });
  });
});