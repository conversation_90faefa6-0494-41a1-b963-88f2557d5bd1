/**
 * Integration tests for PWA validation module
 * Tests the PWA validation against a real application instance
 */

import { describe, it, expect, beforeAll, afterAll } from '@jest/globals';
import { spawn, ChildProcess } from 'child_process';
import { PWAValidator, PWATestSuite, validatePWA } from '../pwa';
import { PWAValidationResult } from '../types';

describe('PWA Validation Integration Tests', () => {
  let serverProcess: ChildProcess;
  const testPort = 5174;
  const baseUrl = `http://localhost:${testPort}`;

  beforeAll(async () => {
    // Start the development server for testing
    serverProcess = spawn('npm', ['run', 'preview', '--', '--port', testPort.toString()], {
      stdio: 'pipe',
      detached: false
    });

    // Wait for server to start
    await new Promise<void>((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('Server failed to start within timeout'));
      }, 30000);

      const checkServer = async () => {
        try {
          const response = await fetch(baseUrl);
          if (response.ok) {
            clearTimeout(timeout);
            resolve();
          } else {
            setTimeout(checkServer, 1000);
          }
        } catch (error) {
          setTimeout(checkServer, 1000);
        }
      };

      checkServer();
    });
  }, 60000);

  afterAll(async () => {
    if (serverProcess) {
      serverProcess.kill('SIGTERM');
      
      // Wait for process to exit
      await new Promise<void>((resolve) => {
        serverProcess.on('exit', () => resolve());
        setTimeout(() => {
          serverProcess.kill('SIGKILL');
          resolve();
        }, 5000);
      });
    }
  });

  describe('Real PWA Validation', () => {
    it('should validate service worker registration in real application', async () => {
      const validator = new PWAValidator({
        baseUrl,
        manifestPath: '/manifest.json',
        serviceWorkerPath: '/sw.js',
        timeout: 30000,
        offlinePages: ['/', '/teamsales'],
        criticalResources: ['/Icebox.webp', '/manifest.json']
      });

      const result = await validator.validate();

      expect(result.serviceWorkerRegistered).toBe(true);
    }, 45000);

    it('should validate PWA manifest in real application', async () => {
      const validator = new PWAValidator({
        baseUrl,
        manifestPath: '/manifest.json',
        serviceWorkerPath: '/sw.js',
        timeout: 30000,
        offlinePages: ['/', '/teamsales'],
        criticalResources: ['/Icebox.webp', '/manifest.json']
      });

      const result = await validator.validate();

      expect(result.manifestValid).toBe(true);
    }, 30000);

    it('should test offline functionality in real application', async () => {
      const validator = new PWAValidator({
        baseUrl,
        manifestPath: '/manifest.json',
        serviceWorkerPath: '/sw.js',
        timeout: 30000,
        offlinePages: ['/', '/teamsales'],
        criticalResources: ['/Icebox.webp', '/manifest.json']
      });

      const result = await validator.validate();

      // Note: Offline functionality might not work perfectly in test environment
      // but we should at least test that the validation runs without errors
      expect(typeof result.offlineFunctionality).toBe('boolean');
    }, 45000);

    it('should test PWA installability in real application', async () => {
      const validator = new PWAValidator({
        baseUrl,
        manifestPath: '/manifest.json',
        serviceWorkerPath: '/sw.js',
        timeout: 30000,
        offlinePages: ['/', '/teamsales'],
        criticalResources: ['/Icebox.webp', '/manifest.json']
      });

      const result = await validator.validate();

      // Installability depends on various factors including HTTPS
      // In localhost environment, it should be installable
      expect(typeof result.installable).toBe('boolean');
    }, 30000);

    it('should validate cache strategy in real application', async () => {
      const validator = new PWAValidator({
        baseUrl,
        manifestPath: '/manifest.json',
        serviceWorkerPath: '/sw.js',
        timeout: 30000,
        offlinePages: ['/', '/teamsales'],
        criticalResources: ['/Icebox.webp', '/manifest.json']
      });

      const result = await validator.validate();

      expect(result.cacheStrategy).toBeDefined();
      expect(typeof result.cacheStrategy.staticAssetsCache).toBe('boolean');
      expect(typeof result.cacheStrategy.apiResponseCache).toBe('boolean');
      expect(Array.isArray(result.cacheStrategy.offlinePages)).toBe(true);
    }, 30000);

    it('should run complete PWA validation suite', async () => {
      const result = await validatePWA({
        baseUrl,
        manifestPath: '/manifest.json',
        serviceWorkerPath: '/sw.js',
        timeout: 30000,
        offlinePages: ['/', '/teamsales'],
        criticalResources: ['/Icebox.webp', '/manifest.json']
      });

      expect(result).toBeDefined();
      expect(typeof result.serviceWorkerRegistered).toBe('boolean');
      expect(typeof result.manifestValid).toBe('boolean');
      expect(typeof result.offlineFunctionality).toBe('boolean');
      expect(typeof result.installable).toBe('boolean');
      expect(result.cacheStrategy).toBeDefined();
    }, 60000);
  });

  describe('PWA Test Suite Integration', () => {
    it('should execute PWA test suite against real application', async () => {
      // Override the default config for the test suite
      const testSuite = new PWATestSuite();
      
      // We need to modify the internal config - this is a bit hacky but necessary for testing
      (testSuite as any).config = {
        baseUrl,
        manifestPath: '/manifest.json',
        serviceWorkerPath: '/sw.js',
        timeout: 30000,
        offlinePages: ['/', '/teamsales'],
        criticalResources: ['/Icebox.webp', '/manifest.json']
      };
      
      (testSuite as any).validator = new PWAValidator((testSuite as any).config);

      const result = await testSuite.execute();

      expect(result).toBeDefined();
      expect(typeof result.passed).toBe('boolean');
      expect(result.testCount).toBe(5);
      expect(result.duration).toBeGreaterThan(0);
      expect(Array.isArray(result.failures)).toBe(true);
    }, 60000);

    it('should provide detailed failure information when PWA validation fails', async () => {
      // Test with invalid configuration to trigger failures
      const validator = new PWAValidator({
        baseUrl,
        manifestPath: '/nonexistent-manifest.json', // This should fail
        serviceWorkerPath: '/nonexistent-sw.js', // This should fail
        timeout: 30000,
        offlinePages: ['/', '/teamsales'],
        criticalResources: ['/nonexistent.png']
      });

      const result = await validator.validate();

      // At least manifest validation should fail
      expect(result.manifestValid).toBe(false);
    }, 30000);
  });

  describe('Error Handling Integration', () => {
    it('should handle network errors gracefully', async () => {
      const validator = new PWAValidator({
        baseUrl: 'http://localhost:99999', // Invalid port
        manifestPath: '/manifest.json',
        serviceWorkerPath: '/sw.js',
        timeout: 5000,
        offlinePages: ['/'],
        criticalResources: []
      });

      const result = await validator.validate();

      expect(result.serviceWorkerRegistered).toBe(false);
      expect(result.manifestValid).toBe(false);
      expect(result.offlineFunctionality).toBe(false);
      expect(result.installable).toBe(false);
    }, 15000);

    it('should handle timeout errors gracefully', async () => {
      const validator = new PWAValidator({
        baseUrl,
        manifestPath: '/manifest.json',
        serviceWorkerPath: '/sw.js',
        timeout: 1, // Very short timeout to trigger timeout errors
        offlinePages: ['/'],
        criticalResources: []
      });

      const result = await validator.validate();

      // Should complete without throwing errors, even with timeouts
      expect(result).toBeDefined();
    }, 10000);
  });

  describe('Real Manifest Validation', () => {
    it('should validate the actual manifest.json file', async () => {
      const response = await fetch(`${baseUrl}/manifest.json`);
      expect(response.ok).toBe(true);

      const manifest = await response.json();
      
      // Validate required fields
      expect(manifest.name || manifest.short_name).toBeTruthy();
      expect(manifest.start_url).toBeTruthy();
      expect(manifest.display).toBeTruthy();
      expect(Array.isArray(manifest.icons)).toBe(true);
      expect(manifest.icons.length).toBeGreaterThan(0);

      // Validate icon sizes
      const hasLargeIcon = manifest.icons.some((icon: any) => {
        if (!icon.sizes) return false;
        const sizes = icon.sizes.split('x');
        return sizes.length === 2 && parseInt(sizes[0]) >= 192;
      });
      expect(hasLargeIcon).toBe(true);
    });

    it('should validate the actual service worker file', async () => {
      const response = await fetch(`${baseUrl}/sw.js`);
      expect(response.ok).toBe(true);

      const serviceWorkerContent = await response.text();
      
      // Basic validation that it's a service worker
      expect(serviceWorkerContent).toContain('addEventListener');
      expect(serviceWorkerContent).toContain('install');
      expect(serviceWorkerContent).toContain('fetch');
    });
  });

  describe('Performance and Reliability', () => {
    it('should complete PWA validation within reasonable time', async () => {
      const startTime = Date.now();
      
      await validatePWA({
        baseUrl,
        manifestPath: '/manifest.json',
        serviceWorkerPath: '/sw.js',
        timeout: 30000,
        offlinePages: ['/'],
        criticalResources: ['/Icebox.webp']
      });

      const duration = Date.now() - startTime;
      
      // Should complete within 45 seconds
      expect(duration).toBeLessThan(45000);
    }, 50000);

    it('should be reliable across multiple runs', async () => {
      const results: PWAValidationResult[] = [];
      
      // Run validation multiple times
      for (let i = 0; i < 3; i++) {
        const result = await validatePWA({
          baseUrl,
          manifestPath: '/manifest.json',
          serviceWorkerPath: '/sw.js',
          timeout: 20000,
          offlinePages: ['/'],
          criticalResources: ['/Icebox.webp']
        });
        results.push(result);
      }

      // Results should be consistent
      const firstResult = results[0];
      results.forEach(result => {
        expect(result.serviceWorkerRegistered).toBe(firstResult.serviceWorkerRegistered);
        expect(result.manifestValid).toBe(firstResult.manifestValid);
      });
    }, 90000);
  });
});