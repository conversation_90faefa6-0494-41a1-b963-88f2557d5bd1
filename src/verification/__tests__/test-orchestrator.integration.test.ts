/**
 * Integration tests for Test Orchestrator Framework
 * Tests the actual Jest and Playwright test execution
 */

import { TestOrchestrator, TestOrchestratorConfig } from '../test-orchestrator';
import { TestConfigManager } from '../test-config';
import { promises as fs } from 'fs';
import path from 'path';

describe('Test Orchestrator Integration', () => {
  let orchestrator: TestOrchestrator;
  let testConfig: TestOrchestratorConfig;

  beforeAll(async () => {
    // Load configuration for integration testing
    testConfig = await TestConfigManager.loadConfig({
      environment: 'test',
      overrides: {
        // Use shorter timeouts for integration tests
        timeout: 120000, // 2 minutes
        testSuites: [
          {
            name: 'unit',
            type: 'unit',
            enabled: true,
            timeout: 30000,
            retries: 0
          },
          {
            name: 'integration',
            type: 'integration',
            enabled: false, // Skip integration tests in this integration test
            timeout: 45000,
            retries: 0
          },
          {
            name: 'e2e',
            type: 'e2e',
            enabled: false, // Skip E2E tests for faster execution
            timeout: 60000,
            retries: 0
          }
        ]
      }
    });

    orchestrator = new TestOrchestrator(testConfig);
  });

  describe('Real Test Execution', () => {
    it('should execute Jest unit tests successfully', async () => {
      // Enable only unit tests for this test
      const unitOnlyConfig = await TestConfigManager.loadConfig({
        environment: 'test',
        overrides: {
          testSuites: [
            {
              name: 'unit',
              type: 'unit',
              enabled: true,
              timeout: 30000,
              retries: 0
            }
          ]
        }
      });

      const unitOrchestrator = new TestOrchestrator(unitOnlyConfig);
      const results = await unitOrchestrator.executeAll();

      expect(results).toHaveLength(1);
      expect(results[0]).toBeDefined();
      expect(typeof results[0].passed).toBe('boolean');
      expect(typeof results[0].duration).toBe('number');
      expect(typeof results[0].testCount).toBe('number');
      expect(Array.isArray(results[0].failures)).toBe(true);

      // Verify test results
        duration: results[0].duration,
        testCount: results[0].testCount,
        failureCount: results[0].failures.length
      });

      if (results[0].failures.length > 0) {
        console.log('Test failures:', results[0].failures.slice(0, 3)); // Log first 3 failures
      }

      // Duration should be reasonable (not 0, not too high)
      expect(results[0].duration).toBeGreaterThan(0);
      expect(results[0].duration).toBeLessThan(60000); // Less than 1 minute
    }, 60000); // 1 minute timeout

    it('should handle test execution with coverage when available', async () => {
      const results = await orchestrator.executeAll({ coverage: true });

      expect(results).toHaveLength(1); // Only unit tests enabled
      const unitResult = results[0];

      expect(unitResult).toBeDefined();
      expect(typeof unitResult.passed).toBe('boolean');

      // Coverage might not be available in all environments, so we just check structure
      if (unitResult.coverage) {
        expect(unitResult.coverage).toHaveProperty('lines');
        expect(unitResult.coverage).toHaveProperty('functions');
        expect(unitResult.coverage).toHaveProperty('branches');
        expect(unitResult.coverage).toHaveProperty('statements');
      }

      console.log('Coverage test results:', {
        passed: unitResult.passed,
        testCount: unitResult.testCount,
        hasCoverage: !!unitResult.coverage
      });
    }, 60000);

    it('should aggregate results correctly from real test execution', async () => {
      const results = await orchestrator.executeAll();
      const aggregated = orchestrator.aggregateResults(results);

      expect(aggregated).toBeDefined();
      expect(typeof aggregated.totalTests).toBe('number');
      expect(typeof aggregated.totalPassed).toBe('number');
      expect(typeof aggregated.totalFailed).toBe('number');
      expect(typeof aggregated.totalDuration).toBe('number');
      expect(typeof aggregated.overallPassed).toBe('boolean');
      expect(typeof aggregated.failuresByType).toBe('object');

      // Totals should add up correctly
      expect(aggregated.totalPassed + aggregated.totalFailed).toBe(aggregated.totalTests);

      console.log('Aggregated results:', aggregated);
    }, 60000);

    it('should generate comprehensive test report', async () => {
      const results = await orchestrator.executeAll();
      const reportPath = path.join(process.cwd(), 'test-orchestrator-integration-report.json');

      const reportJson = await orchestrator.generateReport(results, reportPath);

      // Verify report structure
      const report = JSON.parse(reportJson);
      expect(report).toHaveProperty('timestamp');
      expect(report).toHaveProperty('summary');
      expect(report).toHaveProperty('results');

      expect(report.summary).toHaveProperty('totalTests');
      expect(report.summary).toHaveProperty('totalPassed');
      expect(report.summary).toHaveProperty('totalFailed');
      expect(report.summary).toHaveProperty('totalDuration');
      expect(report.summary).toHaveProperty('overallPassed');

      expect(Array.isArray(report.results)).toBe(true);

      // Verify file was written
      const fileExists = await fs.access(reportPath).then(() => true).catch(() => false);
      expect(fileExists).toBe(true);

      // Clean up
      await fs.unlink(reportPath).catch(() => {});

      console.log('Generated report summary:', report.summary);
    }, 60000);
  });

  describe('Configuration Integration', () => {
    it('should work with different environment configurations', async () => {
      const devConfig = await TestConfigManager.loadConfig({ 
        environment: 'development',
        overrides: {
          testSuites: [
            {
              name: 'unit',
              type: 'unit',
              enabled: true,
              timeout: 20000,
              retries: 0
            }
          ]
        }
      });

      const devOrchestrator = new TestOrchestrator(devConfig);
      const results = await devOrchestrator.executeAll();

      expect(results).toHaveLength(1);
      expect(results[0].passed).toBeDefined();

      console.log('Development config results:', {
        passed: results[0].passed,
        testCount: results[0].testCount
      });
    }, 60000);

    it('should respect timeout configurations', async () => {
      const shortTimeoutConfig = await TestConfigManager.loadConfig({
        overrides: {
          testSuites: [
            {
              name: 'unit',
              type: 'unit',
              enabled: true,
              timeout: 5000, // Very short timeout
              retries: 0
            }
          ]
        }
      });

      const shortTimeoutOrchestrator = new TestOrchestrator(shortTimeoutConfig);
      
      const startTime = Date.now();
      const results = await shortTimeoutOrchestrator.executeAll();
      const executionTime = Date.now() - startTime;

      expect(results).toHaveLength(1);
      
      // If tests complete within timeout, they should pass or fail normally
      // If they timeout, the result should indicate failure
      if (executionTime > 5000) {
        // Test likely timed out
        expect(results[0].passed).toBe(false);
      }

      console.log('Timeout test results:', {
        executionTime,
        passed: results[0].passed,
        testCount: results[0].testCount
      });
    }, 30000);
  });

  describe('Error Handling Integration', () => {
    it('should handle Jest configuration errors gracefully', async () => {
      const invalidConfig: TestOrchestratorConfig = {
        ...testConfig,
        jestConfig: 'non-existent-jest.config.js' // Invalid Jest config
      };

      const invalidOrchestrator = new TestOrchestrator(invalidConfig);
      const results = await invalidOrchestrator.executeAll();

      expect(results).toHaveLength(1);
      
      // Should handle the error gracefully
      expect(results[0]).toBeDefined();
      expect(typeof results[0].passed).toBe('boolean');
      
      // If Jest fails to start due to invalid config, it should be reflected in results
      if (!results[0].passed) {
        expect(results[0].failures.length).toBeGreaterThan(0);
      }

      console.log('Invalid config test results:', {
        passed: results[0].passed,
        failureCount: results[0].failures.length
      });
    }, 60000);

    it('should provide meaningful error messages for test failures', async () => {
      const results = await orchestrator.executeAll();

      // If there are any test failures, they should have meaningful error messages
      results.forEach(result => {
        result.failures.forEach(failure => {
          expect(failure.testName).toBeDefined();
          expect(failure.testName.length).toBeGreaterThan(0);
          expect(failure.error).toBeDefined();
          expect(failure.error.length).toBeGreaterThan(0);
          expect(typeof failure.duration).toBe('number');
        });
      });

      console.log('Error message validation completed');
    }, 60000);
  });

  describe('Performance Integration', () => {
    it('should complete test execution within reasonable time limits', async () => {
      const startTime = Date.now();
      const results = await orchestrator.executeAll();
      const totalTime = Date.now() - startTime;

      expect(results).toHaveLength(1);
      
      // Should complete within configured timeout
      expect(totalTime).toBeLessThan(testConfig.timeout || 120000);
      
      // Should not be too fast (indicates tests didn't actually run)
      expect(totalTime).toBeGreaterThan(100);

      console.log('Performance test results:', {
        totalTime,
        testCount: results[0].testCount,
        passed: results[0].passed
      });
    }, 120000);

    it('should handle parallel execution when configured', async () => {
      const parallelConfig = await TestConfigManager.loadConfig({
        environment: 'test',
        overrides: {
          parallel: true,
          testSuites: [
            {
              name: 'unit',
              type: 'unit',
              enabled: true,
              timeout: 30000,
              retries: 0
            }
          ]
        }
      });

      const parallelOrchestrator = new TestOrchestrator(parallelConfig);
      
      const startTime = Date.now();
      const results = await parallelOrchestrator.executeAll();
      const parallelTime = Date.now() - startTime;

      expect(results).toHaveLength(1);
      expect(results[0]).toBeDefined();

      console.log('Parallel execution results:', {
        time: parallelTime,
        passed: results[0].passed,
        testCount: results[0].testCount
      });
    }, 60000);
  });
});