/**
 * <PERSON>ck tests for external dependencies verification
 * Tests the dependency checker with various mock scenarios
 */

import { ExternalDependencyChecker, createExternalDependencyChecker } from '../external-dependencies';
import { ExternalDependency, DependencyStatus, ExternalDependencies } from '../types';

// Mock fetch globally
global.fetch = jest.fn();
const mockFetch = fetch as jest.MockedFunction<typeof fetch>;

// Mock setTimeout for testing timeout scenarios
jest.useFakeTimers();

describe('ExternalDependencyChecker Mock Tests', () => {
  let checker: ExternalDependencyChecker;
  let mockDependencies: ExternalDependency[];

  beforeEach(() => {
    jest.clearAllMocks();
    jest.clearAllTimers();

    mockDependencies = [
      {
        name: 'Google Maps API',
        url: 'https://maps.googleapis.com/maps/api/js?key=test',
        timeout: 5000,
        critical: true,
      },
      {
        name: 'Google Fonts',
        url: 'https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap',
        timeout: 3000,
        critical: false,
      },
      {
        name: 'CDN Assets',
        url: 'https://cdn.example.com/assets/bundle.js',
        timeout: 4000,
        critical: true,
      },
      {
        name: 'Analytics API',
        url: 'https://analytics.example.com/api/track',
        timeout: 2000,
        critical: false,
      },
    ];

    checker = new ExternalDependencyChecker();
  });

  afterEach(() => {
    if (jest.isMockFunction(setTimeout)) {
      jest.runOnlyPendingTimers();
      jest.useRealTimers();
    }
  });

  describe('constructor', () => {
    it('should initialize with provided dependencies', () => {
      expect(checker).toBeInstanceOf(ExternalDependencyChecker);
    });

    it('should handle empty dependencies array', () => {
      const emptyChecker = new ExternalDependencyChecker();
      expect(emptyChecker).toBeInstanceOf(ExternalDependencyChecker);
    });

    it('should validate dependency configuration', () => {
      const invalidDependencies = [
        {
          name: '',
          url: 'invalid-url',
          timeout: -1000,
          critical: true,
        },
      ];

      const validation = checker.validateDependencyConfig(invalidDependencies);
      expect(validation.valid).toBe(false);
      expect(validation.errors.length).toBeGreaterThan(0);
    });
  });

  describe('checkAllDependencies', () => {
    it('should successfully check available dependency', async () => {
      const mockResponse = {
        ok: true,
        status: 200,
        statusText: 'OK',
        headers: new Headers(),
      };

      mockFetch.mockResolvedValue(mockResponse as Response);

      const result = await checker.checkAllDependencies([mockDependencies[0]]);

      expect(result.googleMaps.available).toBe(true);
      expect(result.googleMaps.service).toBe('Google Maps API');
      expect(result.googleMaps.responseTime).toBeGreaterThanOrEqual(0);
      expect(result.googleMaps.error).toBeUndefined();
    });

    it('should handle unavailable dependency', async () => {
      const mockResponse = {
        ok: false,
        status: 404,
        statusText: 'Not Found',
        headers: new Headers(),
      };

      mockFetch.mockResolvedValueOnce(mockResponse as Response);

      const result = await checker.checkDependency(mockDependencies[1]);

      expect(result.available).toBe(false);
      expect(result.service).toBe('Google Fonts');
      expect(result.responseTime).toBeGreaterThanOrEqual(0);
      expect(result.error).toContain('404');
    });

    it('should handle network errors', async () => {
      const networkError = new Error('Network error: ECONNREFUSED');
      mockFetch.mockRejectedValueOnce(networkError);

      const result = await checker.checkDependency(mockDependencies[2]);

      expect(result.available).toBe(false);
      expect(result.service).toBe('CDN Assets');
      expect(result.error).toContain('Network error');
    });

    it('should handle timeout scenarios', async () => {
      // Mock a slow response that exceeds timeout
      mockFetch.mockImplementationOnce(() => 
        new Promise((resolve) => {
          setTimeout(() => {
            resolve({
              ok: true,
              status: 200,
              statusText: 'OK',
              headers: new Headers(),
            } as Response);
          }, 10000); // 10 seconds, longer than any timeout
        })
      );

      const timeoutDependency = {
        ...mockDependencies[3],
        timeout: 1000, // 1 second timeout
      };

      const resultPromise = checker.checkDependency(timeoutDependency);

      // Fast-forward time to trigger timeout
      jest.advanceTimersByTime(1500);

      const result = await resultPromise;

      expect(result.available).toBe(false);
      expect(result.error).toContain('timeout');
    });

    it('should handle different HTTP status codes', async () => {
      const testCases = [
        { status: 200, ok: true, shouldBeAvailable: true },
        { status: 301, ok: false, shouldBeAvailable: false },
        { status: 403, ok: false, shouldBeAvailable: false },
        { status: 500, ok: false, shouldBeAvailable: false },
        { status: 503, ok: false, shouldBeAvailable: false },
      ];

      for (const testCase of testCases) {
        mockFetch.mockResolvedValueOnce({
          ok: testCase.ok,
          status: testCase.status,
          statusText: `Status ${testCase.status}`,
          headers: new Headers(),
        } as Response);

        const result = await checker.checkDependency(mockDependencies[0]);

        expect(result.available).toBe(testCase.shouldBeAvailable);
        if (!testCase.shouldBeAvailable) {
          expect(result.error).toContain(testCase.status.toString());
        }
      }
    });

    it('should measure response time accurately', async () => {
      const mockDelay = 500; // 500ms delay
      
      mockFetch.mockImplementationOnce(() => 
        new Promise((resolve) => {
          setTimeout(() => {
            resolve({
              ok: true,
              status: 200,
              statusText: 'OK',
              headers: new Headers(),
            } as Response);
          }, mockDelay);
        })
      );

      const resultPromise = checker.checkDependency(mockDependencies[0]);
      
      // Advance timers to simulate the delay
      jest.advanceTimersByTime(mockDelay);
      
      const result = await resultPromise;

      expect(result.available).toBe(true);
      expect(result.responseTime).toBeGreaterThanOrEqual(mockDelay - 50); // Allow some margin
      expect(result.responseTime).toBeLessThanOrEqual(mockDelay + 50);
    });
  });

  describe('checkAllDependencies', () => {
    it('should check all dependencies successfully', async () => {
      // Mock all dependencies as available
      mockFetch.mockResolvedValue({
        ok: true,
        status: 200,
        statusText: 'OK',
        headers: new Headers(),
      } as Response);

      const results = await checker.checkAllDependencies();

      expect(results).toHaveProperty('googleMaps');
      expect(results).toHaveProperty('cdnResources');
      expect(results).toHaveProperty('apiEndpoints');

      // Should have made 4 fetch calls (one for each dependency)
      expect(mockFetch).toHaveBeenCalledTimes(4);
    });

    it('should handle mixed success and failure scenarios', async () => {
      // Mock alternating success/failure
      mockFetch
        .mockResolvedValueOnce({ ok: true, status: 200, statusText: 'OK', headers: new Headers() } as Response)
        .mockResolvedValueOnce({ ok: false, status: 404, statusText: 'Not Found', headers: new Headers() } as Response)
        .mockResolvedValueOnce({ ok: true, status: 200, statusText: 'OK', headers: new Headers() } as Response)
        .mockRejectedValueOnce(new Error('Network error'));

      const results = await checker.checkAllDependencies();

      // Should categorize dependencies correctly
      expect(results.googleMaps.available).toBe(true);
      expect(results.cdnResources).toHaveLength(1);
      expect(results.cdnResources[0].available).toBe(false);
      expect(results.apiEndpoints).toHaveLength(1);
      expect(results.apiEndpoints[0].available).toBe(false);
    });

    it('should handle concurrent dependency checks', async () => {
      // Mock different response times
      const delays = [100, 200, 150, 300];
      
      delays.forEach((delay, index) => {
        mockFetch.mockImplementationOnce(() => 
          new Promise((resolve) => {
            setTimeout(() => {
              resolve({
                ok: true,
                status: 200,
                statusText: 'OK',
                headers: new Headers(),
              } as Response);
            }, delay);
          })
        );
      });

      const startTime = Date.now();
      const resultPromise = checker.checkAllDependencies();

      // Advance timers to complete all requests
      jest.advanceTimersByTime(Math.max(...delays) + 100);

      const results = await resultPromise;
      const endTime = Date.now();

      // Should complete in roughly the time of the slowest request (concurrent execution)
      const executionTime = endTime - startTime;
      expect(executionTime).toBeLessThan(delays.reduce((a, b) => a + b, 0)); // Less than sequential time

      // All dependencies should be available
      expect(results.googleMaps.available).toBe(true);
      expect(results.cdnResources[0].available).toBe(true);
      expect(results.apiEndpoints[0].available).toBe(true);
    });

    it('should categorize dependencies correctly', async () => {
      mockFetch.mockResolvedValue({
        ok: true,
        status: 200,
        statusText: 'OK',
        headers: new Headers(),
      } as Response);

      const results = await checker.checkAllDependencies();

      // Google Maps should be in its own category
      expect(results.googleMaps.service).toBe('Google Maps API');

      // CDN resources should include fonts and assets
      expect(results.cdnResources).toHaveLength(2);
      const cdnNames = results.cdnResources.map(r => r.service);
      expect(cdnNames).toContain('Google Fonts');
      expect(cdnNames).toContain('CDN Assets');

      // API endpoints should include analytics
      expect(results.apiEndpoints).toHaveLength(1);
      expect(results.apiEndpoints[0].service).toBe('Analytics API');
    });

    it('should handle empty dependencies gracefully', async () => {
      const emptyChecker = new ExternalDependencyChecker([]);
      const results = await emptyChecker.checkAllDependencies();

      expect(results.googleMaps.available).toBe(false);
      expect(results.cdnResources).toHaveLength(0);
      expect(results.apiEndpoints).toHaveLength(0);
    });
  });

  describe('retry logic', () => {
    it('should retry failed requests up to configured limit', async () => {
      let attemptCount = 0;
      
      mockFetch.mockImplementation(() => {
        attemptCount++;
        if (attemptCount < 3) {
          return Promise.reject(new Error('Temporary network error'));
        }
        return Promise.resolve({
          ok: true,
          status: 200,
          statusText: 'OK',
          headers: new Headers(),
        } as Response);
      });

      const result = await checker.checkDependency(mockDependencies[0]);

      expect(result.available).toBe(true);
      expect(attemptCount).toBe(3); // Should have retried twice
    });

    it('should fail after maximum retry attempts', async () => {
      mockFetch.mockRejectedValue(new Error('Persistent network error'));

      const result = await checker.checkDependency(mockDependencies[0]);

      expect(result.available).toBe(false);
      expect(result.error).toContain('Persistent network error');
      expect(mockFetch).toHaveBeenCalledTimes(3); // Initial + 2 retries
    });

    it('should use exponential backoff for retries', async () => {
      let attemptTimes: number[] = [];
      
      mockFetch.mockImplementation(() => {
        attemptTimes.push(Date.now());
        return Promise.reject(new Error('Network error'));
      });

      const resultPromise = checker.checkDependency(mockDependencies[0]);

      // Advance timers to trigger retries
      jest.advanceTimersByTime(1000); // First retry after 1s
      jest.advanceTimersByTime(2000); // Second retry after 2s more

      await resultPromise;

      expect(mockFetch).toHaveBeenCalledTimes(3);
      // Verify exponential backoff timing (approximately)
      if (attemptTimes.length >= 2) {
        const firstDelay = attemptTimes[1] - attemptTimes[0];
        const secondDelay = attemptTimes[2] - attemptTimes[1];
        expect(secondDelay).toBeGreaterThan(firstDelay);
      }
    });
  });

  describe('error handling and edge cases', () => {
    it('should handle malformed URLs gracefully', async () => {
      const invalidDependency: ExternalDependency = {
        name: 'Invalid URL',
        url: 'not-a-valid-url',
        timeout: 5000,
        critical: false,
      };

      const result = await checker.checkDependency(invalidDependency);

      expect(result.available).toBe(false);
      expect(result.error).toBeDefined();
    });

    it('should handle very short timeouts', async () => {
      const shortTimeoutDependency: ExternalDependency = {
        name: 'Short Timeout',
        url: 'https://example.com',
        timeout: 1, // 1ms timeout
        critical: false,
      };

      mockFetch.mockImplementationOnce(() => 
        new Promise((resolve) => {
          setTimeout(() => {
            resolve({
              ok: true,
              status: 200,
              statusText: 'OK',
              headers: new Headers(),
            } as Response);
          }, 100); // 100ms delay, much longer than timeout
        })
      );

      const resultPromise = checker.checkDependency(shortTimeoutDependency);
      jest.advanceTimersByTime(50); // Advance past timeout

      const result = await resultPromise;

      expect(result.available).toBe(false);
      expect(result.error).toContain('timeout');
    });

    it('should handle fetch API not being available', async () => {
      // Temporarily remove fetch
      const originalFetch = global.fetch;
      delete (global as any).fetch;

      const result = await checker.checkDependency(mockDependencies[0]);

      expect(result.available).toBe(false);
      expect(result.error).toContain('fetch is not available');

      // Restore fetch
      global.fetch = originalFetch;
    });

    it('should handle response parsing errors', async () => {
      // Mock fetch to return invalid response
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        statusText: 'OK',
        headers: null, // Invalid headers
      } as any);

      const result = await checker.checkDependency(mockDependencies[0]);

      // Should handle gracefully
      expect(result).toBeDefined();
      expect(result.service).toBe('Google Maps API');
    });
  });

  describe('performance and optimization', () => {
    it('should handle large numbers of dependencies efficiently', async () => {
      const manyDependencies: ExternalDependency[] = Array.from({ length: 50 }, (_, i) => ({
        name: `Dependency ${i}`,
        url: `https://api${i}.example.com`,
        timeout: 5000,
        critical: i < 10, // First 10 are critical
      }));

      const largeDependencyChecker = new ExternalDependencyChecker(manyDependencies);

      mockFetch.mockResolvedValue({
        ok: true,
        status: 200,
        statusText: 'OK',
        headers: new Headers(),
      } as Response);

      const startTime = Date.now();
      const results = await largeDependencyChecker.checkAllDependencies();
      const endTime = Date.now();

      expect(results.cdnResources.length + results.apiEndpoints.length).toBe(50);
      expect(endTime - startTime).toBeLessThan(10000); // Should complete within 10 seconds
      expect(mockFetch).toHaveBeenCalledTimes(50);
    });

    it('should cache results for repeated checks', async () => {
      mockFetch.mockResolvedValue({
        ok: true,
        status: 200,
        statusText: 'OK',
        headers: new Headers(),
      } as Response);

      // Check same dependency multiple times
      const result1 = await checker.checkDependency(mockDependencies[0]);
      const result2 = await checker.checkDependency(mockDependencies[0]);
      const result3 = await checker.checkDependency(mockDependencies[0]);

      expect(result1.available).toBe(true);
      expect(result2.available).toBe(true);
      expect(result3.available).toBe(true);

      // Should have made only one actual network request due to caching
      expect(mockFetch).toHaveBeenCalledTimes(1);
    });
  });
});

describe('createDependencyChecker factory function', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should create ExternalDependencyChecker instance', () => {
    const dependencies: ExternalDependency[] = [
      {
        name: 'Test API',
        url: 'https://api.test.com',
        timeout: 5000,
        critical: true,
      },
    ];

    const checker = createDependencyChecker(dependencies);
    expect(checker).toBeInstanceOf(ExternalDependencyChecker);
  });

  it('should handle empty dependencies array', () => {
    const checker = createDependencyChecker([]);
    expect(checker).toBeInstanceOf(ExternalDependencyChecker);
  });

  it('should create checker with default configuration', () => {
    const dependencies: ExternalDependency[] = [
      {
        name: 'Default Config API',
        url: 'https://default.api.com',
        timeout: 5000,
        critical: false,
      },
    ];

    const checker = createDependencyChecker(dependencies);
    expect(checker).toBeInstanceOf(ExternalDependencyChecker);
  });
});