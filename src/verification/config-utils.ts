/**
 * Configuration utilities for the verification system
 */

import { ConfigManager, ConfigurationOptions, CONFIG_PRESETS } from './config';
import { VerificationConfig, TestSuiteConfig } from './types';
import * as fs from 'fs/promises';
import * as path from 'path';

/**
 * Configuration template generator
 */
export class ConfigTemplateGenerator {
  /**
   * Generate a basic configuration template
   */
  static generateBasicTemplate(): VerificationConfig {
    return {
      buildSettings: {
        mode: 'production',
        sourceMaps: false,
        minification: true,
      },
      performanceThresholds: {
        lcp: 2500,
        fid: 100,
        cls: 0.1,
        lighthousePerformance: 90,
      },
      accessibilityLevel: 'AA',
      testSuites: [
        {
          name: 'unit-tests',
          type: 'unit',
          enabled: true,
          timeout: 30000,
          retries: 2,
          description: 'Unit tests for individual components',
          priority: 'high',
        },
        {
          name: 'e2e-tests',
          type: 'e2e',
          enabled: true,
          timeout: 120000,
          retries: 2,
          description: 'End-to-end user journey tests',
          priority: 'critical',
        },
      ],
      externalDependencies: [
        {
          name: 'Google Maps API',
          url: 'https://maps.googleapis.com/maps/api/js',
          timeout: 10000,
          critical: true,
        },
      ],
    };
  }

  /**
   * Generate environment-specific configuration
   */
  static generateEnvironmentConfig(environment: 'development' | 'staging' | 'ci' | 'production'): any {
    const baseConfig = this.generateBasicTemplate();
    const preset = CONFIG_PRESETS[environment];
    
    return {
      default: baseConfig,
      environments: {
        [environment]: {
          name: environment,
          overrides: preset,
        },
      },
    };
  }

  /**
   * Generate comprehensive configuration with all environments
   */
  static generateComprehensiveConfig(): any {
    const baseConfig = this.generateBasicTemplate();
    
    return {
      default: baseConfig,
      environments: {
        development: {
          name: 'development',
          overrides: CONFIG_PRESETS.development,
        },
        staging: {
          name: 'staging',
          extends: 'development',
          overrides: CONFIG_PRESETS.staging,
        },
        ci: {
          name: 'ci',
          overrides: CONFIG_PRESETS.ci,
        },
        production: {
          name: 'production',
          overrides: CONFIG_PRESETS.production,
        },
      },
    };
  }
}

/**
 * Configuration migration utilities
 */
export class ConfigMigration {
  /**
   * Migrate old configuration format to new format
   */
  static async migrateConfig(oldConfigPath: string, newConfigPath: string): Promise<void> {
    try {
      const oldConfigData = await fs.readFile(oldConfigPath, 'utf-8');
      const oldConfig: VerificationConfig = JSON.parse(oldConfigData);
      
      // Convert old format to new format
      const newConfig = {
        default: oldConfig,
        environments: {
          development: {
            name: 'development',
            overrides: CONFIG_PRESETS.development,
          },
          staging: {
            name: 'staging',
            overrides: CONFIG_PRESETS.staging,
          },
          ci: {
            name: 'ci',
            overrides: CONFIG_PRESETS.ci,
          },
          production: {
            name: 'production',
            overrides: CONFIG_PRESETS.production,
          },
        },
      };
      
      await fs.writeFile(newConfigPath, JSON.stringify(newConfig, null, 2));
      console.log(`✅ Configuration migrated from ${oldConfigPath} to ${newConfigPath}`);
      
    } catch (error) {
      throw new Error(`Failed to migrate configuration: ${error}`);
    }
  }

  /**
   * Backup existing configuration
   */
  static async backupConfig(configPath: string): Promise<string> {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupPath = `${configPath}.backup-${timestamp}`;
    
    try {
      await fs.copyFile(configPath, backupPath);
      return backupPath;
    } catch (error) {
      throw new Error(`Failed to backup configuration: ${error}`);
    }
  }
}

/**
 * Configuration validation and suggestions
 */
export class ConfigValidator {
  /**
   * Validate and provide suggestions for configuration
   */
  static async validateWithSuggestions(configPath: string, environment?: string): Promise<{
    valid: boolean;
    errors: string[];
    warnings: string[];
    suggestions: string[];
  }> {
    const configManager = await ConfigManager.loadFromFile(configPath, { environment });
    const validation = configManager.validateConfig();
    const config = configManager.getConfig();
    
    const warnings: string[] = [];
    const suggestions: string[] = [];
    
    // Performance threshold suggestions
    if (config.performanceThresholds.lcp > 3000) {
      warnings.push('LCP threshold is quite high (>3s), consider optimizing for better user experience');
      suggestions.push('Consider reducing LCP threshold to 2.5s or lower');
    }
    
    if (config.performanceThresholds.fid > 300) {
      warnings.push('FID threshold is high (>300ms), may impact user interaction experience');
      suggestions.push('Consider reducing FID threshold to 100ms or lower');
    }
    
    // Test suite suggestions
    const enabledSuites = config.testSuites.filter(suite => suite.enabled);
    if (enabledSuites.length === 0) {
      warnings.push('No test suites are enabled');
      suggestions.push('Enable at least unit tests for basic verification');
    }
    
    const hasE2E = enabledSuites.some(suite => suite.type === 'e2e');
    if (!hasE2E) {
      suggestions.push('Consider enabling E2E tests for comprehensive user journey validation');
    }
    
    const hasPerformance = enabledSuites.some(suite => suite.type === 'performance');
    if (!hasPerformance) {
      suggestions.push('Consider enabling performance tests to validate Core Web Vitals');
    }
    
    // Accessibility suggestions
    if (config.accessibilityLevel === 'AA') {
      suggestions.push('Consider upgrading to AAA accessibility level for better compliance');
    }
    
    // External dependencies suggestions
    const criticalDeps = config.externalDependencies.filter(dep => dep.critical);
    if (criticalDeps.length === 0) {
      warnings.push('No critical external dependencies defined');
      suggestions.push('Mark important external services as critical for proper validation');
    }
    
    return {
      valid: validation.valid,
      errors: validation.errors,
      warnings,
      suggestions,
    };
  }

  /**
   * Generate performance recommendations based on thresholds
   */
  static generatePerformanceRecommendations(thresholds: VerificationConfig['performanceThresholds']): string[] {
    const recommendations: string[] = [];
    
    if (thresholds.lcp > 2500) {
      recommendations.push('LCP > 2.5s: Optimize largest content element loading (images, fonts, critical CSS)');
    }
    
    if (thresholds.fid > 100) {
      recommendations.push('FID > 100ms: Reduce JavaScript execution time and optimize event handlers');
    }
    
    if (thresholds.cls > 0.1) {
      recommendations.push('CLS > 0.1: Ensure proper sizing for images and avoid layout shifts');
    }
    
    if (thresholds.lighthousePerformance < 90) {
      recommendations.push('Lighthouse < 90: Review Lighthouse audit suggestions for performance improvements');
    }
    
    return recommendations;
  }
}

/**
 * Configuration comparison utilities
 */
export class ConfigComparison {
  /**
   * Compare two configurations and highlight differences
   */
  static compareConfigs(config1: VerificationConfig, config2: VerificationConfig): {
    differences: Array<{
      path: string;
      value1: any;
      value2: any;
      type: 'added' | 'removed' | 'changed';
    }>;
    summary: {
      totalDifferences: number;
      performanceChanges: number;
      testSuiteChanges: number;
      dependencyChanges: number;
    };
  } {
    const differences: Array<{
      path: string;
      value1: any;
      value2: any;
      type: 'added' | 'removed' | 'changed';
    }> = [];
    
    // Compare performance thresholds
    Object.keys(config1.performanceThresholds).forEach(key => {
      const key1 = key as keyof typeof config1.performanceThresholds;
      const key2 = key as keyof typeof config2.performanceThresholds;
      
      if (config1.performanceThresholds[key1] !== config2.performanceThresholds[key2]) {
        differences.push({
          path: `performanceThresholds.${key}`,
          value1: config1.performanceThresholds[key1],
          value2: config2.performanceThresholds[key2],
          type: 'changed',
        });
      }
    });
    
    // Compare test suites
    const suite1Names = config1.testSuites.map(s => s.name);
    const suite2Names = config2.testSuites.map(s => s.name);
    
    suite1Names.forEach(name => {
      if (!suite2Names.includes(name)) {
        differences.push({
          path: `testSuites.${name}`,
          value1: config1.testSuites.find(s => s.name === name),
          value2: null,
          type: 'removed',
        });
      }
    });
    
    suite2Names.forEach(name => {
      if (!suite1Names.includes(name)) {
        differences.push({
          path: `testSuites.${name}`,
          value1: null,
          value2: config2.testSuites.find(s => s.name === name),
          type: 'added',
        });
      }
    });
    
    // Compare external dependencies
    const dep1Names = config1.externalDependencies.map(d => d.name);
    const dep2Names = config2.externalDependencies.map(d => d.name);
    
    dep1Names.forEach(name => {
      if (!dep2Names.includes(name)) {
        differences.push({
          path: `externalDependencies.${name}`,
          value1: config1.externalDependencies.find(d => d.name === name),
          value2: null,
          type: 'removed',
        });
      }
    });
    
    dep2Names.forEach(name => {
      if (!dep1Names.includes(name)) {
        differences.push({
          path: `externalDependencies.${name}`,
          value1: null,
          value2: config2.externalDependencies.find(d => d.name === name),
          type: 'added',
        });
      }
    });
    
    const summary = {
      totalDifferences: differences.length,
      performanceChanges: differences.filter(d => d.path.startsWith('performanceThresholds')).length,
      testSuiteChanges: differences.filter(d => d.path.startsWith('testSuites')).length,
      dependencyChanges: differences.filter(d => d.path.startsWith('externalDependencies')).length,
    };
    
    return { differences, summary };
  }
}

/**
 * Configuration export utilities
 */
export class ConfigExporter {
  /**
   * Export configuration to different formats
   */
  static async exportConfig(
    config: VerificationConfig,
    format: 'json' | 'yaml' | 'env',
    outputPath: string
  ): Promise<void> {
    switch (format) {
      case 'json':
        await fs.writeFile(outputPath, JSON.stringify(config, null, 2));
        break;
        
      case 'yaml':
        // Would require yaml library
        throw new Error('YAML export not implemented yet');
        
      case 'env':
        const envContent = this.configToEnvFormat(config);
        await fs.writeFile(outputPath, envContent);
        break;
        
      default:
        throw new Error(`Unsupported export format: ${format}`);
    }
  }
  
  /**
   * Convert configuration to environment variable format
   */
  private static configToEnvFormat(config: VerificationConfig): string {
    const lines: string[] = [];
    
    lines.push('# Verification Configuration Environment Variables');
    lines.push('');
    
    // Build settings
    lines.push('# Build Settings');
    lines.push(`VERIFICATION_BUILD_MODE=${config.buildSettings.mode}`);
    lines.push(`VERIFICATION_BUILD_SOURCEMAPS=${config.buildSettings.sourceMaps}`);
    lines.push(`VERIFICATION_BUILD_MINIFICATION=${config.buildSettings.minification}`);
    lines.push('');
    
    // Performance thresholds
    lines.push('# Performance Thresholds');
    lines.push(`VERIFICATION_PERFORMANCE_LCP=${config.performanceThresholds.lcp}`);
    lines.push(`VERIFICATION_PERFORMANCE_FID=${config.performanceThresholds.fid}`);
    lines.push(`VERIFICATION_PERFORMANCE_CLS=${config.performanceThresholds.cls}`);
    lines.push(`VERIFICATION_PERFORMANCE_LIGHTHOUSE=${config.performanceThresholds.lighthousePerformance}`);
    lines.push('');
    
    // Accessibility
    lines.push('# Accessibility');
    lines.push(`VERIFICATION_ACCESSIBILITY_LEVEL=${config.accessibilityLevel}`);
    lines.push('');
    
    // Test suites (as comma-separated list)
    const enabledSuites = config.testSuites.filter(s => s.enabled).map(s => s.name);
    lines.push('# Test Suites');
    lines.push(`VERIFICATION_ENABLED_TEST_SUITES=${enabledSuites.join(',')}`);
    lines.push('');
    
    return lines.join('\n');
  }
}