/**
 * External Dependencies Checker
 * 
 * This module provides functionality to verify that all external dependencies
 * and services are available and functioning correctly before deployment.
 */

import { DependencyStatus, ExternalDependencies, ExternalDependency } from './types';

/**
 * HTTP client configuration for dependency checking
 */
interface HttpClientConfig {
  timeout: number;
  retries: number;
  retryDelay: number;
  userAgent: string;
}

/**
 * Default HTTP client configuration
 */
const DEFAULT_HTTP_CONFIG: HttpClientConfig = {
  timeout: 10000, // 10 seconds
  retries: 3,
  retryDelay: 1000, // 1 second
  userAgent: 'IceBoxHockey-VerificationBot/1.0',
};

/**
 * HTTP Client for testing external service availability
 */
export class HttpClient {
  private config: HttpClientConfig;

  constructor(config: Partial<HttpClientConfig> = {}) {
    this.config = { ...DEFAULT_HTTP_CONFIG, ...config };
  }

  /**
   * Test if a URL is accessible and responding
   */
  async testUrl(url: string, timeout?: number): Promise<DependencyStatus> {
    const startTime = Date.now();
    const effectiveTimeout = timeout || this.config.timeout;

    try {
      const response = await this.fetchWithRetry(url, effectiveTimeout);
      const responseTime = Date.now() - startTime;

      return {
        service: url,
        available: response.ok,
        responseTime: Math.max(responseTime, 1), // Ensure responseTime is at least 1ms
        error: response.ok ? undefined : `HTTP ${response.status}: ${response.statusText}`,
      };
    } catch (error) {
      const responseTime = Date.now() - startTime;
      return {
        service: url,
        available: false,
        responseTime: Math.max(responseTime, 1), // Ensure responseTime is at least 1ms
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Fetch with retry logic and timeout
   */
  private async fetchWithRetry(url: string, timeout: number): Promise<Response> {
    let lastError: Error | null = null;

    for (let attempt = 0; attempt <= this.config.retries; attempt++) {
      try {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), timeout);

        const response = await fetch(url, {
          signal: controller.signal,
          headers: {
            'User-Agent': this.config.userAgent,
          },
          // Prevent CORS issues for dependency checking
          mode: 'no-cors',
        });

        clearTimeout(timeoutId);
        return response;
      } catch (error) {
        lastError = error instanceof Error ? error : new Error('Unknown fetch error');
        
        // Don't retry on the last attempt
        if (attempt < this.config.retries) {
          await this.delay(this.config.retryDelay * (attempt + 1)); // Exponential backoff
        }
      }
    }

    throw lastError || new Error('All retry attempts failed');
  }

  /**
   * Delay utility for retry logic
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

/**
 * Google Maps API Integration Tester
 */
export class GoogleMapsApiTester {
  private httpClient: HttpClient;
  private apiKey?: string;

  constructor(httpClient: HttpClient, apiKey?: string) {
    this.httpClient = httpClient;
    this.apiKey = apiKey;
  }

  /**
   * Test Google Maps API availability and functionality
   */
  async testGoogleMapsApi(): Promise<DependencyStatus> {
    const baseUrl = 'https://maps.googleapis.com/maps/api/js';
    
    try {
      // Test basic API endpoint availability
      const basicTest = await this.httpClient.testUrl(baseUrl);
      
      if (!basicTest.available) {
        return {
          service: 'Google Maps API',
          available: false,
          responseTime: basicTest.responseTime,
          error: `Base API unavailable: ${basicTest.error}`,
        };
      }

      // If API key is provided, test with key
      if (this.apiKey) {
        const keyTest = await this.httpClient.testUrl(`${baseUrl}?key=${this.apiKey}`);
        return {
          service: 'Google Maps API',
          available: keyTest.available,
          responseTime: keyTest.responseTime,
          error: keyTest.error,
        };
      }

      // Return result with proper service name
      return {
        service: 'Google Maps API',
        available: basicTest.available,
        responseTime: basicTest.responseTime,
        error: basicTest.error,
      };
    } catch (error) {
      return {
        service: 'Google Maps API',
        available: false,
        responseTime: 1,
        error: error instanceof Error ? error.message : 'Unknown error testing Google Maps API',
      };
    }
  }

  /**
   * Test specific Google Maps services
   */
  async testGoogleMapsServices(): Promise<DependencyStatus[]> {
    const services = [
      'https://maps.googleapis.com/maps/api/js',
      'https://maps.gstatic.com/mapfiles/api-3/images/spotlight-poi2.png',
      'https://fonts.gstatic.com/s/roboto/v30/KFOmCnqEu92Fr1Mu4mxK.woff2',
    ];

    const results = await Promise.all(
      services.map(url => this.httpClient.testUrl(url))
    );

    return results;
  }
}

/**
 * CDN Resource Availability Checker
 */
export class CdnResourceChecker {
  private httpClient: HttpClient;

  constructor(httpClient: HttpClient) {
    this.httpClient = httpClient;
  }

  /**
   * Test CDN resource availability
   */
  async testCdnResources(resources: string[]): Promise<DependencyStatus[]> {
    const results = await Promise.all(
      resources.map(async (url) => {
        try {
          return await this.httpClient.testUrl(url);
        } catch (error) {
          return {
            service: url,
            available: false,
            responseTime: 0,
            error: error instanceof Error ? error.message : 'Unknown CDN error',
          };
        }
      })
    );

    return results;
  }

  /**
   * Test common CDN resources used by the application
   */
  async testCommonCdnResources(): Promise<DependencyStatus[]> {
    const commonResources = [
      'https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap',
      'https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuLyfAZ9hiJ-Ek-_EeA.woff2',
      'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css',
    ];

    return await this.testCdnResources(commonResources);
  }
}

/**
 * Main External Dependencies Checker
 */
export class ExternalDependencyChecker {
  private httpClient: HttpClient;
  private googleMapsApiTester: GoogleMapsApiTester;
  private cdnResourceChecker: CdnResourceChecker;

  constructor(config: Partial<HttpClientConfig> = {}, googleMapsApiKey?: string) {
    this.httpClient = new HttpClient(config);
    this.googleMapsApiTester = new GoogleMapsApiTester(this.httpClient, googleMapsApiKey);
    this.cdnResourceChecker = new CdnResourceChecker(this.httpClient);
  }

  /**
   * Check all external dependencies
   */
  async checkAllDependencies(dependencies: ExternalDependency[]): Promise<ExternalDependencies> {
    const startTime = Date.now();

    try {
      // Test Google Maps API
      const googleMapsStatus = await this.googleMapsApiTester.testGoogleMapsApi();

      // Test CDN resources
      const cdnUrls = dependencies
        .filter(dep => dep.name.toLowerCase().includes('cdn') || dep.url.includes('cdn'))
        .map(dep => dep.url);
      
      const cdnResources = await this.cdnResourceChecker.testCdnResources(cdnUrls);

      // Test other API endpoints
      const apiEndpoints = await Promise.all(
        dependencies
          .filter(dep => !dep.name.toLowerCase().includes('google maps') && !dep.name.toLowerCase().includes('cdn'))
          .map(dep => this.httpClient.testUrl(dep.url, dep.timeout))
      );

      const totalTime = Date.now() - startTime;
      console.log(`External dependency check completed in ${totalTime}ms`);

      return {
        googleMaps: googleMapsStatus,
        cdnResources,
        apiEndpoints,
      };
    } catch (error) {
      console.error('Error checking external dependencies:', error);
      
      // Return failed status for all dependencies
      return {
        googleMaps: {
          service: 'Google Maps API',
          available: false,
          responseTime: 0,
          error: 'Dependency check failed',
        },
        cdnResources: [],
        apiEndpoints: [],
      };
    }
  }

  /**
   * Check critical dependencies only
   */
  async checkCriticalDependencies(dependencies: ExternalDependency[]): Promise<ExternalDependencies> {
    const criticalDeps = dependencies.filter(dep => dep.critical);
    return await this.checkAllDependencies(criticalDeps);
  }

  /**
   * Validate dependency configuration
   */
  validateDependencyConfig(dependencies: ExternalDependency[]): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    dependencies.forEach((dep, index) => {
      if (!dep.name) {
        errors.push(`Dependency ${index} must have a name`);
      }
      
      if (!dep.url) {
        errors.push(`Dependency ${dep.name || index} must have a URL`);
      } else {
        try {
          new URL(dep.url);
        } catch {
          errors.push(`Dependency ${dep.name} has invalid URL: ${dep.url}`);
        }
      }
      
      if (dep.timeout <= 0) {
        errors.push(`Dependency ${dep.name} timeout must be positive`);
      }
    });

    return {
      valid: errors.length === 0,
      errors,
    };
  }

  /**
   * Get dependency check summary
   */
  getDependencySummary(result: ExternalDependencies): {
    total: number;
    available: number;
    unavailable: number;
    criticalFailures: string[];
  } {
    const allDependencies = [
      result.googleMaps,
      ...result.cdnResources,
      ...result.apiEndpoints,
    ];

    const available = allDependencies.filter(dep => dep.available).length;
    const unavailable = allDependencies.length - available;
    
    const criticalFailures = allDependencies
      .filter(dep => !dep.available)
      .map(dep => `${dep.service}: ${dep.error || 'Unknown error'}`);

    return {
      total: allDependencies.length,
      available,
      unavailable,
      criticalFailures,
    };
  }
}

/**
 * Factory function to create an external dependency checker
 */
export function createExternalDependencyChecker(
  config: Partial<HttpClientConfig> = {},
  googleMapsApiKey?: string
): ExternalDependencyChecker {
  return new ExternalDependencyChecker(config, googleMapsApiKey);
}

/**
 * Utility function to test a single URL quickly
 */
export async function testSingleUrl(url: string, timeout = 10000): Promise<DependencyStatus> {
  const httpClient = new HttpClient({ timeout });
  return await httpClient.testUrl(url, timeout);
}