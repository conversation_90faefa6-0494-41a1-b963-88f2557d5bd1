/**
 * Comprehensive Error Handling and Recovery System
 * 
 * Provides error handling, retry logic, logging, and graceful failure handling
 * for all verification stages
 */

import { writeFile, mkdir } from 'fs/promises';
import { join, dirname } from 'path';

// Error Types and Interfaces
export interface VerificationError {
  id: string;
  stage: string;
  type: 'build' | 'test' | 'performance' | 'accessibility' | 'pwa' | 'dependency' | 'system';
  severity: 'critical' | 'error' | 'warning' | 'info';
  message: string;
  details?: string;
  stack?: string;
  timestamp: Date;
  context?: Record<string, any>;
  recoverable: boolean;
  retryCount?: number;
  maxRetries?: number;
}

export interface RetryConfig {
  maxRetries: number;
  baseDelay: number; // milliseconds
  maxDelay: number; // milliseconds
  backoffMultiplier: number;
  retryableErrors: string[]; // Error patterns that should trigger retry
  nonRetryableErrors: string[]; // Error patterns that should not be retried
}

export interface LogConfig {
  level: 'debug' | 'info' | 'warn' | 'error';
  outputFile?: string;
  enableConsole: boolean;
  enableFile: boolean;
  maxFileSize: number; // bytes
  maxFiles: number;
  includeStackTrace: boolean;
}

export interface ErrorRecoveryResult {
  success: boolean;
  error?: VerificationError;
  recoveryActions: string[];
  finalAttempt: boolean;
}

// Default configurations
export const DEFAULT_RETRY_CONFIG: RetryConfig = {
  maxRetries: 3,
  baseDelay: 1000,
  maxDelay: 30000,
  backoffMultiplier: 2,
  retryableErrors: [
    'ECONNRESET',
    'ENOTFOUND',
    'ETIMEDOUT',
    'ECONNREFUSED',
    'timeout',
    'network',
    'flaky',
    'intermittent',
    'browser launch failed',
    'page crashed',
    'navigation timeout'
  ],
  nonRetryableErrors: [
    'syntax error',
    'type error',
    'compilation error',
    'configuration error',
    'permission denied',
    'file not found',
    'invalid argument'
  ]
};

export const DEFAULT_LOG_CONFIG: LogConfig = {
  level: 'info',
  outputFile: 'verification.log',
  enableConsole: true,
  enableFile: true,
  maxFileSize: 10 * 1024 * 1024, // 10MB
  maxFiles: 5,
  includeStackTrace: true
};

/**
 * Enhanced Logger with file rotation and structured logging
 */
export class VerificationLogger {
  private config: LogConfig;
  private logBuffer: string[] = [];
  private currentLogFile?: string;

  constructor(config: Partial<LogConfig> = {}) {
    this.config = { ...DEFAULT_LOG_CONFIG, ...config };
    this.initializeLogger();
  }

  private async initializeLogger(): Promise<void> {
    if (this.config.enableFile && this.config.outputFile) {
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      this.currentLogFile = `${this.config.outputFile.replace('.log', '')}-${timestamp}.log`;
      
      try {
        const logDir = dirname(this.currentLogFile);
        await mkdir(logDir, { recursive: true });
      } catch (error) {
        console.warn('Failed to create log directory:', error);
        this.config.enableFile = false;
      }
    }
  }

  async log(level: 'debug' | 'info' | 'warn' | 'error', message: string, context?: Record<string, any>): Promise<void> {
    const timestamp = new Date().toISOString();
    const logEntry = {
      timestamp,
      level: level.toUpperCase(),
      message,
      context: context || {}
    };

    const logLine = JSON.stringify(logEntry);

    // Console logging
    if (this.config.enableConsole && this.shouldLog(level)) {
      const coloredMessage = this.colorizeMessage(level, `[${timestamp}] ${level.toUpperCase()}: ${message}`);
      console.log(coloredMessage);
      
      if (context && Object.keys(context).length > 0) {
        console.log('Context:', JSON.stringify(context, null, 2));
      }
    }

    // File logging
    if (this.config.enableFile && this.currentLogFile && this.shouldLog(level)) {
      this.logBuffer.push(logLine);
      
      // Flush buffer periodically or when it gets large
      if (this.logBuffer.length >= 10) {
        await this.flushLogBuffer();
      }
    }
  }

  async debug(message: string, context?: Record<string, any>): Promise<void> {
    await this.log('debug', message, context);
  }

  async info(message: string, context?: Record<string, any>): Promise<void> {
    await this.log('info', message, context);
  }

  async warn(message: string, context?: Record<string, any>): Promise<void> {
    await this.log('warn', message, context);
  }

  async error(message: string, context?: Record<string, any>): Promise<void> {
    await this.log('error', message, context);
  }

  async logError(error: VerificationError): Promise<void> {
    await this.log(error.severity, error.message, {
      id: error.id,
      stage: error.stage,
      type: error.type,
      details: error.details,
      stack: this.config.includeStackTrace ? error.stack : undefined,
      context: error.context,
      recoverable: error.recoverable,
      retryCount: error.retryCount
    });
  }

  private shouldLog(level: 'debug' | 'info' | 'warn' | 'error'): boolean {
    const levels = ['debug', 'info', 'warn', 'error'];
    const configLevelIndex = levels.indexOf(this.config.level);
    const messageLevelIndex = levels.indexOf(level);
    return messageLevelIndex >= configLevelIndex;
  }

  private colorizeMessage(level: string, message: string): string {
    const colors = {
      debug: '\x1b[36m', // Cyan
      info: '\x1b[32m',  // Green
      warn: '\x1b[33m',  // Yellow
      error: '\x1b[31m', // Red
      reset: '\x1b[0m'   // Reset
    };

    const color = colors[level as keyof typeof colors] || colors.reset;
    return `${color}${message}${colors.reset}`;
  }

  private async flushLogBuffer(): Promise<void> {
    if (!this.currentLogFile || this.logBuffer.length === 0) {
      return;
    }

    try {
      const logContent = this.logBuffer.join('\n') + '\n';
      await writeFile(this.currentLogFile, logContent, { flag: 'a' });
      this.logBuffer = [];
    } catch (error) {
      console.warn('Failed to write to log file:', error);
    }
  }

  async flush(): Promise<void> {
    await this.flushLogBuffer();
  }
}

/**
 * Retry mechanism with exponential backoff
 */
export class RetryManager {
  private config: RetryConfig;
  private logger: VerificationLogger;

  constructor(config: Partial<RetryConfig> = {}, logger?: VerificationLogger) {
    this.config = { ...DEFAULT_RETRY_CONFIG, ...config };
    this.logger = logger || new VerificationLogger();
  }

  /**
   * Execute a function with retry logic
   */
  async executeWithRetry<T>(
    operation: () => Promise<T>,
    context: {
      operationName: string;
      stage: string;
      maxRetries?: number;
    }
  ): Promise<T> {
    const maxRetries = context.maxRetries || this.config.maxRetries;
    let lastError: Error | null = null;
    let attempt = 0;

    while (attempt <= maxRetries) {
      try {
        await this.logger.debug(`Attempting ${context.operationName} (attempt ${attempt + 1}/${maxRetries + 1})`, {
          stage: context.stage,
          attempt: attempt + 1,
          maxRetries: maxRetries + 1
        });

        const result = await operation();
        
        if (attempt > 0) {
          await this.logger.info(`${context.operationName} succeeded after ${attempt + 1} attempts`, {
            stage: context.stage,
            totalAttempts: attempt + 1
          });
        }

        return result;

      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));
        attempt++;

        await this.logger.warn(`${context.operationName} failed on attempt ${attempt}`, {
          stage: context.stage,
          attempt,
          error: lastError.message,
          stack: lastError.stack
        });

        // Check if error is retryable
        if (!this.isRetryableError(lastError) || attempt > maxRetries) {
          break;
        }

        // Calculate delay with exponential backoff
        const delay = this.calculateDelay(attempt - 1);
        await this.logger.debug(`Retrying ${context.operationName} in ${delay}ms`, {
          stage: context.stage,
          delay,
          attempt: attempt + 1
        });

        await this.sleep(delay);
      }
    }

    // All retries exhausted
    const finalError = new Error(
      `${context.operationName} failed after ${attempt} attempts. Last error: ${lastError?.message}`
    );
    finalError.stack = lastError?.stack;

    await this.logger.error(`${context.operationName} failed permanently`, {
      stage: context.stage,
      totalAttempts: attempt,
      finalError: finalError.message,
      originalError: lastError?.message
    });

    throw finalError;
  }

  /**
   * Check if an error should trigger a retry
   */
  private isRetryableError(error: Error): boolean {
    const errorMessage = error.message.toLowerCase();
    const errorStack = (error.stack || '').toLowerCase();
    const fullErrorText = `${errorMessage} ${errorStack}`;

    // Check non-retryable errors first (these take precedence)
    for (const pattern of this.config.nonRetryableErrors) {
      if (fullErrorText.includes(pattern.toLowerCase())) {
        return false;
      }
    }

    // Check retryable errors
    for (const pattern of this.config.retryableErrors) {
      if (fullErrorText.includes(pattern.toLowerCase())) {
        return true;
      }
    }

    // Default to non-retryable for unknown errors
    return false;
  }

  /**
   * Calculate delay with exponential backoff and jitter
   */
  private calculateDelay(attemptNumber: number): number {
    const exponentialDelay = this.config.baseDelay * Math.pow(this.config.backoffMultiplier, attemptNumber);
    const cappedDelay = Math.min(exponentialDelay, this.config.maxDelay);
    
    // Add jitter (±25% of the delay)
    const jitter = cappedDelay * 0.25 * (Math.random() * 2 - 1);
    
    return Math.max(0, Math.round(cappedDelay + jitter));
  }

  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

/**
 * Error Handler for comprehensive error management
 */
export class ErrorHandler {
  private logger: VerificationLogger;
  private retryManager: RetryManager;
  private errorHistory: VerificationError[] = [];

  constructor(
    logConfig: Partial<LogConfig> = {},
    retryConfig: Partial<RetryConfig> = {}
  ) {
    this.logger = new VerificationLogger(logConfig);
    this.retryManager = new RetryManager(retryConfig, this.logger);
  }

  /**
   * Create a verification error with proper context
   */
  createError(
    stage: string,
    type: VerificationError['type'],
    severity: VerificationError['severity'],
    message: string,
    options: {
      details?: string;
      stack?: string;
      context?: Record<string, any>;
      recoverable?: boolean;
      originalError?: Error;
    } = {}
  ): VerificationError {
    const error: VerificationError = {
      id: this.generateErrorId(),
      stage,
      type,
      severity,
      message,
      details: options.details,
      stack: options.stack || options.originalError?.stack,
      timestamp: new Date(),
      context: options.context,
      recoverable: options.recoverable ?? this.isRecoverableError(message, type),
      retryCount: 0,
      maxRetries: this.getMaxRetriesForError(type, severity)
    };

    this.errorHistory.push(error);
    return error;
  }

  /**
   * Handle an error with appropriate recovery actions
   */
  async handleError(error: VerificationError): Promise<ErrorRecoveryResult> {
    await this.logger.logError(error);

    const recoveryActions: string[] = [];

    // Determine recovery actions based on error type and context
    switch (error.type) {
      case 'build':
        recoveryActions.push(...this.getBuildRecoveryActions(error));
        break;
      case 'test':
        recoveryActions.push(...this.getTestRecoveryActions(error));
        break;
      case 'performance':
        recoveryActions.push(...this.getPerformanceRecoveryActions(error));
        break;
      case 'accessibility':
        recoveryActions.push(...this.getAccessibilityRecoveryActions(error));
        break;
      case 'pwa':
        recoveryActions.push(...this.getPWARecoveryActions(error));
        break;
      case 'dependency':
        recoveryActions.push(...this.getDependencyRecoveryActions(error));
        break;
      case 'system':
        recoveryActions.push(...this.getSystemRecoveryActions(error));
        break;
    }

    const finalAttempt = (error.retryCount || 0) >= (error.maxRetries || 0);

    return {
      success: false,
      error,
      recoveryActions,
      finalAttempt
    };
  }

  /**
   * Execute operation with comprehensive error handling
   */
  async executeWithErrorHandling<T>(
    operation: () => Promise<T>,
    context: {
      operationName: string;
      stage: string;
      type: VerificationError['type'];
      maxRetries?: number;
      timeout?: number;
    }
  ): Promise<T> {
    const startTime = Date.now();

    try {
      // Add timeout if specified
      let operationPromise = operation();
      
      if (context.timeout) {
        const timeoutPromise = new Promise<never>((_, reject) => {
          setTimeout(() => {
            reject(new Error(`Operation ${context.operationName} timed out after ${context.timeout}ms`));
          }, context.timeout);
        });
        
        operationPromise = Promise.race([operationPromise, timeoutPromise]);
      }

      // Execute with retry logic
      const result = await this.retryManager.executeWithRetry(
        () => {
          // Create a new promise for each retry attempt
          let attemptPromise = operation();
          
          if (context.timeout) {
            const timeoutPromise = new Promise<never>((_, reject) => {
              setTimeout(() => {
                reject(new Error(`Operation ${context.operationName} timed out after ${context.timeout}ms`));
              }, context.timeout);
            });
            
            attemptPromise = Promise.race([attemptPromise, timeoutPromise]);
          }
          
          return attemptPromise;
        },
        {
          operationName: context.operationName,
          stage: context.stage,
          maxRetries: context.maxRetries
        }
      );

      const duration = Date.now() - startTime;
      await this.logger.info(`${context.operationName} completed successfully`, {
        stage: context.stage,
        duration
      });

      return result;

    } catch (error) {
      const duration = Date.now() - startTime;
      const verificationError = this.createError(
        context.stage,
        context.type,
        'error',
        `${context.operationName} failed: ${error instanceof Error ? error.message : String(error)}`,
        {
          originalError: error instanceof Error ? error : undefined,
          context: { duration, operationName: context.operationName }
        }
      );

      const recoveryResult = await this.handleError(verificationError);
      
      // Re-throw with enhanced error information
      const enhancedError = new Error(verificationError.message);
      enhancedError.stack = verificationError.stack;
      (enhancedError as any).verificationError = verificationError;
      (enhancedError as any).recoveryActions = recoveryResult.recoveryActions;
      
      throw enhancedError;
    }
  }

  /**
   * Get error statistics and patterns
   */
  getErrorStatistics(): {
    totalErrors: number;
    errorsByStage: Record<string, number>;
    errorsByType: Record<string, number>;
    errorsBySeverity: Record<string, number>;
    recoverableErrors: number;
    recentErrors: VerificationError[];
  } {
    const now = Date.now();
    const oneHourAgo = now - (60 * 60 * 1000);

    return {
      totalErrors: this.errorHistory.length,
      errorsByStage: this.groupBy(this.errorHistory, 'stage'),
      errorsByType: this.groupBy(this.errorHistory, 'type'),
      errorsBySeverity: this.groupBy(this.errorHistory, 'severity'),
      recoverableErrors: this.errorHistory.filter(e => e.recoverable).length,
      recentErrors: this.errorHistory.filter(e => e.timestamp.getTime() > oneHourAgo)
    };
  }

  // Recovery action generators
  private getBuildRecoveryActions(error: VerificationError): string[] {
    const actions: string[] = [];
    
    if (error.message.includes('typescript') || error.message.includes('type error')) {
      actions.push('Check TypeScript configuration and fix type errors');
      actions.push('Run "npm run type-check" to identify specific type issues');
    }
    
    if (error.message.includes('module') || error.message.includes('import')) {
      actions.push('Verify all dependencies are installed with "npm install"');
      actions.push('Check import paths and module resolution');
    }
    
    if (error.message.includes('memory') || error.message.includes('heap')) {
      actions.push('Increase Node.js memory limit with --max-old-space-size');
      actions.push('Clear node_modules and reinstall dependencies');
    }

    actions.push('Clear build cache and retry');
    actions.push('Check disk space and file permissions');
    
    return actions;
  }

  private getTestRecoveryActions(error: VerificationError): string[] {
    const actions: string[] = [];
    
    if (error.message.includes('timeout')) {
      actions.push('Increase test timeout values');
      actions.push('Check for infinite loops or blocking operations in tests');
    }
    
    if (error.message.includes('browser') || error.message.includes('playwright')) {
      actions.push('Ensure browser dependencies are installed');
      actions.push('Run "npx playwright install" to update browsers');
      actions.push('Check if display server is available for headless testing');
    }
    
    if (error.message.includes('port') || error.message.includes('address')) {
      actions.push('Check if test server port is available');
      actions.push('Kill any processes using the test port');
    }

    actions.push('Run tests individually to isolate failures');
    actions.push('Clear test cache and temporary files');
    
    return actions;
  }

  private getPerformanceRecoveryActions(error: VerificationError): string[] {
    const actions: string[] = [];
    
    actions.push('Optimize bundle size and remove unused dependencies');
    actions.push('Enable code splitting and lazy loading');
    actions.push('Optimize images and static assets');
    actions.push('Review and optimize critical rendering path');
    actions.push('Consider adjusting performance thresholds if they are too strict');
    
    return actions;
  }

  private getAccessibilityRecoveryActions(error: VerificationError): string[] {
    const actions: string[] = [];
    
    actions.push('Review WCAG 2.1 AA guidelines for specific violations');
    actions.push('Add missing ARIA labels and descriptions');
    actions.push('Ensure proper heading hierarchy and semantic markup');
    actions.push('Test keyboard navigation and focus management');
    actions.push('Verify color contrast ratios meet accessibility standards');
    
    return actions;
  }

  private getPWARecoveryActions(error: VerificationError): string[] {
    const actions: string[] = [];
    
    actions.push('Check service worker registration and update logic');
    actions.push('Validate PWA manifest file syntax and content');
    actions.push('Test offline functionality and cache strategies');
    actions.push('Ensure HTTPS is properly configured');
    actions.push('Verify PWA icons and metadata are correct');
    
    return actions;
  }

  private getDependencyRecoveryActions(error: VerificationError): string[] {
    const actions: string[] = [];
    
    actions.push('Check network connectivity and DNS resolution');
    actions.push('Verify API keys and authentication credentials');
    actions.push('Test external service endpoints manually');
    actions.push('Implement fallback mechanisms for external dependencies');
    actions.push('Consider using CDN alternatives or local fallbacks');
    
    return actions;
  }

  private getSystemRecoveryActions(error: VerificationError): string[] {
    const actions: string[] = [];
    
    actions.push('Check system resources (CPU, memory, disk space)');
    actions.push('Verify Node.js and npm versions are compatible');
    actions.push('Check file system permissions');
    actions.push('Restart development server and clear all caches');
    actions.push('Review system logs for additional error details');
    
    return actions;
  }

  // Utility methods
  private generateErrorId(): string {
    return `err_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private isRecoverableError(message: string, type: VerificationError['type']): boolean {
    const nonRecoverablePatterns = [
      'syntax error',
      'compilation error',
      'configuration error',
      'permission denied',
      'file not found'
    ];

    const lowerMessage = message.toLowerCase();
    return !nonRecoverablePatterns.some(pattern => lowerMessage.includes(pattern));
  }

  private getMaxRetriesForError(type: VerificationError['type'], severity: VerificationError['severity']): number {
    if (severity === 'critical') return 0; // Don't retry critical errors
    
    switch (type) {
      case 'build': return 1; // Build errors usually aren't transient
      case 'test': return 3; // Tests can be flaky
      case 'performance': return 2; // Performance can vary
      case 'accessibility': return 1; // A11y issues are usually deterministic
      case 'pwa': return 2; // PWA tests can be flaky
      case 'dependency': return 5; // Network issues are often transient
      case 'system': return 2; // System issues might be transient
      default: return 2;
    }
  }

  private groupBy<T>(array: T[], key: keyof T): Record<string, number> {
    return array.reduce((groups, item) => {
      const value = String(item[key]);
      groups[value] = (groups[value] || 0) + 1;
      return groups;
    }, {} as Record<string, number>);
  }

  getLogger(): VerificationLogger {
    return this.logger;
  }

  async cleanup(): Promise<void> {
    await this.logger.flush();
  }
}

// Factory functions for easy instantiation
export function createErrorHandler(
  logConfig: Partial<LogConfig> = {},
  retryConfig: Partial<RetryConfig> = {}
): ErrorHandler {
  return new ErrorHandler(logConfig, retryConfig);
}

export function createLogger(config: Partial<LogConfig> = {}): VerificationLogger {
  return new VerificationLogger(config);
}

export function createRetryManager(
  config: Partial<RetryConfig> = {},
  logger?: VerificationLogger
): RetryManager {
  return new RetryManager(config, logger);
}