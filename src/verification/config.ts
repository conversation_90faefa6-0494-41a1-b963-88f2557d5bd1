/**
 * Configuration system for the verification pipeline
 */

import { VerificationConfig, TestSuiteConfig, ExternalDependency } from './types';
import * as fs from 'fs/promises';
import * as path from 'path';

// Environment-specific configuration interface
export interface EnvironmentConfig {
  name: string;
  extends?: string;
  overrides: Partial<VerificationConfig>;
}

// Configuration file structure with environment support
export interface ConfigFile {
  default: VerificationConfig;
  environments?: Record<string, EnvironmentConfig>;
}

// Test suite filter options
export interface TestSuiteFilter {
  include?: string[];
  exclude?: string[];
  types?: ('unit' | 'integration' | 'e2e' | 'performance' | 'accessibility')[];
  tags?: string[];
}

// Enhanced configuration options
export interface ConfigurationOptions {
  environment?: string;
  testSuiteFilter?: TestSuiteFilter;
  performanceOverrides?: Partial<VerificationConfig['performanceThresholds']>;
  accessibilityOverrides?: {
    level?: 'AA' | 'AAA';
    rules?: {
      include?: string[];
      exclude?: string[];
    };
  };
  customThresholds?: Record<string, number>;
}

// Default configuration values
export const DEFAULT_CONFIG: VerificationConfig = {
  buildSettings: {
    mode: 'production',
    sourceMaps: false,
    minification: true,
  },
  performanceThresholds: {
    lcp: 2500, // 2.5 seconds
    fid: 100,  // 100 milliseconds
    cls: 0.1,  // 0.1 cumulative layout shift
    lighthousePerformance: 90, // 90/100 score
  },
  accessibilityLevel: 'AA',
  testSuites: [
    {
      name: 'unit-tests',
      type: 'unit',
      enabled: true,
      timeout: 30000,
      retries: 2,
    },
    {
      name: 'integration-tests',
      type: 'integration',
      enabled: true,
      timeout: 60000,
      retries: 2,
    },
    {
      name: 'e2e-tests',
      type: 'e2e',
      enabled: true,
      timeout: 120000,
      retries: 3,
    },
    {
      name: 'performance-tests',
      type: 'performance',
      enabled: true,
      timeout: 180000,
      retries: 2,
    },
    {
      name: 'accessibility-tests',
      type: 'accessibility',
      enabled: true,
      timeout: 90000,
      retries: 2,
    },
    {
      name: 'pwa-tests',
      type: 'e2e',
      enabled: true,
      timeout: 60000,
      retries: 2,
    },
  ],
  externalDependencies: [
    {
      name: 'Google Maps API',
      url: 'https://maps.googleapis.com/maps/api/js',
      timeout: 10000,
      critical: true,
    },
    {
      name: 'CDN Resources',
      url: 'https://fonts.googleapis.com',
      timeout: 5000,
      critical: false,
    },
  ],
};

/**
 * Configuration manager class
 */
export class ConfigManager {
  private config: VerificationConfig;
  private configFile?: ConfigFile;
  private currentEnvironment?: string;
  private options: ConfigurationOptions;

  constructor(customConfig?: Partial<VerificationConfig>, options: ConfigurationOptions = {}) {
    this.options = options;
    let baseConfig = this.mergeConfig(DEFAULT_CONFIG, customConfig || {});
    this.config = this.applyConfigurationOptions(baseConfig);
  }

  /**
   * Get the current configuration
   */
  getConfig(): VerificationConfig {
    return { ...this.config };
  }

  /**
   * Update configuration with new values
   */
  updateConfig(updates: Partial<VerificationConfig>): void {
    this.config = this.mergeConfig(this.config, updates);
  }

  /**
   * Get performance thresholds
   */
  getPerformanceThresholds() {
    return this.config.performanceThresholds;
  }

  /**
   * Get enabled test suites
   */
  getEnabledTestSuites(): TestSuiteConfig[] {
    return this.config.testSuites.filter(suite => suite.enabled);
  }

  /**
   * Get critical external dependencies
   */
  getCriticalDependencies(): ExternalDependency[] {
    return this.config.externalDependencies.filter(dep => dep.critical);
  }

  /**
   * Get build settings
   */
  getBuildSettings() {
    return this.config.buildSettings;
  }

  /**
   * Get current environment
   */
  getCurrentEnvironment(): string | undefined {
    return this.currentEnvironment;
  }

  /**
   * Get available environments
   */
  getAvailableEnvironments(): string[] {
    if (!this.configFile?.environments) {
      return ['default'];
    }
    return ['default', ...Object.keys(this.configFile.environments)];
  }

  /**
   * Switch to a different environment
   */
  async switchEnvironment(environment: string): Promise<void> {
    if (!this.configFile) {
      throw new Error('No configuration file loaded');
    }
    
    if (environment !== 'default' && !this.configFile.environments?.[environment]) {
      throw new Error(`Environment '${environment}' not found in configuration`);
    }
    
    await this.loadConfigFile(this.configFile, environment);
  }

  /**
   * Get filtered test suites based on current configuration
   */
  getFilteredTestSuites(): TestSuiteConfig[] {
    return this.getEnabledTestSuites();
  }

  /**
   * Update performance thresholds
   */
  updatePerformanceThresholds(thresholds: Partial<VerificationConfig['performanceThresholds']>): void {
    this.config.performanceThresholds = {
      ...this.config.performanceThresholds,
      ...thresholds,
    };
  }

  /**
   * Update accessibility settings
   */
  updateAccessibilitySettings(settings: { level?: 'AA' | 'AAA' }): void {
    if (settings.level) {
      this.config.accessibilityLevel = settings.level;
    }
  }

  /**
   * Add or update test suite configuration
   */
  updateTestSuite(name: string, updates: Partial<TestSuiteConfig>): void {
    const index = this.config.testSuites.findIndex(suite => suite.name === name);
    if (index >= 0) {
      this.config.testSuites[index] = {
        ...this.config.testSuites[index],
        ...updates,
      };
    } else {
      // Add new test suite if it doesn't exist
      const newSuite: TestSuiteConfig = {
        name,
        type: 'unit',
        enabled: true,
        timeout: 30000,
        retries: 2,
        ...updates,
      };
      this.config.testSuites.push(newSuite);
    }
  }

  /**
   * Remove test suite
   */
  removeTestSuite(name: string): void {
    this.config.testSuites = this.config.testSuites.filter(suite => suite.name !== name);
  }

  /**
   * Enable or disable test suites by type
   */
  toggleTestSuitesByType(type: TestSuiteConfig['type'], enabled: boolean): void {
    this.config.testSuites.forEach(suite => {
      if (suite.type === type) {
        suite.enabled = enabled;
      }
    });
  }

  /**
   * Get configuration summary
   */
  getConfigSummary(): {
    environment: string | undefined;
    enabledTestSuites: number;
    totalTestSuites: number;
    performanceThresholds: VerificationConfig['performanceThresholds'];
    accessibilityLevel: string;
    criticalDependencies: number;
  } {
    const enabledSuites = this.getEnabledTestSuites();
    const criticalDeps = this.getCriticalDependencies();
    
    return {
      environment: this.currentEnvironment,
      enabledTestSuites: enabledSuites.length,
      totalTestSuites: this.config.testSuites.length,
      performanceThresholds: this.config.performanceThresholds,
      accessibilityLevel: this.config.accessibilityLevel,
      criticalDependencies: criticalDeps.length,
    };
  }

  /**
   * Validate configuration
   */
  validateConfig(): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Validate performance thresholds
    const { lcp, fid, cls, lighthousePerformance } = this.config.performanceThresholds;
    
    if (lcp <= 0) errors.push('LCP threshold must be positive');
    if (fid <= 0) errors.push('FID threshold must be positive');
    if (cls < 0 || cls > 1) errors.push('CLS threshold must be between 0 and 1');
    if (lighthousePerformance < 0 || lighthousePerformance > 100) {
      errors.push('Lighthouse performance threshold must be between 0 and 100');
    }

    // Validate test suites
    if (this.config.testSuites.length === 0) {
      errors.push('At least one test suite must be configured');
    }

    this.config.testSuites.forEach((suite, index) => {
      if (!suite.name) errors.push(`Test suite ${index} must have a name`);
      if (suite.timeout <= 0) errors.push(`Test suite ${suite.name} timeout must be positive`);
      if (suite.retries < 0) errors.push(`Test suite ${suite.name} retries must be non-negative`);
    });

    // Validate external dependencies
    this.config.externalDependencies.forEach((dep, index) => {
      if (!dep.name) errors.push(`External dependency ${index} must have a name`);
      if (!dep.url) errors.push(`External dependency ${dep.name} must have a URL`);
      if (dep.timeout <= 0) errors.push(`External dependency ${dep.name} timeout must be positive`);
    });

    return {
      valid: errors.length === 0,
      errors,
    };
  }

  /**
   * Load configuration from file with environment support
   */
  static async loadFromFile(filePath: string, options: ConfigurationOptions = {}): Promise<ConfigManager> {
    try {
      const configData = await fs.readFile(filePath, 'utf-8');
      const configFile: ConfigFile = JSON.parse(configData);
      
      const manager = new ConfigManager(undefined, options);
      await manager.loadConfigFile(configFile, options.environment);
      
      return manager;
    } catch (error) {
      console.warn(`Failed to load config from ${filePath}, using defaults:`, error);
      return new ConfigManager(undefined, options);
    }
  }

  /**
   * Load configuration file with environment resolution
   */
  async loadConfigFile(configFile: ConfigFile, environment?: string): Promise<void> {
    this.configFile = configFile;
    this.currentEnvironment = environment || process.env.NODE_ENV || 'default';
    
    // Start with default configuration
    let resolvedConfig = { ...configFile.default };
    
    // Apply environment-specific overrides
    if (environment && configFile.environments?.[environment]) {
      const envConfig = configFile.environments[environment];
      
      // Handle inheritance
      if (envConfig.extends && configFile.environments[envConfig.extends]) {
        const parentConfig = configFile.environments[envConfig.extends];
        resolvedConfig = this.mergeConfig(resolvedConfig, parentConfig.overrides);
      }
      
      // Apply environment overrides
      resolvedConfig = this.mergeConfig(resolvedConfig, envConfig.overrides);
    }
    
    // Apply configuration options
    resolvedConfig = this.applyConfigurationOptions(resolvedConfig);
    
    this.config = resolvedConfig;
  }

  /**
   * Apply configuration options (filters, overrides, etc.)
   */
  private applyConfigurationOptions(config: VerificationConfig): VerificationConfig {
    let result = { ...config };
    
    // Apply test suite filtering
    if (this.options.testSuiteFilter) {
      result.testSuites = this.filterTestSuites(result.testSuites, this.options.testSuiteFilter);
    }
    
    // Apply performance threshold overrides
    if (this.options.performanceOverrides) {
      result.performanceThresholds = {
        ...result.performanceThresholds,
        ...this.options.performanceOverrides,
      };
    }
    
    // Apply accessibility overrides
    if (this.options.accessibilityOverrides) {
      if (this.options.accessibilityOverrides.level) {
        result.accessibilityLevel = this.options.accessibilityOverrides.level;
      }
    }
    
    // Apply custom thresholds
    if (this.options.customThresholds) {
      // Custom thresholds can be applied to various parts of the config
      Object.entries(this.options.customThresholds).forEach(([key, value]) => {
        if (key.startsWith('performance.')) {
          const perfKey = key.replace('performance.', '') as keyof typeof result.performanceThresholds;
          if (perfKey in result.performanceThresholds) {
            (result.performanceThresholds as any)[perfKey] = value;
          }
        }
      });
    }
    
    return result;
  }

  /**
   * Filter test suites based on filter criteria
   */
  private filterTestSuites(testSuites: TestSuiteConfig[], filter: TestSuiteFilter): TestSuiteConfig[] {
    let filtered = [...testSuites];
    
    // Filter by include list
    if (filter.include && filter.include.length > 0) {
      filtered = filtered.filter(suite => filter.include!.includes(suite.name));
    }
    
    // Filter by exclude list
    if (filter.exclude && filter.exclude.length > 0) {
      filtered = filtered.filter(suite => !filter.exclude!.includes(suite.name));
    }
    
    // Filter by types
    if (filter.types && filter.types.length > 0) {
      filtered = filtered.filter(suite => filter.types!.includes(suite.type));
    }
    
    // Filter by tags (if test suites have tags in the future)
    if (filter.tags && filter.tags.length > 0) {
      // This would require extending TestSuiteConfig to include tags
      // For now, we'll skip this filter
    }
    
    return filtered;
  }

  /**
   * Save configuration to file
   */
  async saveToFile(filePath: string): Promise<void> {
    try {
      const fs = await import('fs/promises');
      const path = await import('path');
      
      // Ensure directory exists
      const dir = path.dirname(filePath);
      await fs.mkdir(dir, { recursive: true });
      
      // Write config file
      await fs.writeFile(filePath, JSON.stringify(this.config, null, 2));
    } catch (error) {
      throw new Error(`Failed to save config to ${filePath}: ${error}`);
    }
  }

  /**
   * Merge configurations with deep merge for nested objects
   */
  private mergeConfig(base: VerificationConfig, override: Partial<VerificationConfig>): VerificationConfig {
    const merged = { ...base };

    if (override.buildSettings) {
      merged.buildSettings = { ...base.buildSettings, ...override.buildSettings };
    }

    if (override.performanceThresholds) {
      merged.performanceThresholds = { ...base.performanceThresholds, ...override.performanceThresholds };
    }

    if (override.accessibilityLevel) {
      merged.accessibilityLevel = override.accessibilityLevel;
    }

    if (override.testSuites) {
      merged.testSuites = override.testSuites;
    }

    if (override.externalDependencies) {
      merged.externalDependencies = override.externalDependencies;
    }

    return merged;
  }
}

/**
 * Create a default configuration manager instance
 */
export const createConfigManager = (
  customConfig?: Partial<VerificationConfig>, 
  options?: ConfigurationOptions
): ConfigManager => {
  return new ConfigManager(customConfig, options);
};

/**
 * Configuration preset definitions
 */
export const CONFIG_PRESETS = {
  development: {
    buildSettings: {
      mode: 'development' as const,
      sourceMaps: true,
      minification: false,
    },
    performanceThresholds: {
      lcp: 4000, // More lenient for development
      fid: 200,
      cls: 0.2,
      lighthousePerformance: 70,
    },
  },
  staging: {
    buildSettings: {
      mode: 'production' as const,
      sourceMaps: true,
      minification: true,
    },
    performanceThresholds: {
      lcp: 3000, // Slightly more lenient than production
      fid: 150,
      cls: 0.15,
      lighthousePerformance: 85,
    },
  },
  production: {
    buildSettings: {
      mode: 'production' as const,
      sourceMaps: false,
      minification: true,
    },
    performanceThresholds: {
      lcp: 2500, // Strict production thresholds
      fid: 100,
      cls: 0.1,
      lighthousePerformance: 90,
    },
  },
  ci: {
    testSuites: [
      {
        name: 'unit-tests',
        type: 'unit' as const,
        enabled: true,
        timeout: 30000,
        retries: 1, // Fewer retries in CI
      },
      {
        name: 'integration-tests',
        type: 'integration' as const,
        enabled: true,
        timeout: 60000,
        retries: 1,
      },
      {
        name: 'e2e-tests',
        type: 'e2e' as const,
        enabled: true,
        timeout: 120000,
        retries: 2, // Some retries for flaky E2E tests
      },
    ],
  },
} as const;

/**
 * Create configuration with preset
 */
export const createConfigWithPreset = (
  preset: keyof typeof CONFIG_PRESETS,
  customConfig?: Partial<VerificationConfig>
): ConfigManager => {
  const presetConfig = CONFIG_PRESETS[preset];
  const mergedConfig = customConfig ? 
    { ...presetConfig, ...customConfig } : 
    presetConfig;
  
  return new ConfigManager(mergedConfig);
};

/**
 * Configuration validation utilities
 */
export const ConfigValidation = {
  /**
   * Validate performance thresholds
   */
  validatePerformanceThresholds(thresholds: VerificationConfig['performanceThresholds']): string[] {
    const errors: string[] = [];
    
    if (thresholds.lcp <= 0) errors.push('LCP threshold must be positive');
    if (thresholds.lcp > 10000) errors.push('LCP threshold seems too high (>10s)');
    
    if (thresholds.fid <= 0) errors.push('FID threshold must be positive');
    if (thresholds.fid > 1000) errors.push('FID threshold seems too high (>1s)');
    
    if (thresholds.cls < 0 || thresholds.cls > 1) {
      errors.push('CLS threshold must be between 0 and 1');
    }
    
    if (thresholds.lighthousePerformance < 0 || thresholds.lighthousePerformance > 100) {
      errors.push('Lighthouse performance threshold must be between 0 and 100');
    }
    
    return errors;
  },

  /**
   * Validate test suite configuration
   */
  validateTestSuite(suite: TestSuiteConfig): string[] {
    const errors: string[] = [];
    
    if (!suite.name) errors.push('Test suite must have a name');
    if (suite.timeout <= 0) errors.push(`Test suite ${suite.name} timeout must be positive`);
    if (suite.retries < 0) errors.push(`Test suite ${suite.name} retries must be non-negative`);
    if (suite.retries > 5) errors.push(`Test suite ${suite.name} retries seems excessive (>5)`);
    
    const validTypes = ['unit', 'integration', 'e2e', 'performance', 'accessibility'];
    if (!validTypes.includes(suite.type)) {
      errors.push(`Test suite ${suite.name} has invalid type: ${suite.type}`);
    }
    
    return errors;
  },

  /**
   * Validate external dependency configuration
   */
  validateExternalDependency(dep: ExternalDependency): string[] {
    const errors: string[] = [];
    
    if (!dep.name) errors.push('External dependency must have a name');
    if (!dep.url) errors.push(`External dependency ${dep.name} must have a URL`);
    if (dep.timeout <= 0) errors.push(`External dependency ${dep.name} timeout must be positive`);
    if (dep.timeout > 30000) errors.push(`External dependency ${dep.name} timeout seems too high (>30s)`);
    
    // Basic URL validation
    try {
      new URL(dep.url);
    } catch {
      errors.push(`External dependency ${dep.name} has invalid URL: ${dep.url}`);
    }
    
    return errors;
  },
};