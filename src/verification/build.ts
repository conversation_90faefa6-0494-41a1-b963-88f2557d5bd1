/**
 * Build verification engine that wraps Vite build process
 * Captures errors, warnings, and metrics for production deployment verification
 */

import { spawn } from 'child_process';
import { promises as fs } from 'fs';
import path from 'path';
import { BuildVerificationResult, BuildError, BuildWarning, ChunkInfo } from './types';
import { ErrorHandler } from './error-handling';

export class BuildVerificationEngine {
  private buildStartTime: number = 0;
  private buildEndTime: number = 0;
  private errors: BuildError[] = [];
  private warnings: BuildWarning[] = [];
  private errorHandler?: ErrorHandler;

  constructor(errorHandler?: ErrorHandler) {
    this.errorHandler = errorHandler;
  }

  /**
   * Execute build verification process
   */
  async verify(): Promise<BuildVerificationResult> {
    try {
      this.reset();
      this.buildStartTime = Date.now();

      if (this.errorHandler) {
        await this.errorHandler.getLogger().info('Starting build verification process');
      }

      // Clean previous build with error handling
      if (this.errorHandler) {
        await this.errorHandler.executeWithErrorHandling(
          () => this.cleanBuildDirectory(),
          {
            operationName: 'Clean Build Directory',
            stage: 'build',
            type: 'build',
            maxRetries: 1
          }
        );
      } else {
        await this.cleanBuildDirectory();
      }

      // Execute Vite build with error handling
      let buildSuccess: boolean;
      if (this.errorHandler) {
        buildSuccess = await this.errorHandler.executeWithErrorHandling(
          () => this.executeBuild(),
          {
            operationName: 'Execute Build',
            stage: 'build',
            type: 'build',
            timeout: 300000, // 5 minutes
            maxRetries: 1
          }
        );
      } else {
        buildSuccess = await this.executeBuild();
      }
      
      this.buildEndTime = Date.now();

      // Collect build metrics if build succeeded
      let outputSize = { total: 0, chunks: [] as ChunkInfo[] };
      if (buildSuccess) {
        if (this.errorHandler) {
          outputSize = await this.errorHandler.executeWithErrorHandling(
            () => this.collectBuildMetrics(),
            {
              operationName: 'Collect Build Metrics',
              stage: 'build',
              type: 'build',
              maxRetries: 2
            }
          );
        } else {
          outputSize = await this.collectBuildMetrics();
        }
      }

      const result = {
        success: buildSuccess && this.errors.length === 0,
        buildTime: this.buildEndTime - this.buildStartTime,
        errors: this.errors,
        warnings: this.warnings,
        outputSize,
      };

      if (this.errorHandler) {
        const logger = this.errorHandler.getLogger();
        await logger.info('Build verification completed', {
          success: result.success,
          buildTime: result.buildTime,
          errorCount: result.errors.length,
          warningCount: result.warnings.length,
          outputSizeMB: (result.outputSize.total / (1024 * 1024)).toFixed(2)
        });
      }

      return result;

    } catch (error) {
      this.buildEndTime = Date.now();
      
      // Add unexpected error to errors array
      const buildError: BuildError = {
        file: 'build-process',
        line: 0,
        column: 0,
        message: error instanceof Error ? error.message : 'Unknown build error',
        type: 'bundling',
      };
      this.errors.push(buildError);

      if (this.errorHandler) {
        const verificationError = this.errorHandler.createError(
          'build',
          'build',
          'error',
          'Build verification failed with unexpected error',
          {
            originalError: error instanceof Error ? error : undefined,
            context: { buildTime: this.buildEndTime - this.buildStartTime }
          }
        );
        await this.errorHandler.handleError(verificationError);
      }

      return {
        success: false,
        buildTime: this.buildEndTime - this.buildStartTime,
        errors: this.errors,
        warnings: this.warnings,
        outputSize: { total: 0, chunks: [] },
      };
    }
  }

  /**
   * Reset internal state for new build
   */
  private reset(): void {
    this.errors = [];
    this.warnings = [];
    this.buildStartTime = 0;
    this.buildEndTime = 0;
  }

  /**
   * Clean the build directory
   */
  private async cleanBuildDirectory(): Promise<void> {
    const distPath = path.resolve(process.cwd(), 'dist');
    try {
      await fs.rm(distPath, { recursive: true, force: true });
    } catch (error) {
      // Directory might not exist, which is fine
    }
  }

  /**
   * Execute the Vite build process
   */
  private async executeBuild(): Promise<boolean> {
    return new Promise((resolve) => {
      const buildProcess = spawn('npm', ['run', 'build'], {
        stdio: ['pipe', 'pipe', 'pipe'],
        shell: true,
      });

      let stdout = '';
      let stderr = '';

      buildProcess.stdout?.on('data', (data) => {
        stdout += data.toString();
      });

      buildProcess.stderr?.on('data', (data) => {
        stderr += data.toString();
      });

      buildProcess.on('close', (code) => {
        // Parse build output for errors and warnings
        this.parseBuildOutput(stdout, stderr);
        
        resolve(code === 0);
      });

      buildProcess.on('error', (error) => {
        this.errors.push({
          file: 'build-process',
          line: 0,
          column: 0,
          message: `Build process error: ${error.message}`,
          type: 'bundling',
        });
        resolve(false);
      });
    });
  }

  /**
   * Parse build output to extract errors and warnings
   */
  private parseBuildOutput(stdout: string, stderr: string): void {
    const output = stdout + stderr;
    const lines = output.split('\n');

    for (const line of lines) {
      // Parse TypeScript errors
      const tsError = this.parseTypeScriptError(line);
      if (tsError) {
        this.errors.push(tsError);
        continue;
      }

      // Parse TypeScript warnings
      const tsWarning = this.parseTypeScriptWarning(line);
      if (tsWarning) {
        this.warnings.push(tsWarning);
        continue;
      }

      // Parse Vite/Rollup errors
      const viteError = this.parseViteError(line);
      if (viteError) {
        this.errors.push(viteError);
        continue;
      }

      // Parse Vite warnings
      const viteWarning = this.parseViteWarning(line);
      if (viteWarning) {
        this.warnings.push(viteWarning);
        continue;
      }
    }
  }

  /**
   * Parse TypeScript error from build output
   */
  private parseTypeScriptError(line: string): BuildError | null {
    // Pattern: src/file.ts(line,column): error TS#### message
    const tsErrorPattern = /^(.+\.tsx?)\((\d+),(\d+)\):\s+error\s+TS\d+:\s+(.+)$/;
    const match = line.match(tsErrorPattern);

    if (match) {
      return {
        file: match[1],
        line: parseInt(match[2], 10),
        column: parseInt(match[3], 10),
        message: match[4],
        type: 'typescript',
      };
    }

    return null;
  }

  /**
   * Parse TypeScript warning from build output
   */
  private parseTypeScriptWarning(line: string): BuildWarning | null {
    // Pattern: src/file.ts(line,column): warning TS#### message
    const tsWarningPattern = /^(.+\.tsx?)\((\d+),(\d+)\):\s+warning\s+TS\d+:\s+(.+)$/;
    const match = line.match(tsWarningPattern);

    if (match) {
      return {
        file: match[1],
        line: parseInt(match[2], 10),
        column: parseInt(match[3], 10),
        message: match[4],
        type: 'typescript',
      };
    }

    return null;
  }

  /**
   * Parse Vite/Rollup error from build output
   */
  private parseViteError(line: string): BuildError | null {
    // Pattern: [vite:xxx] error message or [rollup] error message
    if (line.includes('[vite') || line.includes('[rollup')) {
      if (line.toLowerCase().includes('error')) {
        return {
          file: 'vite-build',
          line: 0,
          column: 0,
          message: line.trim(),
          type: 'bundling',
        };
      }
    }

    // Pattern: Error: message
    if (line.startsWith('Error:')) {
      return {
        file: 'build-process',
        line: 0,
        column: 0,
        message: line.replace('Error:', '').trim(),
        type: 'bundling',
      };
    }

    return null;
  }

  /**
   * Parse Vite warning from build output
   */
  private parseViteWarning(line: string): BuildWarning | null {
    // Pattern: warnings or large chunk size warnings
    if (line.toLowerCase().includes('warning') || line.includes('chunk size')) {
      return {
        file: 'vite-build',
        line: 0,
        column: 0,
        message: line.trim(),
        type: 'bundling',
      };
    }

    return null;
  }

  /**
   * Collect build metrics from the dist directory
   */
  private async collectBuildMetrics(): Promise<{ total: number; chunks: ChunkInfo[] }> {
    const distPath = path.resolve(process.cwd(), 'dist');
    
    try {
      const chunks = await this.analyzeChunks(distPath);
      const total = chunks.reduce((sum, chunk) => sum + chunk.size, 0);

      return { total, chunks };
    } catch (error) {
      console.warn('Failed to collect build metrics:', error);
      return { total: 0, chunks: [] };
    }
  }

  /**
   * Analyze chunks in the build output
   */
  private async analyzeChunks(distPath: string): Promise<ChunkInfo[]> {
    const chunks: ChunkInfo[] = [];
    
    try {
      const assetsPath = path.join(distPath, 'assets');
      const files = await fs.readdir(assetsPath);

      for (const file of files) {
        if (file.endsWith('.js') || file.endsWith('.css')) {
          const filePath = path.join(assetsPath, file);
          const stats = await fs.stat(filePath);
          
          // Extract chunk name (remove hash)
          const chunkName = this.extractChunkName(file);
          
          chunks.push({
            name: chunkName,
            size: stats.size,
            modules: [], // Module analysis would require more complex parsing
          });
        }
      }
    } catch (error) {
      console.warn('Failed to analyze chunks:', error);
    }

    return chunks;
  }

  /**
   * Extract chunk name from filename (remove hash)
   */
  private extractChunkName(filename: string): string {
    // Remove hash pattern like .abc123def from filename
    // Pattern matches dot followed by 8+ alphanumeric chars followed by dot
    return filename.replace(/\.[a-zA-Z0-9]{8,}(?=\.)/g, '');
  }

  /**
   * Get detailed error information for debugging
   */
  getDetailedErrors(): string[] {
    return this.errors.map(error => 
      `${error.type.toUpperCase()}: ${error.file}:${error.line}:${error.column} - ${error.message}`
    );
  }

  /**
   * Get detailed warning information
   */
  getDetailedWarnings(): string[] {
    return this.warnings.map(warning => 
      `${warning.type.toUpperCase()}: ${warning.file}:${warning.line}:${warning.column} - ${warning.message}`
    );
  }

  /**
   * Check if build meets size thresholds
   */
  checkSizeThresholds(result: BuildVerificationResult, maxTotalSize: number = 5 * 1024 * 1024): boolean {
    return result.outputSize.total <= maxTotalSize;
  }

  /**
   * Get build summary
   */
  getBuildSummary(result: BuildVerificationResult): string {
    const { success, buildTime, errors, warnings, outputSize } = result;
    const sizeInMB = (outputSize.total / (1024 * 1024)).toFixed(2);
    
    return [
      `Build ${success ? 'SUCCESS' : 'FAILED'}`,
      `Time: ${buildTime}ms`,
      `Errors: ${errors.length}`,
      `Warnings: ${warnings.length}`,
      `Output Size: ${sizeInMB}MB`,
      `Chunks: ${outputSize.chunks.length}`,
    ].join(' | ');
  }
}

/**
 * Factory function to create build verification engine
 */
export const createBuildVerificationEngine = (): BuildVerificationEngine => {
  return new BuildVerificationEngine();
};