/**
 * Accessibility validation system for production deployment verification
 * Integrates axe-core with <PERSON><PERSON> for automated WCAG 2.1 AA compliance checking
 */

import { Page, Browser, chromium } from '@playwright/test';
import { AxeBuilder } from '@axe-core/playwright';
import type { AxeResults } from 'axe-core';
import { AccessibilityResult, A11yViolation, A11yWarning, TestResult } from './types';
import { ConfigManager } from './config';

export interface AccessibilityTestConfig {
  baseUrl: string;
  pages: string[];
  wcagLevel: 'A' | 'AA' | 'AAA';
  includeRules?: string[];
  excludeRules?: string[];
  timeout: number;
  retries: number;
  keyboardNavigation: boolean;
  colorContrast: boolean;
  screenReader: boolean;
}

export interface KeyboardNavigationResult {
  passed: boolean;
  focusableElements: number;
  tabOrder: string[];
  issues: string[];
}

export interface ColorContrastResult {
  passed: boolean;
  violations: Array<{
    element: string;
    foreground: string;
    background: string;
    ratio: number;
    expected: number;
  }>;
}

export interface ScreenReaderResult {
  passed: boolean;
  issues: Array<{
    element: string;
    issue: string;
    severity: 'error' | 'warning';
  }>;
}

export class AccessibilityValidator {
  private config: AccessibilityTestConfig;
  private browser: Browser | null = null;

  constructor(config: Partial<AccessibilityTestConfig> = {}) {
    this.config = {
      baseUrl: 'http://localhost:4183',
      pages: ['/', '/team-sales', '/harbor-city'],
      wcagLevel: 'AA',
      timeout: 30000,
      retries: 2,
      keyboardNavigation: true,
      colorContrast: true,
      screenReader: true,
      ...config,
    };
  }

  /**
   * Run comprehensive accessibility validation
   */
  async validate(): Promise<AccessibilityResult> {
    try {
      this.browser = await chromium.launch({ headless: true });
      
      const results: AccessibilityResult = {
        compliant: true,
        violations: [],
        warnings: [],
        testedPages: [],
      };

      // Test each page
      for (const pagePath of this.config.pages) {
        const pageResult = await this.validatePage(pagePath);
        
        results.testedPages.push(pagePath);
        results.violations.push(...pageResult.violations);
        results.warnings.push(...pageResult.warnings);
        
        if (!pageResult.compliant) {
          results.compliant = false;
        }
      }

      return results;
    } finally {
      if (this.browser) {
        await this.browser.close();
        this.browser = null;
      }
    }
  }

  /**
   * Validate accessibility for a single page
   */
  private async validatePage(pagePath: string): Promise<AccessibilityResult> {
    if (!this.browser) {
      throw new Error('Browser not initialized');
    }

    const page = await this.browser.newPage();
    const url = `${this.config.baseUrl}${pagePath}`;
    
    try {
      // Set a reasonable timeout and wait strategy
      await page.goto(url, { 
        waitUntil: 'domcontentloaded',
        timeout: this.config.timeout 
      });
      
      // Wait for dynamic content to load
      await page.waitForTimeout(2000);

      const result: AccessibilityResult = {
        compliant: true,
        violations: [],
        warnings: [],
        testedPages: [pagePath],
      };

      // Run axe-core analysis with error handling
      try {
        const axeResult = await this.runAxeAnalysis(page);
        const axeViolations = this.processAxeResults(axeResult);
        result.violations.push(...axeViolations.violations);
        result.warnings.push(...axeViolations.warnings);
      } catch (error) {
        result.warnings.push({
          rule: 'axe-core-analysis',
          element: 'page',
          description: `Axe-core analysis failed: ${error}`,
        });
      }

      // Run keyboard navigation tests
      if (this.config.keyboardNavigation) {
        try {
          const keyboardResult = await this.testKeyboardNavigation(page);
          if (!keyboardResult.passed) {
            result.violations.push(...keyboardResult.issues.map(issue => ({
              rule: 'keyboard-navigation',
              impact: 'serious' as const,
              element: 'page',
              description: issue,
            })));
          }
        } catch (error) {
          result.warnings.push({
            rule: 'keyboard-navigation',
            element: 'page',
            description: `Keyboard navigation test failed: ${error}`,
          });
        }
      }

      // Run color contrast tests
      if (this.config.colorContrast) {
        try {
          const contrastResult = await this.testColorContrast(page);
          if (!contrastResult.passed) {
            result.violations.push(...contrastResult.violations.map(violation => ({
              rule: 'color-contrast-enhanced',
              impact: 'serious' as const,
              element: violation.element,
              description: `Insufficient color contrast: ${violation.ratio.toFixed(2)}:1 (expected ${violation.expected}:1)`,
            })));
          }
        } catch (error) {
          result.warnings.push({
            rule: 'color-contrast',
            element: 'page',
            description: `Color contrast test failed: ${error}`,
          });
        }
      }

      // Run screen reader compatibility tests
      if (this.config.screenReader) {
        try {
          const screenReaderResult = await this.testScreenReaderCompatibility(page);
          if (!screenReaderResult.passed) {
            screenReaderResult.issues.forEach(issue => {
              if (issue.severity === 'error') {
                result.violations.push({
                  rule: 'screen-reader-compatibility',
                  impact: 'serious' as const,
                  element: issue.element,
                  description: issue.issue,
                });
              } else {
                result.warnings.push({
                  rule: 'screen-reader-compatibility',
                  element: issue.element,
                  description: issue.issue,
                });
              }
            });
          }
        } catch (error) {
          result.warnings.push({
            rule: 'screen-reader-compatibility',
            element: 'page',
            description: `Screen reader compatibility test failed: ${error}`,
          });
        }
      }

      result.compliant = result.violations.length === 0;
      return result;

    } catch (error) {
      // If page navigation fails, return a result with the error
      return {
        compliant: false,
        violations: [{
          rule: 'page-navigation',
          impact: 'critical' as const,
          element: 'page',
          description: `Failed to load page ${pagePath}: ${error}`,
        }],
        warnings: [],
        testedPages: [pagePath],
      };
    } finally {
      await page.close();
    }
  }

  /**
   * Run axe-core accessibility analysis
   */
  private async runAxeAnalysis(page: Page): Promise<AxeResults> {
    const tags = this.getAxeTags();
    const axeBuilder = new AxeBuilder({ page }).withTags(tags);

    // Include specific rules if configured
    if (this.config.includeRules && this.config.includeRules.length > 0) {
      axeBuilder.withRules(this.config.includeRules);
    }

    // Exclude specific rules if configured
    if (this.config.excludeRules && this.config.excludeRules.length > 0) {
      axeBuilder.disableRules(this.config.excludeRules);
    }

    return await axeBuilder.analyze();
  }

  /**
   * Get axe tags based on WCAG level
   */
  private getAxeTags(): string[] {
    const baseTags = ['wcag2a'];
    
    if (this.config.wcagLevel === 'AA' || this.config.wcagLevel === 'AAA') {
      baseTags.push('wcag2aa');
    }
    
    if (this.config.wcagLevel === 'AAA') {
      baseTags.push('wcag2aaa');
    }

    return baseTags;
  }

  /**
   * Process axe results into our format
   */
  private processAxeResults(axeResults: AxeResults): { violations: A11yViolation[]; warnings: A11yWarning[] } {
    const violations: A11yViolation[] = [];
    const warnings: A11yWarning[] = [];

    // Process violations
    axeResults.violations.forEach((violation) => {
      violation.nodes.forEach((node) => {
        violations.push({
          rule: violation.id,
          impact: violation.impact || 'moderate', // Default to moderate if impact is undefined
          element: node.target.join(', '),
          description: `${violation.description}. ${violation.help}. More info: ${violation.helpUrl}`,
        });
      });
    });

    // Process incomplete results as warnings (if they exist)
    if ('incomplete' in axeResults && Array.isArray(axeResults.incomplete)) {
      (axeResults.incomplete as any[]).forEach((incomplete: any) => {
        if (incomplete.nodes && Array.isArray(incomplete.nodes)) {
          incomplete.nodes.forEach((node: any) => {
            warnings.push({
              rule: incomplete.id,
              element: node.target.join(', '),
              description: `${incomplete.description}. Manual review needed: ${incomplete.help}`,
            });
          });
        }
      });
    }

    return { violations, warnings };
  }

  /**
   * Test keyboard navigation functionality
   */
  private async testKeyboardNavigation(page: Page): Promise<KeyboardNavigationResult> {
    const result: KeyboardNavigationResult = {
      passed: true,
      focusableElements: 0,
      tabOrder: [],
      issues: [],
    };

    try {
      // Get all focusable elements
      const focusableElements = await page.locator(
        'a, button, input, select, textarea, [tabindex]:not([tabindex="-1"])'
      ).all();

      result.focusableElements = focusableElements.length;

      if (focusableElements.length === 0) {
        result.issues.push('No focusable elements found on the page');
        result.passed = false;
        return result;
      }

      // Test tab navigation
      await page.keyboard.press('Tab');
      
      const maxTabTests = Math.min(10, focusableElements.length);
      
      for (let i = 0; i < maxTabTests; i++) {
        const activeElement = await page.evaluate(() => {
          const active = document.activeElement;
          const isElementVisible = (element: HTMLElement): boolean => {
            const rect = element.getBoundingClientRect();
            const style = window.getComputedStyle(element);
            
            return rect.width > 0 && 
                   rect.height > 0 && 
                   style.visibility !== 'hidden' && 
                   style.display !== 'none' &&
                   style.opacity !== '0';
          };
          
          return {
            tagName: active?.tagName,
            id: active?.id,
            className: active?.className,
            visible: active ? isElementVisible(active as HTMLElement) : false,
          };
        });

        if (!activeElement.tagName) {
          result.issues.push(`Tab ${i + 1}: No element focused`);
          result.passed = false;
        } else if (!activeElement.visible) {
          result.issues.push(`Tab ${i + 1}: Focused element is not visible`);
          result.passed = false;
        } else {
          result.tabOrder.push(
            `${activeElement.tagName}${activeElement.id ? `#${activeElement.id}` : ''}`
          );
        }

        if (i < maxTabTests - 1) {
          await page.keyboard.press('Tab');
        }
      }

      // Test reverse navigation
      await page.keyboard.press('Shift+Tab');
      const reverseFocused = await page.evaluate(() => document.activeElement?.tagName);
      
      if (!reverseFocused) {
        result.issues.push('Reverse tab navigation (Shift+Tab) does not work');
        result.passed = false;
      }

      // Test Enter/Space activation on buttons
      const buttons = await page.locator('button, [role="button"]').all();
      for (let i = 0; i < Math.min(3, buttons.length); i++) {
        const button = buttons[i];
        await button.focus();
        
        const hasAccessibleName = await button.evaluate(el => {
          return !!(
            el.getAttribute('aria-label') ||
            el.textContent?.trim() ||
            el.getAttribute('title') ||
            el.getAttribute('aria-labelledby')
          );
        });

        if (!hasAccessibleName) {
          result.issues.push(`Button ${i + 1} lacks accessible name`);
          result.passed = false;
        }
      }

    } catch (error) {
      result.issues.push(`Keyboard navigation test failed: ${error}`);
      result.passed = false;
    }

    return result;
  }

  /**
   * Test color contrast ratios
   */
  private async testColorContrast(page: Page): Promise<ColorContrastResult> {
    const result: ColorContrastResult = {
      passed: true,
      violations: [],
    };

    try {
      // Get text elements and check their contrast
      const textElements = await page.locator('p, h1, h2, h3, h4, h5, h6, span, div, a, button').all();
      
      for (let i = 0; i < Math.min(20, textElements.length); i++) {
        const element = textElements[i];
        
        const contrastInfo = await element.evaluate((el) => {
          const styles = window.getComputedStyle(el);
          const text = el.textContent?.trim();
          
          if (!text || text.length === 0) return null;

          const color = styles.color;
          const backgroundColor = styles.backgroundColor;
          const fontSize = parseFloat(styles.fontSize);
          const fontWeight = styles.fontWeight;
          
          return {
            color,
            backgroundColor,
            fontSize,
            fontWeight,
            text: text.substring(0, 50),
            selector: el.tagName.toLowerCase() + (el.id ? `#${el.id}` : '') + (el.className ? `.${el.className.split(' ')[0]}` : ''),
          };
        });

        if (contrastInfo && contrastInfo.color && contrastInfo.backgroundColor) {
          const ratio = this.calculateContrastRatio(contrastInfo.color, contrastInfo.backgroundColor);
          const isLargeText = contrastInfo.fontSize >= 18 || 
                            (contrastInfo.fontSize >= 14 && (contrastInfo.fontWeight === 'bold' || parseInt(contrastInfo.fontWeight) >= 700));
          
          const requiredRatio = this.config.wcagLevel === 'AAA' ? (isLargeText ? 4.5 : 7) : (isLargeText ? 3 : 4.5);
          
          if (ratio < requiredRatio) {
            result.violations.push({
              element: contrastInfo.selector,
              foreground: contrastInfo.color,
              background: contrastInfo.backgroundColor,
              ratio,
              expected: requiredRatio,
            });
            result.passed = false;
          }
        }
      }
    } catch (error) {
      console.warn('Color contrast test failed:', error);
    }

    return result;
  }

  /**
   * Test screen reader compatibility
   */
  private async testScreenReaderCompatibility(page: Page): Promise<ScreenReaderResult> {
    const result: ScreenReaderResult = {
      passed: true,
      issues: [],
    };

    try {
      // Check for proper heading hierarchy
      const headings = await page.locator('h1, h2, h3, h4, h5, h6').all();
      const headingLevels: number[] = [];

      for (const heading of headings) {
        const level = await heading.evaluate(el => parseInt(el.tagName.charAt(1)));
        headingLevels.push(level);
      }

      if (headingLevels.length > 0) {
        if (headingLevels[0] !== 1) {
          result.issues.push({
            element: 'page',
            issue: 'Page should start with h1 heading',
            severity: 'error',
          });
          result.passed = false;
        }

        // Check for skipped heading levels
        for (let i = 1; i < headingLevels.length; i++) {
          if (headingLevels[i] - headingLevels[i - 1] > 1) {
            result.issues.push({
              element: `h${headingLevels[i]}`,
              issue: `Heading level skipped from h${headingLevels[i - 1]} to h${headingLevels[i]}`,
              severity: 'warning',
            });
          }
        }
      }

      // Check for proper landmarks
      const landmarks = {
        main: await page.locator('main, [role="main"]').count(),
        nav: await page.locator('nav, [role="navigation"]').count(),
        banner: await page.locator('header, [role="banner"]').count(),
        contentinfo: await page.locator('footer, [role="contentinfo"]').count(),
      };

      if (landmarks.main === 0) {
        result.issues.push({
          element: 'page',
          issue: 'Page should have a main landmark',
          severity: 'error',
        });
        result.passed = false;
      }

      if (landmarks.main > 1) {
        result.issues.push({
          element: 'main',
          issue: 'Page should have only one main landmark',
          severity: 'error',
        });
        result.passed = false;
      }

      // Check form labels
      const inputs = await page.locator('input[type="text"], input[type="email"], input[type="tel"], textarea').all();
      
      for (let i = 0; i < inputs.length; i++) {
        const input = inputs[i];
        const hasLabel = await input.evaluate(el => {
          const id = el.id;
          const ariaLabel = el.getAttribute('aria-label');
          const ariaLabelledBy = el.getAttribute('aria-labelledby');
          const hasAssociatedLabel = id ? document.querySelector(`label[for="${id}"]`) : null;
          
          return !!(ariaLabel || ariaLabelledBy || hasAssociatedLabel);
        });

        if (!hasLabel) {
          result.issues.push({
            element: `input[${i}]`,
            issue: 'Form input lacks proper label',
            severity: 'error',
          });
          result.passed = false;
        }
      }

      // Check image alt text
      const images = await page.locator('img').all();
      
      for (let i = 0; i < images.length; i++) {
        const img = images[i];
        const altInfo = await img.evaluate(el => ({
          alt: el.getAttribute('alt'),
          src: el.getAttribute('src'),
          role: el.getAttribute('role'),
        }));

        if (altInfo.alt === null && altInfo.role !== 'presentation') {
          result.issues.push({
            element: `img[${i}]`,
            issue: 'Image lacks alt attribute',
            severity: 'error',
          });
          result.passed = false;
        }
      }

    } catch (error) {
      result.issues.push({
        element: 'page',
        issue: `Screen reader compatibility test failed: ${error}`,
        severity: 'error',
      });
      result.passed = false;
    }

    return result;
  }

  /**
   * Calculate color contrast ratio between two colors
   */
  private calculateContrastRatio(foreground: string, background: string): number {
    try {
      const fgLuminance = this.getRelativeLuminance(foreground);
      const bgLuminance = this.getRelativeLuminance(background);
      
      const lighter = Math.max(fgLuminance, bgLuminance);
      const darker = Math.min(fgLuminance, bgLuminance);
      
      return (lighter + 0.05) / (darker + 0.05);
    } catch (error) {
      // Return a safe default ratio if calculation fails
      return 4.5;
    }
  }

  /**
   * Get relative luminance of a color
   */
  private getRelativeLuminance(color: string): number {
    try {
      // Parse RGB values from color string
      let r = 0, g = 0, b = 0;
      
      if (color.startsWith('rgb(') || color.startsWith('rgba(')) {
        const matches = color.match(/\d+/g);
        if (matches && matches.length >= 3) {
          r = parseInt(matches[0]) / 255;
          g = parseInt(matches[1]) / 255;
          b = parseInt(matches[2]) / 255;
        }
      } else if (color.startsWith('#')) {
        // Handle hex colors
        const hex = color.slice(1);
        if (hex.length === 3) {
          r = parseInt(hex[0] + hex[0], 16) / 255;
          g = parseInt(hex[1] + hex[1], 16) / 255;
          b = parseInt(hex[2] + hex[2], 16) / 255;
        } else if (hex.length === 6) {
          r = parseInt(hex.slice(0, 2), 16) / 255;
          g = parseInt(hex.slice(2, 4), 16) / 255;
          b = parseInt(hex.slice(4, 6), 16) / 255;
        }
      } else {
        // Handle named colors (simplified)
        const namedColors: Record<string, [number, number, number]> = {
          'black': [0, 0, 0],
          'white': [255, 255, 255],
          'red': [255, 0, 0],
          'green': [0, 128, 0],
          'blue': [0, 0, 255],
          'transparent': [255, 255, 255], // Treat as white
        };
        
        const rgb = namedColors[color.toLowerCase()];
        if (rgb) {
          r = rgb[0] / 255;
          g = rgb[1] / 255;
          b = rgb[2] / 255;
        }
      }
      
      // Apply gamma correction
      const gammaCorrect = (c: number) => {
        return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
      };
      
      r = gammaCorrect(r);
      g = gammaCorrect(g);
      b = gammaCorrect(b);
      
      // Calculate relative luminance
      return 0.2126 * r + 0.7152 * g + 0.0722 * b;
    } catch (error) {
      // Return default luminance if parsing fails
      return 0.5;
    }
  }

  /**
   * Generate accessibility report with remediation guidance
   */
  generateReport(result: AccessibilityResult): string {
    const report = [];
    
    report.push('# Accessibility Validation Report');
    report.push(`Generated: ${new Date().toISOString()}`);
    report.push(`Overall Status: ${result.compliant ? '✅ PASSED' : '❌ FAILED'}`);
    report.push(`Pages Tested: ${result.testedPages.join(', ')}`);
    report.push('');

    if (result.violations.length > 0) {
      report.push('## Violations (Must Fix)');
      result.violations.forEach((violation, index) => {
        report.push(`### ${index + 1}. ${violation.rule} (${violation.impact})`);
        report.push(`**Element:** ${violation.element}`);
        report.push(`**Issue:** ${violation.description}`);
        report.push(`**Remediation:** ${this.getRemediationGuidance(violation.rule)}`);
        report.push('');
      });
    }

    if (result.warnings.length > 0) {
      report.push('## Warnings (Should Fix)');
      result.warnings.forEach((warning, index) => {
        report.push(`### ${index + 1}. ${warning.rule}`);
        report.push(`**Element:** ${warning.element}`);
        report.push(`**Issue:** ${warning.description}`);
        report.push('');
      });
    }

    if (result.violations.length === 0 && result.warnings.length === 0) {
      report.push('## ✅ All Tests Passed');
      report.push('No accessibility violations or warnings found.');
    }

    return report.join('\n');
  }

  /**
   * Get remediation guidance for specific accessibility rules
   */
  private getRemediationGuidance(rule: string): string {
    const guidance: Record<string, string> = {
      // Color and contrast
      'color-contrast': 'Ensure text has sufficient contrast ratio (4.5:1 for normal text, 3:1 for large text). Use tools like WebAIM Contrast Checker.',
      'color-contrast-enhanced': 'For AAA compliance, use higher contrast ratios (7:1 for normal text, 4.5:1 for large text).',
      
      // Images and media
      'image-alt': 'Add descriptive alt text to images. Use alt="" for decorative images. Alt text should convey the purpose and content of the image.',
      'input-image-alt': 'Image inputs (like submit buttons) must have alt text describing their function.',
      
      // Forms and labels
      'label': 'Associate form inputs with labels using for/id attributes, aria-label, or aria-labelledby. Every form control needs an accessible name.',
      'form-field-multiple-labels': 'Each form field should have only one label. Remove duplicate labels or use aria-describedby for additional information.',
      
      // Interactive elements
      'button-name': 'Ensure buttons have accessible names via text content, aria-label, or aria-labelledby. Button purpose should be clear.',
      'link-name': 'Provide descriptive link text or use aria-label. Avoid generic text like "click here" or "read more".',
      
      // Document structure
      'heading-order': 'Use headings in logical order (h1, h2, h3) without skipping levels. Headings create document outline for screen readers.',
      'page-has-heading-one': 'Include exactly one h1 heading per page. This serves as the main page title for screen readers.',
      'landmark-one-main': 'Use only one main landmark per page. The main element identifies the primary content area.',
      
      // ARIA and semantics
      'region': 'Use semantic HTML5 elements (main, nav, header, footer) or ARIA landmarks to define page regions.',
      'aria-valid-attr': 'Use only valid ARIA attributes. Check ARIA specification for correct attribute names.',
      'aria-valid-attr-value': 'ARIA attribute values must be valid. Check allowed values for each ARIA attribute.',
      'aria-required-attr': 'Include required ARIA attributes for ARIA roles. Some roles require specific attributes.',
      
      // Language and internationalization
      'html-has-lang': 'Add lang attribute to html element (e.g., <html lang="en">). This helps screen readers pronounce content correctly.',
      'html-lang-valid': 'Use valid language codes in lang attributes. Follow BCP 47 specification (e.g., "en", "en-US").',
      
      // Custom tests
      'keyboard-navigation': 'Ensure all interactive elements are keyboard accessible with Tab/Shift+Tab. Provide visible focus indicators.',
      'screen-reader-compatibility': 'Use semantic HTML and proper ARIA attributes. Test with screen readers like NVDA or VoiceOver.',
      'page-navigation': 'Ensure page loads correctly and all resources are accessible. Check for network issues or server errors.',
      
      // Focus management
      'focus-order-semantics': 'Ensure focus order follows logical reading order. Use tabindex carefully and prefer semantic HTML.',
      'tabindex': 'Avoid positive tabindex values. Use tabindex="0" to add elements to tab order, tabindex="-1" to remove them.',
      
      // Tables
      'table-fake-caption': 'Use proper table captions with <caption> element instead of fake captions.',
      'th-has-data-cells': 'Table headers (th) should be associated with data cells using scope or headers attributes.',
      
      // Lists
      'list': 'Use proper list markup (ul, ol, dl) for lists. Don\'t use list styling on non-list content.',
      'listitem': 'List items (li) should only be direct children of ul, ol, or menu elements.',
      
      // Multimedia
      'audio-caption': 'Provide captions for audio content. Use <track> elements or provide transcripts.',
      'video-caption': 'Provide captions for video content. Use <track> elements with captions.',
      
      // Mobile and responsive
      'meta-viewport': 'Include proper viewport meta tag for responsive design: <meta name="viewport" content="width=device-width, initial-scale=1">',
      'target-size': 'Ensure interactive elements are at least 44x44 pixels for touch accessibility.',
    };

    return guidance[rule] || 'Review WCAG 2.1 guidelines for this rule and implement appropriate fixes. Visit https://www.w3.org/WAI/WCAG21/quickref/ for detailed guidance.';
  }
}

/**
 * Create accessibility test suite for the verification pipeline
 */
export class AccessibilityTestSuite {
  private validator: AccessibilityValidator;

  constructor(config: ConfigManager) {
    this.validator = new AccessibilityValidator({
      wcagLevel: config.getConfig().accessibilityLevel,
      timeout: config.getEnabledTestSuites().find(s => s.type === 'accessibility')?.timeout || 90000,
    });
  }

  /**
   * Execute accessibility test suite
   */
  async execute(): Promise<TestResult> {
    const startTime = Date.now();
    
    try {
      const result = await this.validator.validate();
      const duration = Date.now() - startTime;

      return {
        passed: result.compliant,
        duration,
        testCount: result.testedPages.length,
        failures: result.violations.map(violation => ({
          testName: `${violation.rule} - ${violation.element}`,
          error: violation.description,
          duration: 0,
        })),
      };
    } catch (error) {
      const duration = Date.now() - startTime;
      
      return {
        passed: false,
        duration,
        testCount: 0,
        failures: [{
          testName: 'Accessibility validation',
          error: `Test suite failed: ${error}`,
          duration,
        }],
      };
    }
  }
}