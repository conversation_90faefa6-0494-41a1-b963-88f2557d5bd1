#!/usr/bin/env node

/**
 * Command Line Interface for Production Deployment Verification
 * 
 * This CLI provides a comprehensive interface for running the verification pipeline
 * with various options and output formats.
 */

import { program } from 'commander';
import { runVerification } from './pipeline';
import { ReportGenerator } from './reporting';
import { ConfigManager, ConfigurationOptions, DEFAULT_CONFIG, CONFIG_PRESETS } from './config';
import { VerificationConfig, PipelineOptions, VerificationReport } from './types';
import * as fs from 'fs/promises';
import * as path from 'path';
import { createRequire } from 'module';

// Version information
const require = createRequire(import.meta.url);
const packageJson = require('../../package.json');
const VERSION = packageJson.version || '1.0.0';

/**
 * CLI Configuration interface
 */
interface CLIOptions {
  config?: string;
  environment?: string;
  output?: 'console' | 'json' | 'html';
  outputFile?: string;
  verbose?: boolean;
  skipStages?: string[];
  watch?: boolean;
  ci?: boolean;
  failFast?: boolean;
  timeout?: number;
  testSuites?: string[];
  excludeTestSuites?: string[];
  testTypes?: string[];
  performanceThresholds?: string;
  accessibilityLevel?: 'AA' | 'AAA';
  customThresholds?: string;
}

/**
 * Main CLI class that handles command parsing and execution
 */
class VerificationCLI {
  private program = program;

  constructor() {
    this.setupCommands();
  }

  /**
   * Set up all CLI commands and options
   */
  private setupCommands(): void {
    this.program
      .name('verify-deployment')
      .description('Production deployment verification tool for Ice Box Hockey website')
      .version(VERSION);

    // Main verification command
    this.program
      .command('run')
      .description('Run the complete verification pipeline')
      .option('-c, --config <path>', 'Path to verification config file', 'verification.config.json')
      .option('-e, --environment <env>', 'Environment configuration to use (development|staging|ci|production)')
      .option('-o, --output <format>', 'Output format (console|json|html)', 'console')
      .option('-f, --output-file <path>', 'Output file path (for json/html formats)')
      .option('-v, --verbose', 'Enable verbose logging', false)
      .option('-s, --skip-stages <stages>', 'Comma-separated list of stages to skip')
      .option('--test-suites <suites>', 'Comma-separated list of test suites to include')
      .option('--exclude-test-suites <suites>', 'Comma-separated list of test suites to exclude')
      .option('--test-types <types>', 'Comma-separated list of test types to run (unit|integration|e2e|performance|accessibility)')
      .option('--performance-thresholds <json>', 'JSON string of performance threshold overrides')
      .option('--accessibility-level <level>', 'Accessibility level (AA|AAA)')
      .option('--custom-thresholds <json>', 'JSON string of custom threshold overrides')
      .option('-w, --watch', 'Watch mode - re-run on file changes', false)
      .option('--ci', 'CI mode - optimized for continuous integration', false)
      .option('--fail-fast', 'Stop on first failure', false)
      .option('-t, --timeout <seconds>', 'Global timeout in seconds', '300')
      .action(async (options: CLIOptions) => {
        await this.runVerification(options);
      });

    // Individual stage commands
    this.program
      .command('build')
      .description('Run only build verification')
      .option('-c, --config <path>', 'Path to verification config file', 'verification.config.json')
      .option('-e, --environment <env>', 'Environment configuration to use')
      .option('-v, --verbose', 'Enable verbose logging', false)
      .action(async (options: CLIOptions) => {
        await this.runVerification({ ...options, skipStages: ['tests', 'performance', 'accessibility', 'pwa', 'dependencies'] });
      });

    this.program
      .command('test')
      .description('Run only test suites')
      .option('-c, --config <path>', 'Path to verification config file', 'verification.config.json')
      .option('-e, --environment <env>', 'Environment configuration to use')
      .option('--test-suites <suites>', 'Comma-separated list of test suites to include')
      .option('--test-types <types>', 'Comma-separated list of test types to run')
      .option('-v, --verbose', 'Enable verbose logging', false)
      .action(async (options: CLIOptions) => {
        await this.runVerification({ ...options, skipStages: ['build', 'performance', 'accessibility', 'pwa', 'dependencies'] });
      });

    this.program
      .command('performance')
      .description('Run only performance tests')
      .option('-c, --config <path>', 'Path to verification config file', 'verification.config.json')
      .option('-e, --environment <env>', 'Environment configuration to use')
      .option('--performance-thresholds <json>', 'JSON string of performance threshold overrides')
      .option('-v, --verbose', 'Enable verbose logging', false)
      .action(async (options: CLIOptions) => {
        await this.runVerification({ ...options, skipStages: ['build', 'tests', 'accessibility', 'pwa', 'dependencies'] });
      });

    this.program
      .command('accessibility')
      .description('Run only accessibility tests')
      .option('-c, --config <path>', 'Path to verification config file', 'verification.config.json')
      .option('-e, --environment <env>', 'Environment configuration to use')
      .option('--accessibility-level <level>', 'Accessibility level (AA|AAA)')
      .option('-v, --verbose', 'Enable verbose logging', false)
      .action(async (options: CLIOptions) => {
        await this.runVerification({ ...options, skipStages: ['build', 'tests', 'performance', 'pwa', 'dependencies'] });
      });

    // Configuration commands
    this.program
      .command('init')
      .description('Initialize verification configuration')
      .option('-f, --force', 'Overwrite existing configuration', false)
      .option('--preset <preset>', 'Use configuration preset (development|staging|ci|production)')
      .action(async (options: { force?: boolean; preset?: string }) => {
        await this.initializeConfig(options.force || false, options.preset);
      });

    this.program
      .command('validate-config')
      .description('Validate verification configuration')
      .option('-c, --config <path>', 'Path to verification config file', 'verification.config.json')
      .option('-e, --environment <env>', 'Environment to validate')
      .action(async (options: CLIOptions) => {
        await this.validateConfig(options.config || 'verification.config.json', options.environment);
      });

    this.program
      .command('list-environments')
      .description('List available environments in configuration')
      .option('-c, --config <path>', 'Path to verification config file', 'verification.config.json')
      .action(async (options: CLIOptions) => {
        await this.listEnvironments(options.config || 'verification.config.json');
      });

    this.program
      .command('show-config')
      .description('Show resolved configuration for an environment')
      .option('-c, --config <path>', 'Path to verification config file', 'verification.config.json')
      .option('-e, --environment <env>', 'Environment to show')
      .action(async (options: CLIOptions) => {
        await this.showConfig(options.config || 'verification.config.json', options.environment);
      });

    // Reporting commands
    this.program
      .command('report')
      .description('Generate report from previous verification results')
      .option('-i, --input <path>', 'Input JSON report file', 'verification-report.json')
      .option('-o, --output <format>', 'Output format (html|json)', 'html')
      .option('-f, --output-file <path>', 'Output file path')
      .action(async (options: { input?: string; output?: string; outputFile?: string }) => {
        await this.generateReport(options);
      });

    // CI/CD integration commands
    this.program
      .command('ci-check')
      .description('CI/CD integration check - returns appropriate exit codes')
      .option('-c, --config <path>', 'Path to verification config file', 'verification.config.json')
      .option('-f, --output-file <path>', 'Output file path for CI artifacts')
      .action(async (options: CLIOptions) => {
        await this.ciCheck(options);
      });

    // Utility commands
    this.program
      .command('list-stages')
      .description('List all available verification stages')
      .action(() => {
        this.listStages();
      });

    this.program
      .command('health-check')
      .description('Check if all dependencies are available')
      .action(async () => {
        await this.healthCheck();
      });
  }

  /**
   * Run the verification pipeline with given options
   */
  private async runVerification(options: CLIOptions): Promise<void> {
    try {
      console.log('🚀 Starting production deployment verification...\n');

      // Parse configuration options
      const configOptions = this.parseConfigurationOptions(options);

      // Load configuration
      const config = await this.loadConfig(options.config || 'verification.config.json', configOptions);
      
      // Parse skip stages
      const skipStages = options.skipStages ? options.skipStages.split(',').map(s => s.trim()) : [];

      // Set up pipeline options
      const pipelineOptions: PipelineOptions = {
        config,
        skipStages,
        verbose: options.verbose || false,
        outputFormat: options.output || 'console',
      };

      // Set timeout if specified
      if (options.timeout) {
        const timeoutMs = parseInt(options.timeout) * 1000;
        setTimeout(() => {
          console.error('❌ Verification timed out');
          process.exit(1);
        }, timeoutMs);
      }

      // Run verification
      const report = await runVerification(pipelineOptions);

      // Handle output
      await this.handleOutput(report, options);

      // Set exit code based on results
      if (options.ci || options.failFast) {
        if (report.overallStatus === 'failed') {
          console.error('\n❌ Verification failed - not ready for deployment');
          
          // Show error recovery suggestions if available
          if (report.recommendations && report.recommendations.length > 0) {
            console.error('\n💡 Recovery suggestions:');
            report.recommendations.forEach(rec => {
              console.error(`   • ${rec}`);
            });
          }
          
          process.exit(1);
        } else if (report.overallStatus === 'warning') {
          console.warn('\n⚠️  Verification completed with warnings');
          
          // Show warnings and recommendations
          if (report.recommendations && report.recommendations.length > 0) {
            console.warn('\n💡 Recommendations:');
            report.recommendations.forEach(rec => {
              console.warn(`   • ${rec}`);
            });
          }
          
          process.exit(0); // Don't fail CI on warnings unless specified
        } else {
          console.log('\n✅ Verification passed - ready for deployment');
          process.exit(0);
        }
      }

    } catch (error) {
      console.error('❌ Verification failed with error:', error instanceof Error ? error.message : String(error));
      
      // Show recovery actions if available from error handling system
      if (error instanceof Error && (error as any).recoveryActions) {
        console.error('\n💡 Recovery suggestions:');
        (error as any).recoveryActions.forEach((action: string) => {
          console.error(`   • ${action}`);
        });
      }
      
      if (options.verbose && error instanceof Error) {
        console.error('\nStack trace:', error.stack);
        
        // Show verification error details if available
        if ((error as any).verificationError) {
          const verificationError = (error as any).verificationError;
          console.error('\nVerification Error Details:');
          console.error(`   Stage: ${verificationError.stage}`);
          console.error(`   Type: ${verificationError.type}`);
          console.error(`   Severity: ${verificationError.severity}`);
          if (verificationError.context) {
            console.error(`   Context: ${JSON.stringify(verificationError.context, null, 2)}`);
          }
        }
      }
      
      process.exit(1);
    }
  }

  /**
   * Handle output formatting and file writing
   */
  private async handleOutput(report: VerificationReport, options: CLIOptions): Promise<void> {
    const reportGenerator = new ReportGenerator();

    switch (options.output) {
      case 'json':
        const jsonReport = reportGenerator.generateJSON(report);
        if (options.outputFile) {
          await fs.writeFile(options.outputFile, jsonReport);
          console.log(`📄 JSON report saved to: ${options.outputFile}`);
        } else {
          console.log(jsonReport);
        }
        break;

      case 'html':
        const htmlReport = reportGenerator.generateHTML(report);
        const htmlFile = options.outputFile || `verification-report-${new Date().toISOString().replace(/[:.]/g, '-')}.html`;
        await fs.writeFile(htmlFile, htmlReport);
        console.log(`📄 HTML report saved to: ${htmlFile}`);
        break;

      case 'console':
      default:
        this.printConsoleReport(report);
        break;
    }
  }

  /**
   * Print formatted console report
   */
  private printConsoleReport(report: VerificationReport): void {
    console.log('\n' + '='.repeat(60));
    console.log('📊 VERIFICATION REPORT');
    console.log('='.repeat(60));
    
    // Overall status
    const statusIcon = report.overallStatus === 'passed' ? '✅' : 
                      report.overallStatus === 'warning' ? '⚠️' : '❌';
    console.log(`\n${statusIcon} Overall Status: ${report.overallStatus.toUpperCase()}`);
    console.log(`🚀 Deployment Ready: ${report.deploymentReady ? 'YES' : 'NO'}`);
    console.log(`📅 Timestamp: ${report.timestamp.toISOString()}`);

    // Build verification
    console.log('\n🔨 Build Verification:');
    console.log(`   Status: ${report.buildVerification.success ? '✅ PASSED' : '❌ FAILED'}`);
    console.log(`   Build Time: ${report.buildVerification.buildTime}ms`);
    console.log(`   Output Size: ${(report.buildVerification.outputSize.total / 1024 / 1024).toFixed(2)} MB`);
    if (report.buildVerification.errors.length > 0) {
      console.log(`   Errors: ${report.buildVerification.errors.length}`);
    }

    // Test results
    console.log('\n🧪 Test Results:');
    const totalTests = report.testResults.reduce((sum, result) => sum + result.testCount, 0);
    const passedSuites = report.testResults.filter(result => result.passed).length;
    console.log(`   Test Suites: ${passedSuites}/${report.testResults.length} passed`);
    console.log(`   Total Tests: ${totalTests}`);

    // Performance metrics
    console.log('\n⚡ Performance Metrics:');
    console.log(`   LCP: ${report.performanceMetrics.lcp}ms`);
    console.log(`   FID: ${report.performanceMetrics.fid}ms`);
    console.log(`   CLS: ${report.performanceMetrics.cls}`);
    console.log(`   Lighthouse Performance: ${report.performanceMetrics.lighthouse.performance}`);

    // Accessibility
    console.log('\n♿ Accessibility:');
    console.log(`   WCAG Compliant: ${report.accessibilityResults.compliant ? '✅ YES' : '❌ NO'}`);
    console.log(`   Violations: ${report.accessibilityResults.violations.length}`);
    console.log(`   Pages Tested: ${report.accessibilityResults.testedPages.length}`);

    // PWA
    console.log('\n📱 PWA Validation:');
    console.log(`   Service Worker: ${report.pwaValidation.serviceWorkerRegistered ? '✅' : '❌'}`);
    console.log(`   Manifest Valid: ${report.pwaValidation.manifestValid ? '✅' : '❌'}`);
    console.log(`   Installable: ${report.pwaValidation.installable ? '✅' : '❌'}`);

    // External dependencies
    console.log('\n🌐 External Dependencies:');
    console.log(`   Google Maps: ${report.dependencyStatus.googleMaps.available ? '✅' : '❌'} (${report.dependencyStatus.googleMaps.responseTime}ms)`);

    // Recommendations
    if (report.recommendations.length > 0) {
      console.log('\n💡 Recommendations:');
      report.recommendations.forEach(rec => {
        console.log(`   • ${rec}`);
      });
    }

    console.log('\n' + '='.repeat(60));
  }

  /**
   * Parse configuration options from CLI arguments
   */
  private parseConfigurationOptions(options: CLIOptions): ConfigurationOptions {
    const configOptions: ConfigurationOptions = {};

    // Environment
    if (options.environment) {
      configOptions.environment = options.environment;
    }

    // Test suite filtering
    if (options.testSuites || options.excludeTestSuites || options.testTypes) {
      configOptions.testSuiteFilter = {};
      
      if (options.testSuites) {
        configOptions.testSuiteFilter.include = options.testSuites.split(',').map(s => s.trim());
      }
      
      if (options.excludeTestSuites) {
        configOptions.testSuiteFilter.exclude = options.excludeTestSuites.split(',').map(s => s.trim());
      }
      
      if (options.testTypes) {
        const types = options.testTypes.split(',').map(s => s.trim()) as any[];
        configOptions.testSuiteFilter.types = types;
      }
    }

    // Performance threshold overrides
    if (options.performanceThresholds) {
      try {
        configOptions.performanceOverrides = JSON.parse(options.performanceThresholds);
      } catch (error) {
        console.error('❌ Invalid performance thresholds JSON:', error);
        process.exit(1);
      }
    }

    // Accessibility overrides
    if (options.accessibilityLevel) {
      configOptions.accessibilityOverrides = {
        level: options.accessibilityLevel,
      };
    }

    // Custom thresholds
    if (options.customThresholds) {
      try {
        configOptions.customThresholds = JSON.parse(options.customThresholds);
      } catch (error) {
        console.error('❌ Invalid custom thresholds JSON:', error);
        process.exit(1);
      }
    }

    return configOptions;
  }

  /**
   * Load verification configuration
   */
  private async loadConfig(configPath: string, options: ConfigurationOptions = {}): Promise<VerificationConfig> {
    try {
      const configManager = await ConfigManager.loadFromFile(configPath, options);
      return configManager.getConfig();
    } catch (error) {
      console.error(`❌ Failed to load config from ${configPath}:`, error instanceof Error ? error.message : String(error));
      console.log('💡 Run "verify-deployment init" to create a default configuration');
      process.exit(1);
    }
  }

  /**
   * Initialize default configuration
   */
  private async initializeConfig(force: boolean, preset?: string): Promise<void> {
    const configPath = 'verification.config.json';
    
    try {
      // Check if config already exists
      if (!force) {
        try {
          await fs.access(configPath);
          console.log(`⚠️  Configuration file already exists at ${configPath}`);
          console.log('Use --force to overwrite');
          return;
        } catch {
          // File doesn't exist, continue
        }
      }

      // Create configuration based on preset or default
      let configToWrite: any;
      
      if (preset && CONFIG_PRESETS[preset as keyof typeof CONFIG_PRESETS]) {
        // Use preset configuration
        const presetConfig = CONFIG_PRESETS[preset as keyof typeof CONFIG_PRESETS];
        const baseConfig = { ...DEFAULT_CONFIG };
        configToWrite = {
          default: { ...baseConfig, ...presetConfig },
          environments: {
            development: {
              name: 'development',
              overrides: CONFIG_PRESETS.development,
            },
            staging: {
              name: 'staging',
              overrides: CONFIG_PRESETS.staging,
            },
            ci: {
              name: 'ci',
              overrides: CONFIG_PRESETS.ci,
            },
            production: {
              name: 'production',
              overrides: CONFIG_PRESETS.production,
            },
          },
        };
      } else {
        // Use default configuration with all environments
        configToWrite = {
          default: DEFAULT_CONFIG,
          environments: {
            development: {
              name: 'development',
              overrides: CONFIG_PRESETS.development,
            },
            staging: {
              name: 'staging',
              overrides: CONFIG_PRESETS.staging,
            },
            ci: {
              name: 'ci',
              overrides: CONFIG_PRESETS.ci,
            },
            production: {
              name: 'production',
              overrides: CONFIG_PRESETS.production,
            },
          },
        };
      }

      await fs.writeFile(configPath, JSON.stringify(configToWrite, null, 2));
      console.log(`✅ Configuration initialized at ${configPath}`);

    } catch (error) {
      console.error('❌ Failed to initialize configuration:', error instanceof Error ? error.message : String(error));
      process.exit(1);
    }
  }

  /**
   * Validate configuration file
   */
  private async validateConfig(configPath: string, environment?: string): Promise<void> {
    try {
      const configManager = await ConfigManager.loadFromFile(configPath, { environment });
      const validation = configManager.validateConfig();

      if (validation.valid) {
        console.log(`✅ Configuration is valid${environment ? ` for environment: ${environment}` : ''}`);
        
        // Show configuration summary
        const summary = configManager.getConfigSummary();
        console.log('\n📊 Configuration Summary:');
        console.log(`   Environment: ${summary.environment || 'default'}`);
        console.log(`   Test Suites: ${summary.enabledTestSuites}/${summary.totalTestSuites} enabled`);
        console.log(`   Accessibility Level: ${summary.accessibilityLevel}`);
        console.log(`   Critical Dependencies: ${summary.criticalDependencies}`);
        console.log(`   Performance Thresholds:`);
        console.log(`     LCP: ${summary.performanceThresholds.lcp}ms`);
        console.log(`     FID: ${summary.performanceThresholds.fid}ms`);
        console.log(`     CLS: ${summary.performanceThresholds.cls}`);
        console.log(`     Lighthouse: ${summary.performanceThresholds.lighthousePerformance}`);
      } else {
        console.log(`❌ Configuration validation failed${environment ? ` for environment: ${environment}` : ''}:`);
        validation.errors.forEach(error => {
          console.log(`   • ${error}`);
        });
        process.exit(1);
      }

    } catch (error) {
      console.error('❌ Failed to validate configuration:', error instanceof Error ? error.message : String(error));
      process.exit(1);
    }
  }

  /**
   * List available environments
   */
  private async listEnvironments(configPath: string): Promise<void> {
    try {
      const configManager = await ConfigManager.loadFromFile(configPath);
      const environments = configManager.getAvailableEnvironments();
      
      console.log('📋 Available Environments:');
      environments.forEach(env => {
        const current = env === configManager.getCurrentEnvironment() ? ' (current)' : '';
        console.log(`   • ${env}${current}`);
      });

    } catch (error) {
      console.error('❌ Failed to list environments:', error instanceof Error ? error.message : String(error));
      process.exit(1);
    }
  }

  /**
   * Show resolved configuration
   */
  private async showConfig(configPath: string, environment?: string): Promise<void> {
    try {
      const configManager = await ConfigManager.loadFromFile(configPath, { environment });
      const config = configManager.getConfig();
      
      console.log(`📄 Configuration${environment ? ` for environment: ${environment}` : ''}:`);
      console.log(JSON.stringify(config, null, 2));

    } catch (error) {
      console.error('❌ Failed to show configuration:', error instanceof Error ? error.message : String(error));
      process.exit(1);
    }
  }

  /**
   * Generate report from existing results
   */
  private async generateReport(options: { input?: string; output?: string; outputFile?: string }): Promise<void> {
    try {
      const inputFile = options.input || 'verification-report.json';
      const reportData = await fs.readFile(inputFile, 'utf-8');
      const report: VerificationReport = JSON.parse(reportData);

      const reportGenerator = new ReportGenerator();

      if (options.output === 'html') {
        const htmlReport = reportGenerator.generateHTML(report);
        const outputFile = options.outputFile || `verification-report-${new Date().toISOString().replace(/[:.]/g, '-')}.html`;
        await fs.writeFile(outputFile, htmlReport);
        console.log(`📄 HTML report generated: ${outputFile}`);
      } else {
        const jsonReport = reportGenerator.generateJSON(report);
        if (options.outputFile) {
          await fs.writeFile(options.outputFile, jsonReport);
          console.log(`📄 JSON report saved: ${options.outputFile}`);
        } else {
          console.log(jsonReport);
        }
      }

    } catch (error) {
      console.error('❌ Failed to generate report:', error instanceof Error ? error.message : String(error));
      process.exit(1);
    }
  }

  /**
   * CI/CD integration check
   */
  private async ciCheck(options: CLIOptions): Promise<void> {
    console.log('🔄 Running CI/CD verification check...');
    
    const ciOptions: CLIOptions = {
      ...options,
      ci: true,
      output: 'json',
      outputFile: options.outputFile || 'ci-verification-report.json',
      verbose: false,
    };

    await this.runVerification(ciOptions);
  }

  /**
   * List all available verification stages
   */
  private listStages(): void {
    console.log('📋 Available Verification Stages:');
    console.log('');
    console.log('  build         - TypeScript compilation and asset bundling');
    console.log('  tests         - Unit, integration, and E2E tests');
    console.log('  performance   - Core Web Vitals and Lighthouse metrics');
    console.log('  accessibility - WCAG 2.1 AA compliance testing');
    console.log('  pwa          - Progressive Web App validation');
    console.log('  dependencies - External service availability checks');
    console.log('');
    console.log('Use --skip-stages to exclude specific stages from verification.');
  }

  /**
   * Health check for dependencies
   */
  private async healthCheck(): Promise<void> {
    console.log('🏥 Running health check...');
    
    const checks = [
      { name: 'Node.js', check: () => process.version },
      { name: 'npm', check: async () => {
        const { execSync } = await import('child_process');
        return execSync('npm --version', { encoding: 'utf-8' }).trim();
      }},
      { name: 'Playwright', check: async () => {
        try {
          await import('@playwright/test');
          return 'Available';
        } catch {
          return 'Not installed';
        }
      }},
      { name: 'Jest', check: async () => {
        try {
          await import('jest');
          return 'Available';
        } catch {
          return 'Not installed';
        }
      }},
    ];

    for (const check of checks) {
      try {
        const result = await check.check();
        console.log(`✅ ${check.name}: ${result}`);
      } catch (error) {
        console.log(`❌ ${check.name}: ${error instanceof Error ? error.message : 'Failed'}`);
      }
    }
  }

  /**
   * Start the CLI
   */
  public async run(): Promise<void> {
    await this.program.parseAsync();
  }
}

// Export for programmatic use
export { VerificationCLI };

// Run CLI if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const cli = new VerificationCLI();
  cli.run().catch(error => {
    console.error('❌ CLI Error:', error);
    process.exit(1);
  });
}