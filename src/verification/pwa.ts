/**
 * PWA Validation Module
 * 
 * Implements service worker registration testing, PWA manifest validation,
 * offline functionality testing, and PWA installation behavior testing
 */

import { <PERSON>, Browser, chromium } from 'playwright';
import { promises as fs } from 'fs';
import path from 'path';
import { PWAValidationResult, CacheValidation, TestSuite, TestResult, TestFailure } from './types';
import { ConfigManager } from './config';

export interface PWATestConfig {
    baseUrl: string;
    manifestPath: string;
    serviceWorkerPath: string;
    timeout: number;
    offlinePages: string[];
    criticalResources: string[];
}

export interface ManifestValidationError {
    field: string;
    message: string;
    severity: 'error' | 'warning';
}

export class PWAValidator {
    private config: PWATestConfig;
    private browser?: Browser;
    private page?: Page;

    constructor(config: PWATestConfig) {
        this.config = config;
    }

    /**
     * Initialize browser and page for testing
     */
    private async initialize(): Promise<void> {
        this.browser = await chromium.launch({
            headless: true,
            args: ['--no-sandbox', '--disable-setuid-sandbox']
        });

        const context = await this.browser.newContext({
            viewport: { width: 1280, height: 720 },
            userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        });

        this.page = await context.newPage();

        // Enable service worker events
        await this.page.context().grantPermissions(['notifications']);
    }

    /**
     * Clean up browser resources
     */
    private async cleanup(): Promise<void> {
        if (this.page) {
            await this.page.close();
        }
        if (this.browser) {
            await this.browser.close();
        }
    }

    /**
     * Execute complete PWA validation
     */
    async validate(): Promise<PWAValidationResult> {
        await this.initialize();

        try {
            const [
                serviceWorkerResult,
                manifestResult,
                offlineResult,
                installableResult,
                cacheResult
            ] = await Promise.all([
                this.testServiceWorkerRegistration(),
                this.validateManifest(),
                this.testOfflineFunctionality(),
                this.testInstallability(),
                this.validateCacheStrategy()
            ]);

            return {
                serviceWorkerRegistered: serviceWorkerResult,
                manifestValid: manifestResult,
                offlineFunctionality: offlineResult,
                installable: installableResult,
                cacheStrategy: cacheResult
            };
        } finally {
            await this.cleanup();
        }
    }

    /**
     * Test service worker registration
     */
    private async testServiceWorkerRegistration(): Promise<boolean> {
        if (!this.page) throw new Error('Page not initialized');

        try {
            // Navigate to the application
            await this.page.goto(this.config.baseUrl, {
                waitUntil: 'networkidle',
                timeout: this.config.timeout
            });

            // Wait for service worker registration
            const serviceWorkerRegistered = await this.page.evaluate(async () => {
                // Check if service worker is supported
                if (!('serviceWorker' in navigator)) {
                    return false;
                }

                // Wait for service worker registration
                try {
                    const registration = await navigator.serviceWorker.ready;
                    return registration !== null && registration.active !== null;
                } catch (error) {
                    console.error('Service worker registration failed:', error);
                    return false;
                }
            });

            // Additional check: verify service worker script exists
            if (serviceWorkerRegistered) {
                const swResponse = await this.page.request.get(`${this.config.baseUrl}${this.config.serviceWorkerPath}`);
                return swResponse.ok();
            }

            return serviceWorkerRegistered;
        } catch (error) {
            console.error('Service worker test failed:', error);
            return false;
        }
    }

    /**
     * Validate PWA manifest
     */
    private async validateManifest(): Promise<boolean> {
        try {
            // Fetch and parse manifest
            const manifestUrl = `${this.config.baseUrl}${this.config.manifestPath}`;
            const response = await fetch(manifestUrl);

            if (!response.ok) {
                console.error(`Manifest not found at ${manifestUrl}`);
                return false;
            }

            const manifest = await response.json();
            const errors = this.validateManifestContent(manifest);

            // Log validation errors
            errors.forEach(error => {
                if (error.severity === 'error') {
                    console.error(`Manifest error: ${error.field} - ${error.message}`);
                } else {
                    console.warn(`Manifest warning: ${error.field} - ${error.message}`);
                }
            });

            // Return true if no critical errors
            return !errors.some(error => error.severity === 'error');
        } catch (error) {
            console.error('Manifest validation failed:', error);
            return false;
        }
    }

    /**
     * Validate manifest content according to PWA standards
     */
    private validateManifestContent(manifest: any): ManifestValidationError[] {
        const errors: ManifestValidationError[] = [];

        // Required fields
        if (!manifest.name && !manifest.short_name) {
            errors.push({
                field: 'name/short_name',
                message: 'Either name or short_name is required',
                severity: 'error'
            });
        }

        if (!manifest.start_url) {
            errors.push({
                field: 'start_url',
                message: 'start_url is required',
                severity: 'error'
            });
        }

        if (!manifest.display) {
            errors.push({
                field: 'display',
                message: 'display mode is required',
                severity: 'error'
            });
        }

        if (!manifest.icons || !Array.isArray(manifest.icons) || manifest.icons.length === 0) {
            errors.push({
                field: 'icons',
                message: 'At least one icon is required',
                severity: 'error'
            });
        } else {
            // Validate icons
            const hasRequiredSizes = manifest.icons.some((icon: any) => {
                const sizes = icon.sizes?.split('x');
                return sizes && sizes.length === 2 && parseInt(sizes[0]) >= 192;
            });

            if (!hasRequiredSizes) {
                errors.push({
                    field: 'icons',
                    message: 'At least one icon with size 192x192 or larger is required',
                    severity: 'error'
                });
            }
        }

        // Recommended fields
        if (!manifest.theme_color) {
            errors.push({
                field: 'theme_color',
                message: 'theme_color is recommended for better user experience',
                severity: 'warning'
            });
        }

        if (!manifest.background_color) {
            errors.push({
                field: 'background_color',
                message: 'background_color is recommended for splash screen',
                severity: 'warning'
            });
        }

        if (!manifest.description) {
            errors.push({
                field: 'description',
                message: 'description is recommended for app stores',
                severity: 'warning'
            });
        }

        return errors;
    }

    /**
     * Test offline functionality
     */
    private async testOfflineFunctionality(): Promise<boolean> {
        if (!this.page) throw new Error('Page not initialized');

        try {
            // First, load the page online to populate cache
            await this.page.goto(this.config.baseUrl, {
                waitUntil: 'networkidle',
                timeout: this.config.timeout
            });

            // Wait for service worker to be ready
            await this.page.waitForFunction(() => {
                return navigator.serviceWorker.ready;
            }, { timeout: 10000 });

            // Go offline
            await this.page.context().setOffline(true);

            // Test offline functionality for each configured page
            const offlineResults = await Promise.all(
                this.config.offlinePages.map(async (pagePath) => {
                    try {
                        const url = `${this.config.baseUrl}${pagePath}`;
                        await this.page!.goto(url, {
                            waitUntil: 'domcontentloaded',
                            timeout: 10000
                        });

                        // Check if page loaded successfully
                        const pageLoaded = await this.page!.evaluate(() => {
                            return document.readyState === 'complete' &&
                                document.body.children.length > 0;
                        });

                        return pageLoaded;
                    } catch (error) {
                        console.error(`Offline test failed for ${pagePath}:`, error);
                        return false;
                    }
                })
            );

            // Go back online
            await this.page.context().setOffline(false);

            return offlineResults.every(result => result);
        } catch (error) {
            console.error('Offline functionality test failed:', error);
            return false;
        }
    }

    /**
     * Test PWA installability
     */
    private async testInstallability(): Promise<boolean> {
        if (!this.page) throw new Error('Page not initialized');

        try {
            await this.page.goto(this.config.baseUrl, {
                waitUntil: 'networkidle',
                timeout: this.config.timeout
            });

            // Check if beforeinstallprompt event is triggered
            const installable = await this.page.evaluate(() => {
                return new Promise<boolean>((resolve) => {
                    let installPromptFired = false;

                    // Listen for beforeinstallprompt event
                    window.addEventListener('beforeinstallprompt', () => {
                        installPromptFired = true;
                        resolve(true);
                    });

                    // Timeout after 5 seconds
                    setTimeout(() => {
                        resolve(installPromptFired);
                    }, 5000);
                });
            });

            // Additional checks for PWA criteria
            if (installable) {
                // Verify HTTPS (or localhost)
                const isSecure = this.config.baseUrl.startsWith('https://') ||
                    this.config.baseUrl.includes('localhost');

                if (!isSecure) {
                    console.warn('PWA requires HTTPS for installation');
                    return false;
                }

                return true;
            }

            return false;
        } catch (error) {
            console.error('Installability test failed:', error);
            return false;
        }
    }

    /**
     * Validate cache strategy
     */
    private async validateCacheStrategy(): Promise<CacheValidation> {
        if (!this.page) throw new Error('Page not initialized');

        try {
            await this.page.goto(this.config.baseUrl, {
                waitUntil: 'networkidle',
                timeout: this.config.timeout
            });

            // Wait for service worker to be ready
            await this.page.waitForFunction(() => {
                return navigator.serviceWorker.ready;
            }, { timeout: 10000 });

            const cacheValidation = await this.page.evaluate(async (criticalResources) => {
                // Check if caches API is available
                if (!('caches' in window)) {
                    return {
                        staticAssetsCache: false,
                        apiResponseCache: false,
                        offlinePages: []
                    };
                }

                try {
                    const cacheNames = await caches.keys();
                    let staticAssetsCache = false;
                    let apiResponseCache = false;
                    const offlinePages: string[] = [];

                    // Check each cache
                    for (const cacheName of cacheNames) {
                        const cache = await caches.open(cacheName);
                        const requests = await cache.keys();

                        for (const request of requests) {
                            const url = request.url;

                            // Check for static assets
                            if (url.match(/\.(js|css|png|jpg|jpeg|webp|svg|woff2?)$/)) {
                                staticAssetsCache = true;
                            }

                            // Check for API responses
                            if (url.includes('/api/')) {
                                apiResponseCache = true;
                            }

                            // Check for cached pages
                            if (url.endsWith('/') || url.match(/\.html$/)) {
                                offlinePages.push(url);
                            }
                        }
                    }

                    return {
                        staticAssetsCache,
                        apiResponseCache,
                        offlinePages
                    };
                } catch (error) {
                    console.error('Cache validation error:', error);
                    return {
                        staticAssetsCache: false,
                        apiResponseCache: false,
                        offlinePages: []
                    };
                }
            }, this.config.criticalResources);

            return cacheValidation;
        } catch (error) {
            console.error('Cache strategy validation failed:', error);
            return {
                staticAssetsCache: false,
                apiResponseCache: false,
                offlinePages: []
            };
        }
    }
}

/**
 * PWA Test Suite for integration with test orchestrator
 */
export class PWATestSuite implements TestSuite {
    name = 'pwa';
    type: 'unit' | 'integration' | 'e2e' | 'performance' | 'accessibility' = 'e2e';

    private config: PWATestConfig;
    private validator: PWAValidator;

    constructor(configManager?: ConfigManager) {
        // Default configuration
        this.config = {
            baseUrl: process.env.BASE_URL || 'http://localhost:5173',
            manifestPath: '/manifest.json',
            serviceWorkerPath: '/sw.js',
            timeout: 30000,
            offlinePages: ['/', '/teamsales', '/harbor-city'],
            criticalResources: [
                '/Icebox.webp',
                '/manifest.json',
                '/sw.js'
            ]
        };

        this.validator = new PWAValidator(this.config);
    }

    async execute(): Promise<TestResult> {
        const startTime = Date.now();
        const failures: TestFailure[] = [];

        try {
            const result = await this.validator.validate();

            // Check each validation result and create failures for failed tests
            if (!result.serviceWorkerRegistered) {
                failures.push({
                    testName: 'Service Worker Registration',
                    error: 'Service worker failed to register or is not active',
                    duration: 0
                });
            }

            if (!result.manifestValid) {
                failures.push({
                    testName: 'PWA Manifest Validation',
                    error: 'PWA manifest is invalid or missing required fields',
                    duration: 0
                });
            }

            if (!result.offlineFunctionality) {
                failures.push({
                    testName: 'Offline Functionality',
                    error: 'Application does not work properly in offline mode',
                    duration: 0
                });
            }

            if (!result.installable) {
                failures.push({
                    testName: 'PWA Installability',
                    error: 'Application does not meet PWA installability criteria',
                    duration: 0
                });
            }

            if (!result.cacheStrategy.staticAssetsCache) {
                failures.push({
                    testName: 'Static Assets Caching',
                    error: 'Static assets are not being cached properly',
                    duration: 0
                });
            }

            const duration = Date.now() - startTime;
            const testCount = 5; // Number of PWA validation tests

            return {
                passed: failures.length === 0,
                duration,
                testCount,
                failures
            };
        } catch (error) {
            const duration = Date.now() - startTime;

            return {
                passed: false,
                duration,
                testCount: 1,
                failures: [{
                    testName: 'PWA Validation Suite',
                    error: error instanceof Error ? error.message : 'Unknown error during PWA validation',
                    duration
                }]
            };
        }
    }
}

/**
 * Standalone PWA validation function for direct usage
 */
export async function validatePWA(config?: Partial<PWATestConfig>): Promise<PWAValidationResult> {
    const defaultConfig: PWATestConfig = {
        baseUrl: process.env.BASE_URL || 'http://localhost:5173',
        manifestPath: '/manifest.json',
        serviceWorkerPath: '/sw.js',
        timeout: 30000,
        offlinePages: ['/', '/teamsales', '/harbor-city'],
        criticalResources: [
            '/Icebox.webp',
            '/manifest.json',
            '/sw.js'
        ]
    };

    const finalConfig = { ...defaultConfig, ...config };
    const validator = new PWAValidator(finalConfig);

    return await validator.validate();
}