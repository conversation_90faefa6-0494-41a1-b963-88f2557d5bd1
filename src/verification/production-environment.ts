/**
 * Production-like Environment Testing Module
 * 
 * Tests application in production-like conditions including:
 * - Production build testing with optimized assets
 * - Production server simulation for realistic testing
 * - Network condition simulation for performance testing
 * - Application loading and functionality in production mode
 */

import { spawn, ChildProcess } from 'child_process';
import { promises as fs } from 'fs';
import path from 'path';
import { createServer, ViteDevServer } from 'vite';
import { Page, Browser, chromium, firefox, webkit } from '@playwright/test';
import { VerificationStageResult } from './types';

export interface ProductionEnvironmentConfig {
  buildCommand: string;
  previewCommand: string;
  previewPort: number;
  testTimeout: number;
  networkConditions: NetworkCondition[];
  browsers: BrowserType[];
  criticalPages: string[];
}

export interface NetworkCondition {
  name: string;
  downloadThroughput: number; // bytes per second
  uploadThroughput: number;   // bytes per second
  latency: number;            // milliseconds
}

export interface BrowserType {
  name: 'chromium' | 'firefox' | 'webkit';
  enabled: boolean;
}

export interface ProductionTestResult {
  buildSuccess: boolean;
  serverStarted: boolean;
  pageLoadTests: PageLoadTestResult[];
  functionalityTests: FunctionalityTestResult[];
  networkConditionTests: NetworkConditionTestResult[];
  overallSuccess: boolean;
  errors: string[];
  warnings: string[];
}

export interface PageLoadTestResult {
  page: string;
  browser: string;
  loadTime: number;
  success: boolean;
  errors: string[];
  metrics: {
    domContentLoaded: number;
    firstContentfulPaint: number;
    largestContentfulPaint: number;
    resourceCount: number;
    totalSize: number;
  };
}

export interface FunctionalityTestResult {
  testName: string;
  browser: string;
  success: boolean;
  duration: number;
  error?: string;
}

export interface NetworkConditionTestResult {
  condition: string;
  browser: string;
  pages: {
    page: string;
    loadTime: number;
    success: boolean;
    error?: string;
  }[];
  overallSuccess: boolean;
}

export class ProductionEnvironmentTester {
  private config: ProductionEnvironmentConfig;
  private previewServer?: ChildProcess;
  private baseUrl: string;

  constructor(config: ProductionEnvironmentConfig) {
    this.config = config;
    this.baseUrl = `http://localhost:${config.previewPort}`;
  }

  /**
   * Execute complete production environment testing
   */
  async test(): Promise<ProductionTestResult> {
    const result: ProductionTestResult = {
      buildSuccess: false,
      serverStarted: false,
      pageLoadTests: [],
      functionalityTests: [],
      networkConditionTests: [],
      overallSuccess: false,
      errors: [],
      warnings: []
    };

    try {
      // Step 1: Build application in production mode
      console.log('Building application in production mode...');
      result.buildSuccess = await this.buildProduction();
      
      if (!result.buildSuccess) {
        result.errors.push('Production build failed');
        return result;
      }

      // Step 2: Start production preview server
      console.log('Starting production preview server...');
      result.serverStarted = await this.startPreviewServer();
      
      if (!result.serverStarted) {
        result.errors.push('Failed to start preview server');
        return result;
      }

      // Step 3: Test page loading across browsers
      console.log('Testing page loading across browsers...');
      result.pageLoadTests = await this.testPageLoading();

      // Step 4: Test core functionality
      console.log('Testing core functionality...');
      result.functionalityTests = await this.testCoreFunctionality();

      // Step 5: Test under different network conditions
      console.log('Testing under different network conditions...');
      result.networkConditionTests = await this.testNetworkConditions();

      // Determine overall success
      result.overallSuccess = this.evaluateOverallSuccess(result);

    } catch (error) {
      result.errors.push(`Production environment testing failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      // Clean up preview server
      await this.stopPreviewServer();
    }

    return result;
  }

  /**
   * Build application in production mode
   */
  private async buildProduction(): Promise<boolean> {
    return new Promise((resolve) => {
      const buildProcess = spawn('npm', ['run', 'build'], {
        stdio: ['pipe', 'pipe', 'pipe'],
        shell: true,
      });

      let stderr = '';

      buildProcess.stderr?.on('data', (data) => {
        stderr += data.toString();
      });

      buildProcess.on('close', (code) => {
        if (code !== 0) {
          console.error('Build failed:', stderr);
        }
        resolve(code === 0);
      });

      buildProcess.on('error', (error) => {
        console.error('Build process error:', error);
        resolve(false);
      });
    });
  }

  /**
   * Start production preview server
   */
  private async startPreviewServer(): Promise<boolean> {
    return new Promise((resolve) => {
      this.previewServer = spawn('npm', ['run', 'preview', '--', '--port', this.config.previewPort.toString()], {
        stdio: ['pipe', 'pipe', 'pipe'],
        shell: true,
      });

      let stdout = '';
      let resolved = false;

      this.previewServer.stdout?.on('data', (data) => {
        stdout += data.toString();
        
        // Check if server is ready
        if (stdout.includes('Local:') && !resolved) {
          resolved = true;
          // Give server a moment to fully start
          setTimeout(() => resolve(true), 2000);
        }
      });

      this.previewServer.on('error', (error) => {
        if (!resolved) {
          console.error('Preview server error:', error);
          resolved = true;
          resolve(false);
        }
      });

      // Timeout after 30 seconds
      setTimeout(() => {
        if (!resolved) {
          resolved = true;
          resolve(false);
        }
      }, 30000);
    });
  }

  /**
   * Stop production preview server
   */
  private async stopPreviewServer(): Promise<void> {
    if (this.previewServer) {
      this.previewServer.kill('SIGTERM');
      
      // Wait for graceful shutdown
      await new Promise<void>((resolve) => {
        if (!this.previewServer) {
          resolve();
          return;
        }

        this.previewServer.on('close', () => resolve());
        
        // Force kill after 5 seconds
        setTimeout(() => {
          if (this.previewServer && !this.previewServer.killed) {
            this.previewServer.kill('SIGKILL');
          }
          resolve();
        }, 5000);
      });
    }
  }

  /**
   * Test page loading across different browsers
   */
  private async testPageLoading(): Promise<PageLoadTestResult[]> {
    const results: PageLoadTestResult[] = [];
    const enabledBrowsers = this.config.browsers.filter(b => b.enabled);

    for (const browserConfig of enabledBrowsers) {
      const browser = await this.launchBrowser(browserConfig.name);
      
      try {
        for (const pagePath of this.config.criticalPages) {
          const result = await this.testPageLoad(browser, browserConfig.name, pagePath);
          results.push(result);
        }
      } finally {
        await browser.close();
      }
    }

    return results;
  }

  /**
   * Test loading of a specific page
   */
  private async testPageLoad(browser: Browser, browserName: string, pagePath: string): Promise<PageLoadTestResult> {
    const page = await browser.newPage();
    const url = `${this.baseUrl}${pagePath}`;
    
    const result: PageLoadTestResult = {
      page: pagePath,
      browser: browserName,
      loadTime: 0,
      success: false,
      errors: [],
      metrics: {
        domContentLoaded: 0,
        firstContentfulPaint: 0,
        largestContentfulPaint: 0,
        resourceCount: 0,
        totalSize: 0
      }
    };

    try {
      const startTime = Date.now();

      // Listen for console errors
      page.on('console', (msg) => {
        if (msg.type() === 'error') {
          result.errors.push(`Console error: ${msg.text()}`);
        }
      });

      // Listen for page errors
      page.on('pageerror', (error) => {
        result.errors.push(`Page error: ${error.message}`);
      });

      // Navigate to page
      const response = await page.goto(url, { 
        waitUntil: 'networkidle',
        timeout: this.config.testTimeout 
      });

      result.loadTime = Date.now() - startTime;

      if (!response || !response.ok()) {
        result.errors.push(`HTTP ${response?.status()}: Failed to load page`);
        return result;
      }

      // Collect performance metrics
      const performanceMetrics = await page.evaluate(() => {
        const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
        const paintEntries = performance.getEntriesByType('paint');
        const resources = performance.getEntriesByType('resource');

        const fcp = paintEntries.find(entry => entry.name === 'first-contentful-paint');
        const lcp = paintEntries.find(entry => entry.name === 'largest-contentful-paint');

        return {
          domContentLoaded: navigation.domContentLoadedEventEnd - navigation.navigationStart,
          firstContentfulPaint: fcp ? fcp.startTime : 0,
          largestContentfulPaint: lcp ? lcp.startTime : 0,
          resourceCount: resources.length,
          totalSize: resources.reduce((total, resource) => total + (resource.transferSize || 0), 0)
        };
      });

      result.metrics = performanceMetrics;
      result.success = result.errors.length === 0;

    } catch (error) {
      result.errors.push(`Page load failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      await page.close();
    }

    return result;
  }

  /**
   * Test core functionality in production mode
   */
  private async testCoreFunctionality(): Promise<FunctionalityTestResult[]> {
    const results: FunctionalityTestResult[] = [];
    const enabledBrowsers = this.config.browsers.filter(b => b.enabled);

    for (const browserConfig of enabledBrowsers) {
      const browser = await this.launchBrowser(browserConfig.name);
      
      try {
        // Test navigation functionality
        const navResult = await this.testNavigation(browser, browserConfig.name);
        results.push(navResult);

        // Test search functionality
        const searchResult = await this.testSearch(browser, browserConfig.name);
        results.push(searchResult);

        // Test responsive design
        const responsiveResult = await this.testResponsiveDesign(browser, browserConfig.name);
        results.push(responsiveResult);

        // Test PWA features
        const pwaResult = await this.testPWAFeatures(browser, browserConfig.name);
        results.push(pwaResult);

      } finally {
        await browser.close();
      }
    }

    return results;
  }

  /**
   * Test navigation functionality
   */
  private async testNavigation(browser: Browser, browserName: string): Promise<FunctionalityTestResult> {
    const page = await browser.newPage();
    const startTime = Date.now();

    try {
      await page.goto(this.baseUrl, { waitUntil: 'networkidle' });

      // Test main navigation links
      const navLinks = await page.locator('nav a').all();
      
      for (const link of navLinks.slice(0, 3)) { // Test first 3 links
        await link.click();
        await page.waitForLoadState('networkidle');
        
        // Check if page loaded successfully
        const title = await page.title();
        if (!title) {
          throw new Error('Page title is empty after navigation');
        }
      }

      return {
        testName: 'Navigation',
        browser: browserName,
        success: true,
        duration: Date.now() - startTime
      };

    } catch (error) {
      return {
        testName: 'Navigation',
        browser: browserName,
        success: false,
        duration: Date.now() - startTime,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    } finally {
      await page.close();
    }
  }

  /**
   * Test search functionality
   */
  private async testSearch(browser: Browser, browserName: string): Promise<FunctionalityTestResult> {
    const page = await browser.newPage();
    const startTime = Date.now();

    try {
      await page.goto(this.baseUrl, { waitUntil: 'networkidle' });

      // Look for search input
      const searchInput = page.locator('input[type="search"], input[placeholder*="search" i]').first();
      
      if (await searchInput.count() > 0) {
        await searchInput.fill('hockey');
        await searchInput.press('Enter');
        
        // Wait for search results
        await page.waitForTimeout(2000);
        
        // Check if results are displayed
        const hasResults = await page.locator('[data-testid*="search"], [class*="search"], [id*="search"]').count() > 0;
        
        if (!hasResults) {
          throw new Error('No search results found');
        }
      }

      return {
        testName: 'Search',
        browser: browserName,
        success: true,
        duration: Date.now() - startTime
      };

    } catch (error) {
      return {
        testName: 'Search',
        browser: browserName,
        success: false,
        duration: Date.now() - startTime,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    } finally {
      await page.close();
    }
  }

  /**
   * Test responsive design
   */
  private async testResponsiveDesign(browser: Browser, browserName: string): Promise<FunctionalityTestResult> {
    const page = await browser.newPage();
    const startTime = Date.now();

    try {
      await page.goto(this.baseUrl, { waitUntil: 'networkidle' });

      // Test different viewport sizes
      const viewports = [
        { width: 375, height: 667 },   // Mobile
        { width: 768, height: 1024 },  // Tablet
        { width: 1920, height: 1080 }  // Desktop
      ];

      for (const viewport of viewports) {
        await page.setViewportSize(viewport);
        await page.waitForTimeout(1000);

        // Check if content is visible and properly laid out
        const bodyHeight = await page.evaluate(() => document.body.scrollHeight);
        if (bodyHeight < 100) {
          throw new Error(`Content not properly rendered at ${viewport.width}x${viewport.height}`);
        }
      }

      return {
        testName: 'Responsive Design',
        browser: browserName,
        success: true,
        duration: Date.now() - startTime
      };

    } catch (error) {
      return {
        testName: 'Responsive Design',
        browser: browserName,
        success: false,
        duration: Date.now() - startTime,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    } finally {
      await page.close();
    }
  }

  /**
   * Test PWA features
   */
  private async testPWAFeatures(browser: Browser, browserName: string): Promise<FunctionalityTestResult> {
    const page = await browser.newPage();
    const startTime = Date.now();

    try {
      await page.goto(this.baseUrl, { waitUntil: 'networkidle' });

      // Check for service worker registration
      const swRegistered = await page.evaluate(() => {
        return 'serviceWorker' in navigator;
      });

      if (!swRegistered) {
        throw new Error('Service Worker not supported or registered');
      }

      // Check for manifest
      const manifestLink = await page.locator('link[rel="manifest"]').count();
      if (manifestLink === 0) {
        throw new Error('PWA manifest not found');
      }

      return {
        testName: 'PWA Features',
        browser: browserName,
        success: true,
        duration: Date.now() - startTime
      };

    } catch (error) {
      return {
        testName: 'PWA Features',
        browser: browserName,
        success: false,
        duration: Date.now() - startTime,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    } finally {
      await page.close();
    }
  }

  /**
   * Test under different network conditions
   */
  private async testNetworkConditions(): Promise<NetworkConditionTestResult[]> {
    const results: NetworkConditionTestResult[] = [];
    const enabledBrowsers = this.config.browsers.filter(b => b.enabled);

    for (const browserConfig of enabledBrowsers) {
      for (const networkCondition of this.config.networkConditions) {
        const browser = await this.launchBrowser(browserConfig.name);
        
        try {
          const result = await this.testUnderNetworkCondition(
            browser, 
            browserConfig.name, 
            networkCondition
          );
          results.push(result);
        } finally {
          await browser.close();
        }
      }
    }

    return results;
  }

  /**
   * Test under specific network condition
   */
  private async testUnderNetworkCondition(
    browser: Browser, 
    browserName: string, 
    condition: NetworkCondition
  ): Promise<NetworkConditionTestResult> {
    const result: NetworkConditionTestResult = {
      condition: condition.name,
      browser: browserName,
      pages: [],
      overallSuccess: true
    };

    const context = await browser.newContext();
    
    try {
      // Set network conditions (Chromium only)
      if (browserName === 'chromium') {
        const cdpSession = await context.newCDPSession(await context.newPage());
        await cdpSession.send('Network.emulateNetworkConditions', {
          offline: false,
          downloadThroughput: condition.downloadThroughput,
          uploadThroughput: condition.uploadThroughput,
          latency: condition.latency
        });
      }

      // Test critical pages under this network condition
      for (const pagePath of this.config.criticalPages.slice(0, 2)) { // Test first 2 pages
        const page = await context.newPage();
        const startTime = Date.now();

        try {
          await page.goto(`${this.baseUrl}${pagePath}`, { 
            waitUntil: 'networkidle',
            timeout: this.config.testTimeout * 2 // Double timeout for slow networks
          });

          const loadTime = Date.now() - startTime;

          result.pages.push({
            page: pagePath,
            loadTime,
            success: true
          });

        } catch (error) {
          result.pages.push({
            page: pagePath,
            loadTime: Date.now() - startTime,
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error'
          });
          result.overallSuccess = false;
        } finally {
          await page.close();
        }
      }

    } finally {
      await context.close();
    }

    return result;
  }

  /**
   * Launch browser instance
   */
  private async launchBrowser(browserType: 'chromium' | 'firefox' | 'webkit'): Promise<Browser> {
    const options = {
      headless: true,
      args: ['--no-sandbox', '--disable-dev-shm-usage']
    };

    switch (browserType) {
      case 'chromium':
        return await chromium.launch(options);
      case 'firefox':
        return await firefox.launch(options);
      case 'webkit':
        return await webkit.launch(options);
      default:
        throw new Error(`Unsupported browser type: ${browserType}`);
    }
  }

  /**
   * Evaluate overall success of production environment testing
   */
  private evaluateOverallSuccess(result: ProductionTestResult): boolean {
    if (!result.buildSuccess || !result.serverStarted) {
      return false;
    }

    // Check page load tests
    const pageLoadFailures = result.pageLoadTests.filter(test => !test.success);
    if (pageLoadFailures.length > 0) {
      result.warnings.push(`${pageLoadFailures.length} page load tests failed`);
    }

    // Check functionality tests
    const functionalityFailures = result.functionalityTests.filter(test => !test.success);
    if (functionalityFailures.length > 0) {
      result.warnings.push(`${functionalityFailures.length} functionality tests failed`);
    }

    // Check network condition tests
    const networkFailures = result.networkConditionTests.filter(test => !test.overallSuccess);
    if (networkFailures.length > 0) {
      result.warnings.push(`${networkFailures.length} network condition tests failed`);
    }

    // Consider test successful if critical functionality works
    const criticalFailures = pageLoadFailures.length + functionalityFailures.length;
    return criticalFailures === 0;
  }

  /**
   * Generate detailed report
   */
  generateReport(result: ProductionTestResult): string {
    const lines: string[] = [];
    
    lines.push('=== Production Environment Test Report ===');
    lines.push(`Overall Success: ${result.overallSuccess ? 'PASS' : 'FAIL'}`);
    lines.push(`Build Success: ${result.buildSuccess ? 'PASS' : 'FAIL'}`);
    lines.push(`Server Started: ${result.serverStarted ? 'PASS' : 'FAIL'}`);
    lines.push('');

    // Page Load Tests
    lines.push('Page Load Tests:');
    result.pageLoadTests.forEach(test => {
      lines.push(`  ${test.page} (${test.browser}): ${test.success ? 'PASS' : 'FAIL'} - ${test.loadTime}ms`);
      if (test.errors.length > 0) {
        test.errors.forEach(error => lines.push(`    Error: ${error}`));
      }
    });
    lines.push('');

    // Functionality Tests
    lines.push('Functionality Tests:');
    result.functionalityTests.forEach(test => {
      lines.push(`  ${test.testName} (${test.browser}): ${test.success ? 'PASS' : 'FAIL'} - ${test.duration}ms`);
      if (test.error) {
        lines.push(`    Error: ${test.error}`);
      }
    });
    lines.push('');

    // Network Condition Tests
    lines.push('Network Condition Tests:');
    result.networkConditionTests.forEach(test => {
      lines.push(`  ${test.condition} (${test.browser}): ${test.overallSuccess ? 'PASS' : 'FAIL'}`);
      test.pages.forEach(page => {
        lines.push(`    ${page.page}: ${page.success ? 'PASS' : 'FAIL'} - ${page.loadTime}ms`);
        if (page.error) {
          lines.push(`      Error: ${page.error}`);
        }
      });
    });

    if (result.errors.length > 0) {
      lines.push('');
      lines.push('Errors:');
      result.errors.forEach(error => lines.push(`  - ${error}`));
    }

    if (result.warnings.length > 0) {
      lines.push('');
      lines.push('Warnings:');
      result.warnings.forEach(warning => lines.push(`  - ${warning}`));
    }

    return lines.join('\n');
  }
}

/**
 * Default configuration for production environment testing
 */
export const DEFAULT_PRODUCTION_CONFIG: ProductionEnvironmentConfig = {
  buildCommand: 'npm run build',
  previewCommand: 'npm run preview',
  previewPort: 4173,
  testTimeout: 30000,
  networkConditions: [
    {
      name: 'Fast 3G',
      downloadThroughput: 1.5 * 1024 * 1024 / 8, // 1.5 Mbps
      uploadThroughput: 750 * 1024 / 8,           // 750 Kbps
      latency: 150
    },
    {
      name: 'Slow 3G',
      downloadThroughput: 500 * 1024 / 8,         // 500 Kbps
      uploadThroughput: 500 * 1024 / 8,           // 500 Kbps
      latency: 300
    }
  ],
  browsers: [
    { name: 'chromium', enabled: true },
    { name: 'firefox', enabled: false },
    { name: 'webkit', enabled: false }
  ],
  criticalPages: [
    '/',
    '/team-sales',
    '/harbor-city'
  ]
};

/**
 * Factory function to create production environment tester
 */
export const createProductionEnvironmentTester = (
  config: Partial<ProductionEnvironmentConfig> = {}
): ProductionEnvironmentTester => {
  const mergedConfig = { ...DEFAULT_PRODUCTION_CONFIG, ...config };
  return new ProductionEnvironmentTester(mergedConfig);
};