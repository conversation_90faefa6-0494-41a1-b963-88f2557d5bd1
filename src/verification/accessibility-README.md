# Accessibility Validation System

A comprehensive accessibility validation system that integrates axe-core with <PERSON><PERSON> for automated WCAG 2.1 AA compliance checking, keyboard navigation testing, color contrast validation, and screen reader compatibility testing.

## Features

### ✅ Core Accessibility Testing
- **Axe-core Integration**: Automated WCAG 2.1 compliance checking using industry-standard axe-core library
- **Multiple WCAG Levels**: Support for WCAG 2.1 A, AA, and AAA compliance levels
- **Comprehensive Rule Coverage**: Tests 30+ accessibility rules including color contrast, form labels, ARIA attributes, and semantic markup

### ✅ Advanced Testing Capabilities
- **Keyboard Navigation Testing**: Validates tab order, focus management, and keyboard accessibility
- **Color Contrast Validation**: Calculates and validates color contrast ratios for text elements
- **Screen Reader Compatibility**: Tests heading hierarchy, landmarks, and ARIA attributes
- **Form Accessibility**: Validates form labels, error states, and input associations

### ✅ Reporting and Remediation
- **Detailed Violation Reports**: Comprehensive reports with element selectors and descriptions
- **Remediation Guidance**: Specific, actionable guidance for fixing accessibility issues
- **Multiple Output Formats**: Console, JSON, and HTML report generation
- **Severity Classification**: Critical, serious, moderate, and minor impact levels

### ✅ Integration and Configuration
- **Test Suite Integration**: Seamless integration with existing test infrastructure
- **Configurable Options**: Customizable timeouts, retries, rule inclusion/exclusion
- **Error Handling**: Graceful failure handling with detailed error reporting
- **Production Ready**: Designed for CI/CD integration and deployment verification

## Quick Start

### Basic Usage

```typescript
import { AccessibilityValidator } from './src/verification/accessibility';

// Create validator with default configuration
const validator = new AccessibilityValidator();

// Run accessibility validation
const results = await validator.validate();

// Generate report with remediation guidance
const report = validator.generateReport(results);
console.log(report);
```

### Custom Configuration

```typescript
const validator = new AccessibilityValidator({
  baseUrl: 'http://localhost:3000',
  pages: ['/', '/about', '/contact'],
  wcagLevel: 'AAA',
  timeout: 30000,
  keyboardNavigation: true,
  colorContrast: true,
  screenReader: true,
  includeRules: ['color-contrast', 'image-alt', 'label'],
  excludeRules: ['bypass'],
});
```

### Test Suite Integration

```typescript
import { AccessibilityTestSuite } from './src/verification/accessibility';
import { ConfigManager } from './src/verification/config';

const config = new ConfigManager({
  accessibilityLevel: 'AA',
  testSuites: [{
    name: 'accessibility-tests',
    type: 'accessibility',
    enabled: true,
    timeout: 60000,
    retries: 2,
  }],
});

const testSuite = new AccessibilityTestSuite(config);
const results = await testSuite.execute();
```

## Configuration Options

### AccessibilityTestConfig

| Option | Type | Default | Description |
|--------|------|---------|-------------|
| `baseUrl` | string | `'http://localhost:4183'` | Base URL for testing |
| `pages` | string[] | `['/', '/team-sales', '/harbor-city']` | Pages to test |
| `wcagLevel` | 'A' \| 'AA' \| 'AAA' | `'AA'` | WCAG compliance level |
| `includeRules` | string[] | `undefined` | Specific rules to include |
| `excludeRules` | string[] | `undefined` | Rules to exclude from testing |
| `timeout` | number | `30000` | Page load timeout in milliseconds |
| `retries` | number | `2` | Number of retry attempts |
| `keyboardNavigation` | boolean | `true` | Enable keyboard navigation testing |
| `colorContrast` | boolean | `true` | Enable color contrast validation |
| `screenReader` | boolean | `true` | Enable screen reader compatibility testing |

## Testing Features

### 1. Axe-core WCAG Compliance

Tests all major WCAG 2.1 guidelines including:
- Color contrast requirements
- Image alt text
- Form labels and associations
- Button accessible names
- Heading hierarchy
- ARIA attributes and roles
- Keyboard accessibility
- Focus management

### 2. Keyboard Navigation Testing

- **Tab Order Validation**: Ensures logical tab sequence
- **Focus Visibility**: Verifies focus indicators are visible
- **Keyboard Activation**: Tests Enter/Space key activation
- **Focus Trapping**: Validates modal focus management
- **Skip Links**: Checks for proper navigation shortcuts

### 3. Color Contrast Validation

- **WCAG AA Compliance**: 4.5:1 ratio for normal text, 3:1 for large text
- **WCAG AAA Compliance**: 7:1 ratio for normal text, 4.5:1 for large text
- **Multiple Color Formats**: RGB, hex, and named color support
- **Dynamic Content**: Tests computed styles and dynamic elements

### 4. Screen Reader Compatibility

- **Semantic Markup**: Validates proper HTML5 semantic elements
- **ARIA Landmarks**: Tests main, nav, banner, contentinfo regions
- **Heading Structure**: Ensures logical heading hierarchy (h1-h6)
- **Form Accessibility**: Validates labels, fieldsets, and error messages
- **Image Descriptions**: Tests alt text and decorative image handling

## Remediation Guidance

The system provides specific, actionable guidance for common accessibility issues:

### Color Contrast Issues
```
Ensure text has sufficient contrast ratio (4.5:1 for normal text, 3:1 for large text). 
Use tools like WebAIM Contrast Checker to verify and adjust colors.
```

### Missing Alt Text
```
Add descriptive alt text to images. Use alt="" for decorative images. 
Alt text should convey the purpose and content of the image.
```

### Form Label Issues
```
Associate form inputs with labels using for/id attributes, aria-label, or aria-labelledby. 
Every form control needs an accessible name.
```

### Keyboard Navigation Problems
```
Ensure all interactive elements are keyboard accessible with Tab/Shift+Tab. 
Provide visible focus indicators and logical tab order.
```

## Report Examples

### Violation Report
```
# Accessibility Validation Report
Generated: 2024-01-15T10:30:00.000Z
Overall Status: ❌ FAILED
Pages Tested: /, /team-sales, /harbor-city

## Violations (Must Fix)
### 1. color-contrast (serious)
**Element:** div.hero-text
**Issue:** Text has insufficient color contrast ratio of 3.2:1 (expected 4.5:1)
**Remediation:** Ensure text has sufficient contrast ratio (4.5:1 for normal text, 3:1 for large text)...

### 2. image-alt (critical)
**Element:** img.product-image
**Issue:** Image element missing required alt attribute
**Remediation:** Add descriptive alt text to images. Use alt="" for decorative images...
```

### Success Report
```
# Accessibility Validation Report
Generated: 2024-01-15T10:30:00.000Z
Overall Status: ✅ PASSED
Pages Tested: /

## ✅ All Tests Passed
No accessibility violations or warnings found.
```

## CLI Usage

### Run Demo
```bash
npm run demo:accessibility
```

### Run Accessibility Tests
```bash
npm run test:accessibility
```

### Run Verification Script
```bash
npx tsx scripts/verify-accessibility-implementation.ts
```

### Run Comprehensive Test
```bash
npx tsx scripts/test-accessibility-simple.ts
```

## Integration with CI/CD

### GitHub Actions Example
```yaml
- name: Run Accessibility Tests
  run: |
    npm run build
    npm run preview &
    sleep 5
    npm run test:accessibility
```

### Jest Integration
```javascript
import { AccessibilityTestSuite } from './src/verification/accessibility';

describe('Accessibility Tests', () => {
  test('should pass WCAG 2.1 AA compliance', async () => {
    const testSuite = new AccessibilityTestSuite(config);
    const results = await testSuite.execute();
    expect(results.passed).toBe(true);
  });
});
```

## Error Handling

The system includes comprehensive error handling for:

- **Browser Launch Failures**: Graceful degradation with detailed error messages
- **Page Navigation Timeouts**: Retry logic with exponential backoff
- **Axe-core Analysis Failures**: Fallback to manual validation checks
- **Network Connectivity Issues**: Timeout handling and connection retry
- **Invalid Configuration**: Input validation with helpful error messages

## Performance Considerations

- **Parallel Testing**: Tests multiple pages concurrently when possible
- **Smart Timeouts**: Configurable timeouts based on page complexity
- **Resource Optimization**: Efficient browser resource management
- **Selective Testing**: Option to test specific rules or page subsets
- **Caching**: Results caching for repeated test runs

## Best Practices

### 1. Test Early and Often
- Integrate accessibility testing into development workflow
- Run tests on every pull request
- Include accessibility in definition of done

### 2. Fix Issues Systematically
- Address critical and serious issues first
- Use provided remediation guidance
- Test fixes with actual assistive technologies

### 3. Maintain Accessibility Standards
- Establish team accessibility guidelines
- Provide accessibility training for developers
- Regular accessibility audits and reviews

### 4. Monitor and Improve
- Track accessibility metrics over time
- Set up automated alerts for regressions
- Continuously update testing rules and standards

## Troubleshooting

### Common Issues

**Server Connection Timeouts**
```bash
# Ensure development server is running
npm run build && npm run preview
```

**Browser Launch Failures**
```bash
# Install Playwright browsers
npx playwright install
```

**Memory Issues with Large Sites**
```typescript
// Reduce concurrent page testing
const validator = new AccessibilityValidator({
  pages: ['/'], // Test fewer pages at once
  timeout: 60000, // Increase timeout
});
```

### Debug Mode

Enable verbose logging for troubleshooting:
```typescript
const validator = new AccessibilityValidator({
  // ... other config
  verbose: true, // Enable debug logging
});
```

## Contributing

When contributing to the accessibility validation system:

1. **Follow WCAG Guidelines**: Ensure new rules follow WCAG 2.1 standards
2. **Add Tests**: Include unit and integration tests for new features
3. **Update Documentation**: Keep README and code comments current
4. **Test Thoroughly**: Verify changes work across different browsers and assistive technologies

## Resources

- [WCAG 2.1 Guidelines](https://www.w3.org/WAI/WCAG21/quickref/)
- [Axe-core Documentation](https://github.com/dequelabs/axe-core)
- [Playwright Testing](https://playwright.dev/)
- [WebAIM Contrast Checker](https://webaim.org/resources/contrastchecker/)
- [ARIA Authoring Practices](https://www.w3.org/WAI/ARIA/apg/)

## License

This accessibility validation system is part of the Ice Box Hockey production deployment verification system and follows the same licensing terms as the main project.