import React from 'react';
import { LucideIcon } from 'lucide-react';

/**
 * CategoryCard component for displaying product or service categories
 * @param icon - The Lucide icon component to display
 * @param title - The category title
 * @param description - The category description
 * @param items - Array of items in this category
 * @param gridCols - Grid column classes for responsive layout
 * @returns A styled category card component
 */
interface CategoryCardProps {
  /** The Lucide icon component to display */
  icon: LucideIcon;
  /** The category title */
  title: string;
  /** The category description */
  description: string;
  /** Array of items in this category */
  items: string[];
  /** Grid column classes for responsive layout */
  gridCols?: string;
}

const CategoryCard: React.FC<CategoryCardProps> = ({
  icon: Icon,
  title,
  description,
  items,
  gridCols = 'md:grid-cols-2 lg:grid-cols-2'
}) => {
  return (
    <div className="bg-white/10 backdrop-blur-sm p-8 rounded-xl border border-white/20 hover:border-blue-300/50 transition-all duration-300 group">
      <div className="flex items-start gap-6">
        <div className="bg-gradient-to-br from-blue-500 to-blue-600 w-16 h-16 rounded-lg flex items-center justify-center flex-shrink-0 group-hover:scale-110 transition-transform duration-300">
          <Icon className="h-8 w-8 text-white" />
        </div>
        
        <div className="flex-1">
          <h3 className="text-2xl font-bold text-white mb-3">
            {title}
          </h3>
          
          <p className="text-white mb-6 opacity-90">
            {description}
          </p>
          
          <ul className="space-y-2">
            {items.map((item, itemIndex) => (
              <li key={itemIndex} className="flex items-center gap-2 text-white">
                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                {item}
              </li>
            ))}
          </ul>
        </div>
      </div>
    </div>
  );
};

export default CategoryCard;