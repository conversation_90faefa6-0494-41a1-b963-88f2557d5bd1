import React from 'react';

// Skeleton components for different content types
export const SkeletonText: React.FC<{ 
  lines?: number; 
  className?: string;
  width?: 'full' | 'half' | 'quarter';
}> = ({ 
  lines = 1, 
  className = '', 
  width = 'full' 
}) => {
  const widthClass = {
    full: 'w-full',
    half: 'w-1/2',
    quarter: 'w-1/4'
  }[width];

  return (
    <div className={`space-y-2 ${className}`}>
      {Array.from({ length: lines }).map((_, i) => (
        <div
          key={i}
          className={`h-4 bg-gray-200 rounded animate-pulse ${
            i === lines - 1 && lines > 1 ? 'w-3/4' : widthClass
          }`}
        />
      ))}
    </div>
  );
};

export const SkeletonImage: React.FC<{ 
  className?: string;
  aspectRatio?: 'square' | 'video' | 'wide';
}> = ({ 
  className = '', 
  aspectRatio = 'square' 
}) => {
  const aspectClass = {
    square: 'aspect-square',
    video: 'aspect-video',
    wide: 'aspect-[3/1]'
  }[aspectRatio];

  return (
    <div className={`bg-gray-200 rounded animate-pulse ${aspectClass} ${className}`}>
      <div className="flex items-center justify-center h-full">
        <svg 
          className="w-8 h-8 text-gray-400" 
          fill="currentColor" 
          viewBox="0 0 20 20"
        >
          <path 
            fillRule="evenodd" 
            d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" 
            clipRule="evenodd" 
          />
        </svg>
      </div>
    </div>
  );
};

export const SkeletonCard: React.FC<{ className?: string }> = ({ 
  className = '' 
}) => (
  <div className={`bg-white p-6 rounded-lg border border-gray-200 ${className}`}>
    <SkeletonImage className="mb-4" />
    <SkeletonText lines={2} />
    <div className="mt-4">
      <SkeletonText width="quarter" />
    </div>
  </div>
);

export const SkeletonGrid: React.FC<{ 
  items?: number;
  columns?: number;
  className?: string;
}> = ({ 
  items = 6, 
  columns = 3, 
  className = '' 
}) => (
  <div className={`grid gap-6 ${className}`} style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}>
    {Array.from({ length: items }).map((_, i) => (
      <SkeletonCard key={i} />
    ))}
  </div>
);

// Loading spinner components
export const LoadingSpinner: React.FC<{ 
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}> = ({ 
  size = 'md', 
  className = '' 
}) => {
  const sizeClass = {
    sm: 'w-4 h-4',
    md: 'w-8 h-8',
    lg: 'w-12 h-12'
  }[size];

  return (
    <div className={`animate-spin rounded-full border-2 border-gray-300 border-t-blue-600 ${sizeClass} ${className}`} />
  );
};

export const LoadingDots: React.FC<{ className?: string }> = ({ 
  className = '' 
}) => (
  <div className={`flex space-x-1 ${className}`}>
    {[0, 1, 2].map((i) => (
      <div
        key={i}
        className="w-2 h-2 bg-blue-600 rounded-full animate-pulse"
        style={{ animationDelay: `${i * 0.2}s` }}
      />
    ))}
  </div>
);

/**
 * Section loader component for Suspense fallbacks
 * Provides a consistent loading experience across pages
 * @param className - Additional CSS classes
 */
export const SectionLoader: React.FC<{ className?: string }> = ({ 
  className = '' 
}) => (
  <div className={`py-20 flex items-center justify-center ${className}`} style={{ backgroundColor: '#1b263b' }}>
    <div className="animate-pulse flex space-x-4">
      <div className="rounded-full bg-blue-400/20 h-3 w-3"></div>
      <div className="rounded-full bg-blue-400/40 h-3 w-3"></div>
      <div className="rounded-full bg-blue-400/60 h-3 w-3"></div>
    </div>
  </div>
);

/**
 * Full-screen app loader for main application loading states
 * Used for route-level Suspense fallbacks
 * @param className - Additional CSS classes
 */
export const AppLoader: React.FC<{ className?: string }> = ({ 
  className = '' 
}) => (
  <div className={`min-h-screen flex items-center justify-center bg-[#1b263b] ${className}`}>
    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-400"></div>
  </div>
);

export const LoadingPulse: React.FC<{ 
  className?: string;
  children?: React.ReactNode;
}> = ({ 
  className = '', 
  children 
}) => (
  <div className={`animate-pulse ${className}`}>
    {children}
  </div>
);

// Progressive loading component
export const ProgressiveLoader: React.FC<{
  isLoading: boolean;
  skeleton: React.ReactNode;
  children: React.ReactNode;
  delay?: number;
}> = ({ 
  isLoading, 
  skeleton, 
  children, 
  delay = 0 
}) => {
  const [showSkeleton, setShowSkeleton] = React.useState(isLoading);

  React.useEffect(() => {
    if (!isLoading && delay > 0) {
      const timer = setTimeout(() => setShowSkeleton(false), delay);
      return () => clearTimeout(timer);
    } else {
      setShowSkeleton(isLoading);
      return undefined;
    }
  }, [isLoading, delay]);

  return (
    <div className="relative">
      {showSkeleton && (
        <div className={`transition-opacity duration-300 ${!isLoading ? 'opacity-0' : 'opacity-100'}`}>
          {skeleton}
        </div>
      )}
      {!showSkeleton && (
        <div className="transition-opacity duration-300 opacity-100">
          {children}
        </div>
      )}
    </div>
  );
};

// Hockey-specific loading states
export const HockeyLoadingSpinner: React.FC<{ className?: string }> = ({ 
  className = '' 
}) => (
  <div className={`relative ${className}`}>
    <div className="w-12 h-12 border-4 border-blue-200 border-t-blue-600 rounded-full animate-spin" />
    <div className="absolute inset-0 flex items-center justify-center">
      <span className="text-xs font-bold text-blue-600">🏒</span>
    </div>
  </div>
);

export const BrandLoadingSkeleton: React.FC = () => (
  <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-6">
    {Array.from({ length: 12 }).map((_, i) => (
      <div key={i} className="bg-white p-4 rounded-lg border border-gray-200">
        <SkeletonImage className="mb-2" aspectRatio="square" />
        <SkeletonText width="half" />
      </div>
    ))}
  </div>
);

export const ServiceLoadingSkeleton: React.FC = () => (
  <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
    {Array.from({ length: 4 }).map((_, i) => (
      <div key={i} className="bg-white/10 p-8 rounded-xl border border-white/20">
        <div className="w-16 h-16 bg-gray-300 rounded-lg mb-6 animate-pulse" />
        <SkeletonText lines={3} className="text-white" />
      </div>
    ))}
  </div>
);

export const ProductLoadingSkeleton: React.FC = () => (
  <div className="grid gap-6" style={{ gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))' }}>
    {Array.from({ length: 8 }).map((_, i) => (
      <div key={i} className="bg-white rounded-lg shadow p-4 border border-gray-100">
        <SkeletonImage className="w-32 h-32 mx-auto mb-4" aspectRatio="square" />
        <SkeletonText lines={2} className="mb-2" />
        <SkeletonText width="quarter" />
      </div>
    ))}
  </div>
);

export default {
  SkeletonText,
  SkeletonImage,
  SkeletonCard,
  SkeletonGrid,
  LoadingSpinner,
  LoadingDots,
  LoadingPulse,
  SectionLoader,
  AppLoader,
  ProgressiveLoader,
  HockeyLoadingSpinner,
  BrandLoadingSkeleton,
  ServiceLoadingSkeleton,
  ProductLoadingSkeleton
};