import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Download, X, Wifi, WifiOff } from 'lucide-react';
import { showInstallPrompt, isPWAInstalled, getNetworkStatus } from '@/utils/pwa';
import { useToast } from '@/hooks/use-toast';

interface PWAInstallButtonProps {
  className?: string;
  variant?: 'default' | 'outline' | 'ghost';
  size?: 'default' | 'sm' | 'lg';
}

export const PWAInstallButton: React.FC<PWAInstallButtonProps> = ({
  className = '',
  variant = 'default',
  size = 'default'
}) => {
  const [showInstall, setShowInstall] = useState(false);
  const [isInstalled, setIsInstalled] = useState(false);
  const [isOnline, setIsOnline] = useState(true);
  const [showUpdateNotification, setShowUpdateNotification] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    // Check if PWA is already installed
    setIsInstalled(isPWAInstalled());
    
    // Check network status
    setIsOnline(getNetworkStatus().online);

    // Listen for PWA events
    const handleShowInstall = () => setShowInstall(true);
    const handleHideInstall = () => setShowInstall(false);
    const handleUpdateAvailable = () => setShowUpdateNotification(true);
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('pwa:showInstall', handleShowInstall);
    window.addEventListener('pwa:hideInstall', handleHideInstall);
    window.addEventListener('pwa:updateAvailable', handleUpdateAvailable);
    window.addEventListener('pwa:online', handleOnline);
    window.addEventListener('pwa:offline', handleOffline);

    // Fallback: Show install button if PWA criteria are met but beforeinstallprompt hasn't fired
    // This can happen in development or when browser doesn't trigger the event
    const fallbackTimer = setTimeout(() => {
      if (!isPWAInstalled() && !showInstall) {
        console.log('PWA: Showing fallback install button');
        setShowInstall(true);
      }
    }, 3000); // Wait 3 seconds for beforeinstallprompt

    return () => {
      window.removeEventListener('pwa:showInstall', handleShowInstall);
      window.removeEventListener('pwa:hideInstall', handleHideInstall);
      window.removeEventListener('pwa:updateAvailable', handleUpdateAvailable);
      window.removeEventListener('pwa:online', handleOnline);
      window.removeEventListener('pwa:offline', handleOffline);
      clearTimeout(fallbackTimer);
    };
  }, []);

  const handleInstallClick = async () => {
    try {
      const installed = await showInstallPrompt();
      if (installed) {
        setShowInstall(false);
        setIsInstalled(true);
        toast({
          title: "App Installed!",
          description: "Ice Box Hockey has been added to your home screen.",
        });
      }
    } catch (error) {
      console.error('Install failed:', error);
      toast({
        title: "Installation Failed",
        description: "Unable to install the app. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleUpdateClick = () => {
    window.location.reload();
  };

  const handleDismissUpdate = () => {
    setShowUpdateNotification(false);
  };

  // Don't show install button if already installed
  if (isInstalled && !showUpdateNotification) {
    return null;
  }

  return (
    <div className="flex flex-col gap-2">
      {/* Network Status Indicator */}
      <div className="flex items-center gap-2 text-sm text-gray-700 dark:text-gray-300">
        {isOnline ? (
          <>
            <Wifi className="h-4 w-4 text-green-500" />
            <span>Online</span>
          </>
        ) : (
          <>
            <WifiOff className="h-4 w-4 text-red-500" />
            <span>Offline - Using cached content</span>
          </>
        )}
      </div>

      {/* Update Notification */}
      {showUpdateNotification && (
        <div className="flex items-center gap-2 p-3 bg-blue-50 border border-blue-200 rounded-lg">
          <div className="flex-1">
            <p className="text-sm font-medium text-blue-900">
              App Update Available
            </p>
            <p className="text-xs text-blue-700">
              A new version is ready to install
            </p>
          </div>
          <div className="flex gap-2">
            <Button
              size="sm"
              onClick={handleUpdateClick}
              className="bg-blue-600 hover:bg-blue-700"
            >
              Update
            </Button>
            <Button
              size="sm"
              variant="ghost"
              onClick={handleDismissUpdate}
              aria-label="Dismiss update notification"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>
      )}

      {/* Install Button */}
      {showInstall && !isInstalled && (
        <Button
          onClick={handleInstallClick}
          variant="outline"
          size={size}
          className={`flex items-center gap-2 bg-gray-500 hover:bg-gray-600 text-green-400 hover:text-green-300 border-gray-400 hover:border-gray-500 ${className}`}
        >
          <Download className="h-4 w-4" />
          Install App
        </Button>
      )}
    </div>
  );
};

export default PWAInstallButton;