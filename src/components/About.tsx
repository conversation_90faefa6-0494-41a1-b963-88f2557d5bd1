
//#Website Navigation
//#Hero
//#About
//#Services
//#Products
//#Brands
//#Footer

import Map from './Map';
import Heading from './Heading';

// Constants
const STORE_LOCATION = {
  lat: 33.7969,
  lng: -118.3001,
  address: '23770 S Western Ave, Harbor City, CA 90710'
};

const STORE_HOURS = [
  { day: 'Monday - Friday', hours: '10:00 AM - 8:00 PM' },
  { day: 'Saturday', hours: '10:00 AM - 8:00 PM' },
  { day: 'Sunday', hours: '12:00 PM - 6:00 PM' }
] as const;

const About = () => {
  // Remove unnecessary state since Map component now handles its own loading
  // This improves performance by removing redundant state management
  return (
    <section id="about" className="py-20 text-white" style={{ backgroundColor: '#1b263b' }}>
      <div className="container mx-auto px-8">
        {/* Main Heading */}
        <div className="text-center mb-12">
          <Heading as="h2">
            About The Ice Box
          </Heading>
        </div>
        
        {/* Two-column layout for content and map */}
        <div className="grid lg:grid-cols-2 gap-12 items-start">
          {/* Left Column - About Content */}
          <div className="space-y-8 text-white text-lg leading-relaxed opacity-90">
            {/* The Ice Box Difference Section */}
            <div className="space-y-4">
              <Heading as="h3" className="text-2xl text-blue-300 mb-4">The Ice Box Difference</Heading>
              <p>
                At The Ice Box, our team of players, coaches, and hockey specialists 
                brings unparalleled expertise to every interaction. We don't just sell gear, we provide comprehensive solutions that the big box retailers can't match. Our staff's deep understanding of the game ensures you 
                get the right equipment for your specific needs. 
              </p>
            </div>

            {/* Community and Convenience Section */}
            <div className="space-y-4">
              <Heading as="h3" className="text-2xl text-blue-300 mb-4">Community and Convenience</Heading>
              <p>
                Located inside the Skating Edge Arena, home of the Bay Harbor Red Wings, 
                we're your go-to spot for everything hockey. Whether you need quick purchases, 
                last-minute needs, or expert advice.
              </p>
              <p>
                More than just a store, we're part of the local hockey community. We support 
                local teams and events, building relationships that go beyond just selling equipment.
              </p>
            </div>
          </div>
          
          {/* Right Column - Find Us Section */}
          <div className="sticky top-6">
            <Heading as="h2" className="text-3xl md:text-4xl mb-8">Find Us</Heading>
            <div className="bg-white/5 backdrop-blur-sm p-6 rounded-xl border border-white/10">
              {/* Interactive Map */}
              <Map 
                address={STORE_LOCATION.address}
                className="rounded-lg overflow-hidden shadow-xl w-full h-[250px] sm:h-[300px] md:h-[350px]"
              />
              
              {/* Store Hours */}
              <div className="mt-6">
                <div className="bg-white/5 p-4 rounded-lg">
                  <Heading as="h3" className="text-lg font-semibold mb-3">Store Hours</Heading>
                  <div className="space-y-1.5 text-sm text-white/90">
                    {STORE_HOURS.map(({ day, hours }) => (
                      <p key={day} className="flex justify-between">
                        <span>{day}:</span> 
                        <span>{hours}</span>
                      </p>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default About;

