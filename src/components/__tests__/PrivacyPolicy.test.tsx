import React from 'react';
import { render, screen } from '@testing-library/react';
import { <PERSON>rowserRouter } from 'react-router-dom';
import { HelmetProvider } from 'react-helmet-async';
import PrivacyPolicy from '../../pages/PrivacyPolicy';

/**
 * Privacy Policy component unit tests
 * 
 * Tests the Privacy Policy component rendering and content:
 * - Component renders without crashing
 * - Key sections are present
 * - Contact information is displayed
 * - SEO elements are properly set
 */

// Helper function to render component with required providers
const renderWithProviders = (component: React.ReactElement) => {
  return render(
    <HelmetProvider>
      <BrowserRouter>
        {component}
      </BrowserRouter>
    </HelmetProvider>
  );
};

describe('PrivacyPolicy Component', () => {
  test('renders without crashing', () => {
    renderWithProviders(<PrivacyPolicy />);
    expect(screen.getByRole('heading', { level: 1 })).toBeInTheDocument();
  });

  test('displays main heading', () => {
    renderWithProviders(<PrivacyPolicy />);
    expect(screen.getByRole('heading', { name: /privacy policy/i, level: 1 })).toBeInTheDocument();
  });

  test('displays last updated date', () => {
    renderWithProviders(<PrivacyPolicy />);
    expect(screen.getByText(/last updated:/i)).toBeInTheDocument();
    
    // Check that it shows current year
    const currentYear = new Date().getFullYear().toString();
    expect(screen.getByText(new RegExp(currentYear))).toBeInTheDocument();
  });

  test('displays key privacy policy sections', () => {
    renderWithProviders(<PrivacyPolicy />);
    
    // Check for main sections
    expect(screen.getByRole('heading', { name: /information we collect/i })).toBeInTheDocument();
    expect(screen.getByRole('heading', { name: /how we use your information/i })).toBeInTheDocument();
    expect(screen.getByRole('heading', { name: /data storage and security/i })).toBeInTheDocument();
    expect(screen.getByRole('heading', { name: /third-party services/i })).toBeInTheDocument();
    expect(screen.getByRole('heading', { name: /your privacy rights/i })).toBeInTheDocument();
    expect(screen.getByRole('heading', { name: /contact us/i })).toBeInTheDocument();
  });

  test('displays contact information', () => {
    renderWithProviders(<PrivacyPolicy />);
    
    // Check for business contact details
    expect(screen.getByText(/the ice box hockey/i)).toBeInTheDocument();
    expect(screen.getByText(/23770 s western ave/i)).toBeInTheDocument();
    expect(screen.getByText(/harbor city, ca 90710/i)).toBeInTheDocument();
    
    // Check for email link
    const emailLink = screen.getByRole('link', { name: /<EMAIL>/i });
    expect(emailLink).toBeInTheDocument();
    expect(emailLink).toHaveAttribute('href', 'mailto:<EMAIL>');
  });

  test('displays information collection details', () => {
    renderWithProviders(<PrivacyPolicy />);
    
    // Check for specific information types mentioned
    expect(screen.getByText(/personal information/i)).toBeInTheDocument();
    expect(screen.getByText(/automatically collected information/i)).toBeInTheDocument();
    expect(screen.getByText(/payment information/i)).toBeInTheDocument();
    expect(screen.getByText(/browser type/i)).toBeInTheDocument();
  });

  test('displays security measures', () => {
    renderWithProviders(<PrivacyPolicy />);
    
    // Check for security-related content
    expect(screen.getByText(/ssl encryption/i)).toBeInTheDocument();
    expect(screen.getByText(/secure servers/i)).toBeInTheDocument();
    expect(screen.getByText(/security audits/i)).toBeInTheDocument();
  });

  test('displays user rights information', () => {
    renderWithProviders(<PrivacyPolicy />);
    
    // Check for user rights
    expect(screen.getByText(/access.*request access to your personal information/i)).toBeInTheDocument();
    expect(screen.getByText(/correction.*request correction of inaccurate information/i)).toBeInTheDocument();
    expect(screen.getByText(/deletion.*request deletion of your personal information/i)).toBeInTheDocument();
  });

  test('displays third-party services information', () => {
    renderWithProviders(<PrivacyPolicy />);
    
    // Check for third-party services mentioned
    expect(screen.getByText(/google analytics/i)).toBeInTheDocument();
    expect(screen.getByText(/payment processing/i)).toBeInTheDocument();
    expect(screen.getByText(/social media integration/i)).toBeInTheDocument();
  });

  test('displays cookies information', () => {
    renderWithProviders(<PrivacyPolicy />);
    
    // Check for cookies section
    expect(screen.getByRole('heading', { name: /cookies and tracking technologies/i })).toBeInTheDocument();
    expect(screen.getByText(/essential cookies/i)).toBeInTheDocument();
    expect(screen.getByText(/analytics cookies/i)).toBeInTheDocument();
    expect(screen.getByText(/preference cookies/i)).toBeInTheDocument();
  });

  test('displays children privacy policy', () => {
    renderWithProviders(<PrivacyPolicy />);
    
    // Check for children's privacy section
    expect(screen.getByRole('heading', { name: /children's privacy/i })).toBeInTheDocument();
    expect(screen.getByText(/not intended for children under the age of 13/i)).toBeInTheDocument();
  });

  test('has proper responsive styling classes', () => {
    renderWithProviders(<PrivacyPolicy />);
    
    // Check for responsive container classes
    const mainContainer = screen.getByRole('heading', { level: 1 }).closest('div');
    expect(mainContainer).toHaveClass('container', 'mx-auto');
  });

  test('displays website link in contact section', () => {
    renderWithProviders(<PrivacyPolicy />);
    
    // Check for website link
    const websiteLink = screen.getByRole('link', { name: /iceboxhockey.com/i });
    expect(websiteLink).toBeInTheDocument();
    expect(websiteLink).toHaveAttribute('href', 'https://iceboxhockey.com');
  });
});