import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import { ProductCard } from '../ProductCard';
import { Product } from '../../types/product';

/**
 * Test suite for ProductCard component
 * Verifies rendering, prop handling, and accessibility
 */
describe('ProductCard', () => {
  const mockProduct: Product = {
    id: '1',
    name: 'Test Hockey Stick',
    price: '$199.99',
    image: '/test-image.jpg',
    description: 'A high-quality hockey stick for professional players',
    category: 'sticks',
    inStock: true
  };

  const mockProductWithoutDescription: Product = {
    id: '2',
    name: 'Test Helmet',
    price: '$149.99',
    image: '/test-helmet.jpg',
    description: '',
    category: 'protective',
    inStock: true
  };

  /**
   * Test basic rendering functionality
   */
  it('renders product information correctly', () => {
    render(<ProductCard product={mockProduct} />);
    
    // Check if product name is displayed
    expect(screen.getByText('Test Hockey Stick')).toBeInTheDocument();
    
    // Check if price is displayed
    expect(screen.getByText('$199.99')).toBeInTheDocument();
    
    // Check if description is displayed
    expect(screen.getByText('A high-quality hockey stick for professional players')).toBeInTheDocument();
    
    // Check if image is rendered with correct attributes
    const image = screen.getByAltText('Test Hockey Stick');
    expect(image).toBeInTheDocument();
    expect(image).toHaveAttribute('src', '/test-image.jpg');
  });

  /**
   * Test conditional description rendering
   */
  it('does not render description when not provided', () => {
    render(<ProductCard product={mockProductWithoutDescription} />);
    
    // Check that product name and price are still displayed
    expect(screen.getByText('Test Helmet')).toBeInTheDocument();
    expect(screen.getByText('$149.99')).toBeInTheDocument();
    
    // Check that description paragraph is not rendered
    expect(screen.queryByText('A high-quality hockey stick for professional players')).not.toBeInTheDocument();
  });

  /**
   * Test CSS classes and styling
   */
  it('applies correct CSS classes', () => {
    const { container } = render(<ProductCard product={mockProduct} />);
    
    // Check main container classes
    const cardElement = container.firstChild as HTMLElement;
    expect(cardElement).toHaveClass('bg-white', 'rounded-lg', 'shadow', 'p-4');
    expect(cardElement).toHaveClass('hover:shadow-lg', 'transition-shadow');
  });

  /**
   * Test accessibility features
   */
  it('has proper accessibility attributes', () => {
    render(<ProductCard product={mockProduct} />);
    
    // Check image alt text
    const image = screen.getByAltText('Test Hockey Stick');
    expect(image).toBeInTheDocument();
    
    // Verify the card structure is accessible
    const productName = screen.getByText('Test Hockey Stick');
    expect(productName).toHaveClass('font-semibold');
  });

  /**
   * Test edge cases
   */
  it('handles missing optional properties gracefully', () => {
    const minimalProduct: Product = {
      id: '3',
      name: 'Minimal Product',
      price: '$99.99',
      image: '/minimal.jpg',
      description: '',
      inStock: true
    };

    render(<ProductCard product={minimalProduct} />);
    
    expect(screen.getByText('Minimal Product')).toBeInTheDocument();
    expect(screen.getByText('$99.99')).toBeInTheDocument();
  });

  /**
   * Test hover interactions
   */
  it('has hover effects applied', () => {
    const { container } = render(<ProductCard product={mockProduct} />);
    
    const cardElement = container.firstChild as HTMLElement;
    expect(cardElement).toHaveClass('hover:shadow-lg', 'transition-shadow');
  });
});