import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import { Shield } from 'lucide-react';
import CategoryCard from '../CategoryCard';

/**
 * Test suite for CategoryCard component
 * Verifies rendering, prop handling, and responsive behavior
 */
describe('CategoryCard', () => {
  const mockProps = {
    icon: Shield,
    title: 'Test Category',
    description: 'This is a test category description',
    items: ['Item 1', 'Item 2', 'Item 3']
  };

  /**
   * Test basic rendering functionality
   */
  it('renders category information correctly', () => {
    render(<CategoryCard {...mockProps} />);
    
    // Check if title is displayed
    expect(screen.getByText('Test Category')).toBeInTheDocument();
    
    // Check if description is displayed
    expect(screen.getByText('This is a test category description')).toBeInTheDocument();
    
    // Check if all items are displayed
    expect(screen.getByText('Item 1')).toBeInTheDocument();
    expect(screen.getByText('Item 2')).toBeInTheDocument();
    expect(screen.getByText('Item 3')).toBeInTheDocument();
  });

  /**
   * Test icon rendering
   */
  it('renders the icon correctly', () => {
    const { container } = render(<CategoryCard {...mockProps} />);
    
    // Check if icon container exists with correct classes
    const iconContainer = container.querySelector('.bg-gradient-to-br');
    expect(iconContainer).toBeInTheDocument();
    expect(iconContainer).toHaveClass('from-blue-500', 'to-blue-600');
  });

  /**
   * Test CSS classes and styling
   */
  it('applies correct CSS classes', () => {
    const { container } = render(<CategoryCard {...mockProps} />);
    
    // Check main container classes
    const cardElement = container.firstChild as HTMLElement;
    expect(cardElement).toHaveClass(
      'bg-white/10',
      'backdrop-blur-sm',
      'p-8',
      'rounded-xl',
      'border',
      'border-white/20'
    );
    expect(cardElement).toHaveClass('hover:border-blue-300/50', 'transition-all', 'duration-300', 'group');
  });

  /**
   * Test items list rendering
   */
  it('renders items list with correct structure', () => {
    render(<CategoryCard {...mockProps} />);
    
    // Check if list items have correct structure
    const listItems = screen.getAllByRole('listitem');
    expect(listItems).toHaveLength(3);
    
    // Each list item should contain the bullet point and text
    listItems.forEach((item, index) => {
      expect(item).toHaveClass('flex', 'items-center', 'gap-2', 'text-white');
      expect(item).toContainHTML(`Item ${index + 1}`);
    });
  });

  /**
   * Test with empty items array
   */
  it('handles empty items array gracefully', () => {
    const propsWithEmptyItems = {
      ...mockProps,
      items: []
    };
    
    render(<CategoryCard {...propsWithEmptyItems} />);
    
    // Title and description should still be displayed
    expect(screen.getByText('Test Category')).toBeInTheDocument();
    expect(screen.getByText('This is a test category description')).toBeInTheDocument();
    
    // No list items should be present
    const listItems = screen.queryAllByRole('listitem');
    expect(listItems).toHaveLength(0);
  });

  /**
   * Test with single item
   */
  it('handles single item correctly', () => {
    const propsWithSingleItem = {
      ...mockProps,
      items: ['Single Item']
    };
    
    render(<CategoryCard {...propsWithSingleItem} />);
    
    expect(screen.getByText('Single Item')).toBeInTheDocument();
    
    const listItems = screen.getAllByRole('listitem');
    expect(listItems).toHaveLength(1);
  });

  /**
   * Test hover effects
   */
  it('has hover effects applied', () => {
    const { container } = render(<CategoryCard {...mockProps} />);
    
    const cardElement = container.firstChild as HTMLElement;
    expect(cardElement).toHaveClass('group');
    
    // Icon container should have hover scale effect
    const iconContainer = container.querySelector('.group-hover\\:scale-110');
    expect(iconContainer).toBeInTheDocument();
  });

  /**
   * Test accessibility
   */
  it('has proper semantic structure', () => {
    render(<CategoryCard {...mockProps} />);
    
    // Check heading structure
    const heading = screen.getByRole('heading', { level: 3 });
    expect(heading).toHaveTextContent('Test Category');
    
    // Check list structure
    const list = screen.getByRole('list');
    expect(list).toBeInTheDocument();
    expect(list).toHaveClass('space-y-2');
  });
});