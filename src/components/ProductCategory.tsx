import React from 'react';
import { <PERSON>, Zap, Star, Award } from 'lucide-react';
import CategoryCard from './CategoryCard';
import Heading from './Heading';

/**
 * ProductCategory component for displaying product categories
 * @returns A section containing product categories with equipment offerings
 */
const productCategories = [
  {
    icon: Shield,
    title: "Protective Gear",
    description: "The Ice Box offers premium helmets, pads, and protective equipment from top brands to keep you safe on the ice.",
    items: ["Helmets & Cages", "Shoulder Pads", "Elbow Pads", "Shin Guards", "Protective Pants"]
  },
  {
    icon: Zap,
    title: "Sticks & Equipment",
    description: "Discover The Ice Box's selection of high-performance sticks and essential playing equipment for players at every level.",
    items: ["Hockey Sticks", "Gloves", "Skates", "Equipment Bags", "Training Aids"]
  },
  {
    icon: Star,
    title: "Apparel & Accessories",
    description: "Complete your look with The Ice Box's premium team jerseys, practice wear, and hockey lifestyle gear.",
    items: ["Jerseys & Socks", "Practice Jerseys", "Base Layers", "Casual Wear", "Accessories"]
  },
  {
    icon: Award,
    title: "Goalie Specialized",
    description: "The Ice Box specializes in complete goaltender equipment and specialized gear for the last line of defense.",
    items: ["Goalie Masks", "Chest & Arm", "Leg Pads", "Goalie Sticks", "Specialized Gear"]
  }
];

const ProductCategory: React.FC = () => {
  return (
    <>
      <div className="text-center mb-16">
        <Heading as="h2" className="mb-6">
          Equipment We Offer
        </Heading>
        <p className="text-xl text-white max-w-3xl mx-auto opacity-90 mb-12">
          At The Ice Box, we're proud to stock the finest hockey equipment from the world's leading brands and provide expert skate services to keep you performing at your best.
        </p>
      </div>
      
      <div className="grid md:grid-cols-2 lg:grid-cols-2 gap-6 mb-16">
        {productCategories.map((category, index) => (
          <CategoryCard
            key={`product-${index}`}
            icon={category.icon}
            title={category.title}
            description={category.description}
            items={category.items}
          />
        ))}
      </div>
    </>
  );
};

export default ProductCategory;