import React from 'react';

/**
 * Heading component for rendering semantic heading elements with consistent styling
 * @param children - The heading content to display
 * @param as - The HTML heading element to render (h1-h6)
 * @param className - Additional CSS classes to apply
 * @param props - Additional HTML attributes
 * @returns A styled heading element
 */
interface HeadingProps extends React.HTMLAttributes<HTMLHeadingElement> {
  /** The heading content */
  children: React.ReactNode;
  /** The HTML element to render (h1, h2, h3, etc.) */
  as?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6';
  /** Additional CSS classes */
  className?: string;
}

const Heading: React.FC<HeadingProps> = ({ 
  children, 
  as: Tag = 'h2', 
  className = '', 
  ...props 
}) => {
  return (
    <Tag 
      className={`text-4xl md:text-5xl font-bold text-white mb-6 ${className}`}
      {...props}
    >
      {children}
    </Tag>
  );
};

export default Heading;
