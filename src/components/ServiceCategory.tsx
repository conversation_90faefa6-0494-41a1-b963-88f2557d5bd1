import React from 'react';
import { Zap, Settings, Scissors, Shield, RotateCw, Thermometer } from 'lucide-react';
import CategoryCard from './CategoryCard';
import Heading from './Heading';

/**
 * ServiceCategory component for displaying skate services
 * @returns A section containing service categories with skate service offerings
 */
const serviceCategories = [
  {
    icon: Zap,
    title: "Skate Sharpening",
    description: "Precision sharpening services to match your playing style and ice conditions.",
    items: ["Standard Sharpening", "Cross-Grinding", "Radius of Hollow (ROH) Options", "Quick Turnaround"]
  },
  {
    icon: Settings,
    title: "Blade Profiling",
    description: "Custom contouring to optimize stride, agility, and performance on the ice.",
    items: ["Custom Profiling", "Performance Analysis", "Player-Specific Adjustments", "Blade Balancing", "Multi-Radius Options"]
  },
  {
    icon: Scissors,
    title: "Blade & Holder Services",
    description: "Expert blade and holder replacement to extend the life of your skates.",
    items: ["Blade Replacement", "TUUK Holder Installation", "CCM XS Holder Setup", "Runner Replacement", "Full Blade Assembly"]
  },
  {
    icon: Shield,
    title: "Eyelet Repair",
    description: "Professional repair and replacement of damaged or pulled-out lace eyelets.",
    items: ["Eyelet Replacement", "Riveting Services", "Broken Eyelet Repair", "Reinforcement", "Speed Lace Installation"]
  },
  {
    icon: RotateCw,
    title: "Holder Replacement",
    description: "Complete holder replacement services for all major skate brands and models.",
    items: ["Holder Removal", "Alignment Check", "Custom Mounting", "Blade Compatibility Check", "Professional Installation"]
  },
  {
    icon: Thermometer,
    title: "Heat Molding",
    description: "Custom skate fitting through heat molding for comfort and performance.",
    items: ["Oven Baking", "Custom Fitting", "Punch Outs", "Stretching", "Break-In Assistance"]
  }
];

const ServiceCategory: React.FC = () => {
  return (
    <>
      <div className="text-center mb-12">
        <Heading as="h3" className="text-4xl md:text-5xl font-bold text-white mb-6">Skate Services</Heading>
        <p className="text-xl text-white max-w-3xl mx-auto opacity-90 mb-8">
          Our certified technicians use state-of-the-art equipment to ensure precision and quality in every service.
        </p>
      </div>
      
      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
        {serviceCategories.map((category, index) => (
          <CategoryCard
            key={`service-${index}`}
            icon={category.icon}
            title={category.title}
            description={category.description}
            items={category.items}
            gridCols="md:grid-cols-2 lg:grid-cols-3"
          />
        ))}
      </div>
    </>
  );
};

export default ServiceCategory;