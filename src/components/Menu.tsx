/**
 * Menu.tsx
 * Accessible dropdown menu for upper right navigation.
 * - Keyboard accessible (<PERSON><PERSON>, Arrow, Enter/Escape)
 * - ARIA roles for menu and items
 * - Styled for Ice Box Hockey branding
 * 
 * Usage: <Menu />
 */
import React, { useState, useRef, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { trackNavigation } from '@/utils/analytics';

const menuItems = [
  { label: 'Home', href: '/#home' },
  { label: 'About', href: '/#about' },
  { label: 'Services', href: '/#services' },
  { label: 'Brands', href: '/#brands' },
  { label: 'Team Sales', href: '/teamsales' },
  { label: 'Contact', href: '/#contact' },
];

const Menu: React.FC = () => {
  const [open, setOpen] = useState(false);
  const buttonRef = useRef<HTMLButtonElement>(null);
  const menuRef = useRef<HTMLUListElement>(null);

  // Close on outside click
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        menuRef.current &&
        !menuRef.current.contains(event.target as Node) &&
        buttonRef.current &&
        !buttonRef.current.contains(event.target as Node)
      ) {
        setOpen(false);
      }
    }
    if (open) {
      document.addEventListener('mousedown', handleClickOutside);
    }
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [open]);

  // Keyboard navigation
  function handleKeyDown(e: React.KeyboardEvent) {
    if (e.key === 'Escape') setOpen(false);
    if (e.key === 'ArrowDown') {
      e.preventDefault();
      const first = menuRef.current?.querySelector('a');
      (first as HTMLElement)?.focus();
    }
  }

  return (
    <nav aria-label="Main Navigation" className="relative inline-block text-left">
      <button
        ref={buttonRef}
        aria-haspopup="true"
        aria-expanded={open}
        aria-controls="main-menu"
        className="p-3 rounded-full bg-white/10 hover:bg-white/20 focus:outline-none focus:ring-2 focus:ring-blue-300 transition"
        onClick={() => setOpen((v) => !v)}
        onKeyDown={handleKeyDown}
      >
        <span className="sr-only">Open menu</span>
        <svg className="w-7 h-7 text-white" fill="none" stroke="currentColor" strokeWidth={2} viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" d="M4 8h16M4 16h16" />
        </svg>
      </button>
      {open && (
        <ul
          ref={menuRef}
          id="main-menu"
          role="menu"
          aria-label="Main menu"
          className="absolute right-0 mt-2 w-48 rounded-lg shadow-lg bg-[#1b263b] ring-1 ring-white/10 z-50 focus:outline-none"
        >
          {menuItems.map((item, idx) => (
            <li key={item.label} role="none">
              <Link
                to={item.href}
                role="menuitem"
                tabIndex={0}
                className="block px-5 py-3 text-white hover:bg-blue-700/80 focus:bg-blue-700/80 transition"
                onKeyDown={(e) => {
                  if (e.key === 'ArrowDown') {
                    e.preventDefault();
                    const next = (e.target as HTMLElement).parentElement?.nextElementSibling?.querySelector('a');
                    (next as HTMLElement)?.focus();
                  }
                  if (e.key === 'ArrowUp') {
                    e.preventDefault();
                    const prev = (e.target as HTMLElement).parentElement?.previousElementSibling?.querySelector('a');
                    (prev as HTMLElement)?.focus();
                  }
                  if (e.key === 'Escape') {
                    setOpen(false);
                    buttonRef.current?.focus();
                  }
                }}
                onClick={() => {
                  setOpen(false);
                  trackNavigation(item.label.toLowerCase());
                }}
              >
                {item.label}
              </Link>
            </li>
          ))}
        </ul>
      )}
    </nav>
  );
};

export default Menu;
