
//#Website Navigation
//#Hero
//#About
//#Services
//#Products
//#Brands
//#Footer

import { ChevronRight, MapPin, Phone } from 'lucide-react';
import Heading from './Heading';


const Hero = () => {
  return (
    <section id="home" className="relative min-h-screen text-white overflow-hidden flex items-center" style={{ backgroundColor: '#1b263b' }}>
      {/* Background image layer */}
      <div
        className="absolute inset-0 w-full h-full z-0"
        aria-hidden="true"
        style={{
          backgroundImage: "url('/Whisk_cbee896f2a.webp')",
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundRepeat: 'no-repeat',
          opacity: 0.1,
          pointerEvents: 'none',
        }}
      ></div>
      {/* Background pattern */}
      <div className="absolute inset-0 opacity-10 z-0">
        <div className="absolute inset-0 bg-[linear-gradient(45deg,transparent_35%,rgba(255,255,255,.1)_50%,transparent_65%)] bg-[length:20px_20px]"></div>
      </div>
      

      
      <div className="relative z-10 container mx-auto px-8 pt-28 pb-16">
        <div className="max-w-4xl">
          <Heading as="h1" className="text-6xl md:text-7xl leading-tight mb-6">
            The Ice Box
          </Heading>
          <Heading as="h2" className="text-4xl md:text-5xl text-blue-300 mb-6">
            Your Hockey Specialists
          </Heading>
          
          <p className="text-xl md:text-2xl text-white mb-8 max-w-2xl leading-relaxed opacity-90">
            Your one-stop shop for professional-grade hockey equipment, expert fitting services, and personalized recommendations from players who live and breathe the game.
          </p>
          

          <div className="flex flex-col sm:flex-row gap-6 text-white opacity-90">
          </div>
        </div>
      </div>
      
      {/* Decorative elements */}
      <div className="absolute bottom-0 left-0 w-full h-32 bg-gradient-to-t from-white/5 to-transparent"></div>
      <div className="absolute top-1/4 right-10 w-64 h-64 bg-blue-500/20 rounded-full blur-3xl"></div>
      <div className="absolute bottom-1/4 left-10 w-48 h-48 bg-cyan-400/20 rounded-full blur-3xl"></div>


    </section>
  );
};

export default Hero;

