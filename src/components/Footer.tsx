
//#Website Navigation
//#Hero
//#About
//#Services
//#Products
//#Brands
//#Footer

import { MapPin, Phone, Mail } from 'lucide-react';
import { trackSocialClick, trackContactInteraction } from '@/utils/analytics';

const Footer = () => {
  return (
    <footer id="contact" className="bg-[#1b263b] text-white border-t border-white/20">
      {/* Main Footer Content */}
      <div className="container mx-auto px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-12 gap-12 md:gap-8 lg:gap-12">
          {/* Brand & Description */}
          <div className="md:col-span-12 lg:col-span-4 mb-8 md:mb-0">
            <h3 className="text-2xl font-bold mb-6 text-blue-400 flex items-center">
              <span className="bg-blue-400/20 p-2 rounded-lg mr-3">🏒</span>
              The Ice Box
            </h3>
            <p className="text-white/90 mb-6 leading-relaxed">
            Your premier destination for professional hockey equipment and expert services in Harbor City. Where champions gear up.
            </p>
            <div className="flex space-x-6">
              <a 
                href="https://www.facebook.com/people/Ice-Box-Hockey-Shop/61577383866320/" 
                target="_blank" 
                rel="noopener noreferrer" 
                className="text-white/80 hover:text-blue-400 transition-colors"
                onClick={() => trackSocialClick('facebook')}
              >
                <span className="sr-only">Facebook</span>
                <svg className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z" />
                </svg>
              </a>
              <a 
                href="https://www.instagram.com/iceboxhockey_ca/" 
                target="_blank" 
                rel="noopener noreferrer" 
                className="text-white/80 hover:text-blue-400 transition-colors"
                onClick={() => trackSocialClick('instagram')}
              >
                <span className="sr-only">Instagram</span>
                <svg className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zM12 0C8.741 0 8.333.014 7.053.072 2.695.272.273 2.69.073 7.052.014 8.333 0 8.741 0 12c0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98C8.333 23.987 8.741 24 12 24c3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98C15.668.014 15.259 0 12 0zm0 5.838a6.162 6.162 0 100 12.324 6.162 6.162 0 000-12.324zM12 16a4 4 0 110-8 4 4 0 010 8zm6.406-11.845a1.44 1.44 0 100 2.881 1.44 1.44 0 000-2.881z" />
                </svg>
              </a>

            </div>
          </div>


          {/* Contact Info */}
          <div className="md:col-span-6 lg:col-span-4 mb-8 md:mb-0">
            <h3 className="text-lg font-semibold text-blue-400 mb-6 uppercase tracking-wider">Contact Us</h3>
            <address className="not-italic space-y-4">
              <div className="flex items-start">
                <MapPin className="h-5 w-5 text-blue-400 mt-1 flex-shrink-0" />
                <span className="ml-3 text-white/90">23770 S Western Ave<br />Harbor City, CA 90710</span>
              </div>
              {/* <div className="flex items-center">
                <Phone className="h-5 w-5 text-blue-400" />
                <a href="tel:5551237825" className="ml-3 text-white/90 hover:text-blue-400 transition-colors">(555) 123-PUCK</a>
              </div> */}
              <div className="flex items-center">
                <Mail className="h-5 w-5 text-blue-400" />
                <a 
                  href="mailto:<EMAIL>" 
                  className="ml-3 text-white/90 hover:text-blue-400 transition-colors"
                  onClick={() => trackContactInteraction('email')}
                >
                  <EMAIL>
                </a>
              </div>
            </address>
          </div>

          {/* Store Hours */}
          <div className="md:col-span-6 lg:col-span-4">
            <h3 className="text-lg font-semibold text-blue-400 mb-6 uppercase tracking-wider">Store Hours</h3>
            <div className="bg-white/10 p-6 rounded-lg">
              <ul className="space-y-3">
                <li className="flex justify-between">
                  <span className="text-white/90">Monday - Friday</span>
                  <span className="text-white font-medium">11:00 AM - 8:00 PM</span>
                </li>
                <li className="flex justify-between">
                  <span className="text-white/90">Saturday</span>
                  <span className="text-white font-medium">09:00 AM - 7:00 PM</span>
                </li>
                <li className="flex justify-between">
                  <span className="text-white/90">Sunday</span>
                  <span className="text-white font-medium">09:00 PM - 7:00 PM</span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      {/* Copyright & Legal */}
      <div className="border-t border-white/20 py-6">
        <div className="container mx-auto px-8">
            <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <p className="text-white/75 text-sm">
              &copy; {new Date().getFullYear()} The Ice Box. All rights reserved.
            </p>
            <div className="flex space-x-6 mt-4 md:mt-0">
              <a href="#" className="text-white/75 hover:text-blue-400 text-sm transition-colors">Privacy Policy</a>
              <a href="#" className="text-white/75 hover:text-blue-400 text-sm transition-colors">Terms of Service</a>
              <a href="#" className="text-white/75 hover:text-blue-400 text-sm transition-colors">Sitemap</a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;

