//#Website Navigation
//#Hero
//#About
//#Services
//#Products
//#Brands
//#Footer

import { useState, useRef, useEffect } from 'react';
import { MapPin } from 'lucide-react';
import { trackMapInteraction } from '@/utils/analytics';
import { LoadingSpinner } from '@/components/LoadingStates';

interface MapProps {
  /**
   * The address to display on the map.
   */
  address?: string;
  
  /**
   * Additional CSS class name for the map container
   */
  className?: string;
}

const Map = (props: MapProps) => {
  const { 
    address = "23770 S Western Ave, Harbor City, CA 90710", 
    className = '' 
  } = props;
  const [isVisible, setIsVisible] = useState(false);
  const [isLoaded, setIsLoaded] = useState(false);
  const mapRef = useRef<HTMLDivElement>(null);

  // Use Intersection Observer to load map only when visible
  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting && !isVisible) {
          setIsVisible(true);
          // Track map view
          trackMapInteraction('view');
          // Add a small delay to ensure smooth loading
          setTimeout(() => setIsLoaded(true), 100);
        }
      },
      {
        threshold: 0.1,
        rootMargin: '50px'
      }
    );

    if (mapRef.current) {
      observer.observe(mapRef.current);
    }

    return () => {
      if (mapRef.current) {
        observer.unobserve(mapRef.current);
      }
    };
  }, [isVisible]);

  return (
    <div ref={mapRef} className={`relative w-full ${className}`}>
      <div className="h-[500px] md:h-[600px] w-full">
        {isVisible ? (
          isLoaded ? (
            <iframe
              src={`https://www.google.com/maps?q=${encodeURIComponent(address)}&output=embed`}
              width="100%"
              height="100%"
              style={{ border: 0 }}
              allowFullScreen={true}
              loading="lazy"
              referrerPolicy="no-referrer-when-downgrade"
              className='grayscale-[70%] invert-[90%] rounded-lg shadow-xl transition-opacity duration-300'
              onLoad={() => {
                setIsLoaded(true);
                trackMapInteraction('click');
              }}
            />
          ) : (
            <div className="w-full h-full bg-gray-200 rounded-lg flex items-center justify-center">
              <LoadingSpinner size="md" />
            </div>
          )
        ) : (
          <div className="w-full h-full bg-gray-100 rounded-lg flex items-center justify-center">
            <div className="text-center">
              <MapPin className="h-12 w-12 text-gray-400 mx-auto mb-2" />
              <p className="text-gray-500">Map loading...</p>
            </div>
          </div>
        )}
      </div>
      <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 bg-white/90 backdrop-blur-sm px-4 py-2 rounded-lg shadow-lg flex items-center gap-2">
        <MapPin className="h-5 w-5 text-blue-500 flex-shrink-0" />
        <span className="text-sm font-medium text-gray-800">Located Inside the Skating Edge Ice Arena</span>
      </div>
    </div>
  );
};

export default Map;