import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { Suspense, lazy } from "react";
import { HelmetProvider } from "react-helmet-async";
import Layout from "./components/Layout";
import usePerformanceMonitor from "./hooks/usePerformanceMonitor";
import { AppLoader } from "@/components/LoadingStates";

// Lazy load components for better performance
const Index = lazy(() => import("./pages/Index"));
const NotFound = lazy(() => import("./pages/NotFound"));
const TeamSales = lazy(() => import("./pages/TeamSales"));
const HarborCity = lazy(() => import("./pages/HarborCity"));
const JerseyCustomizer = lazy(() => import("./pages/JerseyCustomizer"));
const PrivacyPolicy = lazy(() => import("./pages/PrivacyPolicy"));

// Only import dev tools in development and lazy load them
const StagewiseDevTools = import.meta.env.DEV ? lazy(async () => {
  const { StagewiseToolbar } = await import("@stagewise/toolbar-react");
  const StagewisePlugins = await import("@stagewise-plugins/react");
  
  return {
    default: () => (
      <StagewiseToolbar
        config={{
          plugins: [StagewisePlugins.ReactPlugin],
        }}
      />
    )
  };
}) : null;

// Optimize QueryClient configuration
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minutes
      gcTime: 10 * 60 * 1000, // 10 minutes (formerly cacheTime)
      retry: 1,
      refetchOnWindowFocus: false,
    },
  },
});



const App = () => {
  // Monitor performance metrics
  usePerformanceMonitor();

  return (
    <HelmetProvider>
      {StagewiseDevTools && (
        <Suspense fallback={null}>
          <StagewiseDevTools />
        </Suspense>
      )}
      <QueryClientProvider client={queryClient}>
        <TooltipProvider>
          <Toaster />
          <Sonner />
          <BrowserRouter>
            <Layout>
              <Suspense fallback={<AppLoader />}>
                <Routes>
                  <Route path="/" element={<Index />} />
                  <Route path="/teamsales" element={<TeamSales />} />
                                    <Route path="/harbor-city" element={<HarborCity />} />
                  <Route path="/create-jersey" element={<JerseyCustomizer />} />
                  <Route path="/privacy-policy" element={<PrivacyPolicy />} />
                  {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
                  <Route path="*" element={<NotFound />} />
                </Routes>
              </Suspense>
            </Layout>
          </BrowserRouter>
        </TooltipProvider>
      </QueryClientProvider>
    </HelmetProvider>
  );
};

export default App;
