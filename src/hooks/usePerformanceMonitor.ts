import { useEffect } from 'react';
import { trackPerformanceMetric } from '@/utils/analytics';

interface PerformanceMetrics {
  fcp?: number; // First Contentful Paint
  lcp?: number; // Largest Contentful Paint
  fid?: number; // First Input Delay
  cls?: number; // Cumulative Layout Shift
  ttfb?: number; // Time to First Byte
}

export const usePerformanceMonitor = () => {
  useEffect(() => {
    // Only run in production
    if (import.meta.env.DEV) return;

    const metrics: PerformanceMetrics = {};

    // Measure First Contentful Paint
    const fcpObserver = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      const fcpEntry = entries.find(entry => entry.name === 'first-contentful-paint');
      if (fcpEntry) {
        metrics.fcp = fcpEntry.startTime;
        trackPerformanceMetric('first_contentful_paint', fcpEntry.startTime, 'ms');
      }
    });

    // Measure Largest Contentful Paint
    const lcpObserver = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      const lastEntry = entries[entries.length - 1];
      if (lastEntry) {
        metrics.lcp = lastEntry.startTime;
        trackPerformanceMetric('largest_contentful_paint', lastEntry.startTime, 'ms');
      }
    });

    // Measure First Input Delay
    const fidObserver = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      entries.forEach((entry) => {
        if (entry.name === 'first-input') {
          metrics.fid = (entry as any).processingStart - entry.startTime;
          trackPerformanceMetric('first_input_delay', metrics.fid, 'ms');
        }
      });
    });

    // Measure Cumulative Layout Shift
    const clsObserver = new PerformanceObserver((list) => {
      let clsValue = 0;
      const entries = list.getEntries();
      entries.forEach((entry) => {
        if (!(entry as any).hadRecentInput) {
          clsValue += (entry as any).value;
        }
      });
      metrics.cls = clsValue;
      trackPerformanceMetric('cumulative_layout_shift', clsValue, 'score');
    });

    // Start observing
    try {
      fcpObserver.observe({ entryTypes: ['paint'] });
      lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });
      fidObserver.observe({ entryTypes: ['first-input'] });
      clsObserver.observe({ entryTypes: ['layout-shift'] });
    } catch (error) {
      // Performance monitoring not supported in this browser
    }

    // Measure Time to First Byte
    const measureTTFB = () => {
      const navigationEntry = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      if (navigationEntry) {
        metrics.ttfb = navigationEntry.responseStart - navigationEntry.requestStart;
        trackPerformanceMetric('time_to_first_byte', metrics.ttfb, 'ms');
      }
    };

    // Measure TTFB when DOM is loaded
    if (document.readyState === 'complete') {
      measureTTFB();
    } else {
      window.addEventListener('load', measureTTFB);
    }

    // Send metrics to analytics after 5 seconds
    const timeout = setTimeout(() => {
      // In production, send these to an analytics service
      // analytics.track('performance_metrics', metrics);
    }, 5000);

    return () => {
      clearTimeout(timeout);
      fcpObserver.disconnect();
      lcpObserver.disconnect();
      fidObserver.disconnect();
      clsObserver.disconnect();
      window.removeEventListener('load', measureTTFB);
    };
  }, []);
};

export default usePerformanceMonitor;