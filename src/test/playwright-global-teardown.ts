/**
 * Global Playwright teardown for verification system integration
 */

import fs from 'fs';
import path from 'path';
import kill from 'tree-kill';
import { exec } from 'child_process';

async function globalTeardown() {
  // Stop the web server
  if (process.env.PLAYWRIGHT_WEBSERVER_PID) {
    const serverPid = parseInt(process.env.PLAYWRIGHT_WEBSERVER_PID, 10);
    console.log(`Stopping web server with PID: ${serverPid}`);
    await new Promise<void>((resolve, reject) => {
      kill(serverPid, 'SIGKILL', (err) => {
        if (err) {
          console.error('Failed to kill web server:', err);
          return reject(err);
        }
        console.log('Web server stopped successfully.');
        resolve();
      });
    });
  }
  const testRunId = process.env.PLAYWRIGHT_TEST_RUN_ID;
  
  if (testRunId) {
    console.log(`Playwright global teardown completed. Test run ID: ${testRunId}`);
  }

  // Generate summary report for verification system
  const reportsDir = process.env.VERIFICATION_REPORTS_DIR;
  if (reportsDir && fs.existsSync(reportsDir)) {
    const summaryPath = path.join(reportsDir, 'playwright-summary.json');
    const summary = {
      testRunId,
      timestamp: new Date().toISOString(),
      reportsGenerated: true,
      artifactsLocation: path.join(reportsDir, 'playwright-artifacts'),
    };
    
    fs.writeFileSync(summaryPath, JSON.stringify(summary, null, 2));
  }

  // Clean up environment variables
  delete process.env.PLAYWRIGHT_VERIFICATION_MODE;
  delete process.env.VERIFICATION_REPORTS_DIR;
  delete process.env.PLAYWRIGHT_TEST_RUN_ID;
  delete process.env.VERIFICATION_RUN_ID;
}

export default globalTeardown;