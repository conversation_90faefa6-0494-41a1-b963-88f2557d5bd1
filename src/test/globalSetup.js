/**
 * Global Jest setup for verification system integration
 */

const fs = require('fs');
const path = require('path');

module.exports = async () => {
  // Create verification reports directory if it doesn't exist
  const reportsDir = path.join(process.cwd(), 'verification-reports');
  if (!fs.existsSync(reportsDir)) {
    fs.mkdirSync(reportsDir, { recursive: true });
  }

  // Set environment variables for verification integration
  process.env.JEST_VERIFICATION_MODE = 'true';
  process.env.VERIFICATION_REPORTS_DIR = reportsDir;

  // Create a test run identifier for correlation with verification reports
  const testRunId = `jest-${Date.now()}`;
  process.env.JEST_TEST_RUN_ID = testRunId;

  console.log(`Jest global setup completed. Test run ID: ${testRunId}`);
};