/**
 * Global Playwright setup for verification system integration
 */

import { chromium, FullConfig } from '@playwright/test';
import fs from 'fs';
import path from 'path';

async function globalSetup(config: FullConfig) {
  // Create verification reports directory if it doesn't exist
  const reportsDir = path.join(process.cwd(), 'verification-reports');
  if (!fs.existsSync(reportsDir)) {
    fs.mkdirSync(reportsDir, { recursive: true });
  }

  // Create playwright artifacts directory
  const artifactsDir = path.join(reportsDir, 'playwright-artifacts');
  if (!fs.existsSync(artifactsDir)) {
    fs.mkdirSync(artifactsDir, { recursive: true });
  }

  // Set environment variables for verification integration
  process.env.PLAYWRIGHT_VERIFICATION_MODE = 'true';
  process.env.VERIFICATION_REPORTS_DIR = reportsDir;

  // Create a test run identifier for correlation with verification reports
  const testRunId = `playwright-${Date.now()}`;
  process.env.PLAYWRIGHT_TEST_RUN_ID = testRunId;
  process.env.VERIFICATION_RUN_ID = testRunId;

  // Pre-warm browser for faster test execution
  if (process.env.CI) {
    const browser = await chromium.launch();
    await browser.close();
  }

  console.log(`Playwright global setup completed. Test run ID: ${testRunId}`);
  console.log(`Base URL: ${config.projects[0]?.use?.baseURL || 'http://localhost:8080'}`);
}

export default globalSetup;