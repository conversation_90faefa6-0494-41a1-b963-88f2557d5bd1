/**
 * Global Jest teardown for verification system integration
 */

const fs = require('fs');
const path = require('path');

module.exports = async () => {
  const testRunId = process.env.JEST_TEST_RUN_ID;
  
  if (testRunId) {
    console.log(`Jest global teardown completed. Test run ID: ${testRunId}`);
  }

  // Clean up environment variables
  delete process.env.JEST_VERIFICATION_MODE;
  delete process.env.VERIFICATION_REPORTS_DIR;
  delete process.env.JEST_TEST_RUN_ID;
};