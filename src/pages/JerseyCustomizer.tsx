import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

const JerseyCustomizer = () => {
  const [name, setName] = useState('');
  const [number, setNumber] = useState('');
  const [color, setColor] = useState('#ffffff');
  const [style, setStyle] = useState('home');

  return (
    <div className="container mx-auto py-8">
      <h1 className="text-3xl font-bold mb-4">Custom Jersey Designer</h1>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        <div>
          <Card>
            <CardHeader>
              <CardTitle>Jersey Preview</CardTitle>
            </CardHeader>
            <CardContent>
              <div
                className="w-full h-96 border rounded-lg flex items-center justify-center text-black font-bold text-4xl"
                style={{ backgroundColor: color }}
              >
                <div>
                  <div className="text-center">{name}</div>
                  <div className="text-center">{number}</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
        <div>
          <Card>
            <CardHeader>
              <CardTitle>Customization Options</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label htmlFor="name">Name</label>
                <Input id="name" value={name} onChange={(e) => setName(e.target.value)} />
              </div>
              <div>
                <label htmlFor="number">Number</label>
                <Input id="number" type="number" value={number} onChange={(e) => setNumber(e.target.value)} />
              </div>
              <div>
                <label htmlFor="color">Color</label>
                <Input id="color" type="color" value={color} onChange={(e) => setColor(e.target.value)} />
              </div>
              <div>
                <label htmlFor="style">Style</label>
                <Select value={style} onValueChange={setStyle}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select style" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="home">Home</SelectItem>
                    <SelectItem value="away">Away</SelectItem>
                    <SelectItem value="alternate">Alternate</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <Button className="w-full">Add to Cart</Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default JerseyCustomizer;