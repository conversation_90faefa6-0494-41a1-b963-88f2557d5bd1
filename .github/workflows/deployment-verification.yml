name: Production Deployment Verification

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]
  workflow_dispatch:
    inputs:
      skip_stages:
        description: 'Comma-separated list of stages to skip'
        required: false
        default: ''
      verbose:
        description: 'Enable verbose logging'
        type: boolean
        default: false

jobs:
  verification:
    name: Run Verification Pipeline
    runs-on: ubuntu-latest
    timeout-minutes: 30
    
    strategy:
      matrix:
        node-version: [18.x, 20.x]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v4
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Install Playwright browsers
      run: npx playwright install --with-deps
      
    - name: Create verification config
      run: |
        npx tsx src/verification/cli.ts init --force
        
    - name: Run verification pipeline
      id: verification
      run: |
        # Set up environment
        export CI=true
        export NODE_ENV=production
        
        # Prepare skip stages argument
        SKIP_STAGES=""
        if [ -n "${{ github.event.inputs.skip_stages }}" ]; then
          SKIP_STAGES="--skip-stages ${{ github.event.inputs.skip_stages }}"
        fi
        
        # Prepare verbose argument
        VERBOSE=""
        if [ "${{ github.event.inputs.verbose }}" = "true" ]; then
          VERBOSE="--verbose"
        fi
        
        # Run verification
        npx tsx src/verification/cli.ts run \
          --ci \
          --output json \
          --output-file verification-report.json \
          --timeout 1200 \
          $SKIP_STAGES \
          $VERBOSE
          
    - name: Generate HTML report
      if: always()
      run: |
        if [ -f verification-report.json ]; then
          npx tsx src/verification/cli.ts report \
            --input verification-report.json \
            --output html \
            --output-file verification-report.html
        fi
        
    - name: Upload verification reports
      if: always()
      uses: actions/upload-artifact@v4
      with:
        name: verification-reports-node-${{ matrix.node-version }}
        path: |
          verification-report.json
          verification-report.html
        retention-days: 30
        
    - name: Comment PR with results
      if: github.event_name == 'pull_request' && always()
      uses: actions/github-script@v7
      with:
        script: |
          const fs = require('fs');
          
          if (!fs.existsSync('verification-report.json')) {
            return;
          }
          
          const report = JSON.parse(fs.readFileSync('verification-report.json', 'utf8'));
          
          const statusIcon = report.overallStatus === 'passed' ? '✅' : 
                            report.overallStatus === 'warning' ? '⚠️' : '❌';
          
          const deploymentIcon = report.deploymentReady ? '🚀' : '🚫';
          
          const comment = `## ${statusIcon} Verification Report (Node.js ${{ matrix.node-version }})
          
          **Overall Status:** ${report.overallStatus.toUpperCase()}
          **Deployment Ready:** ${deploymentIcon} ${report.deploymentReady ? 'YES' : 'NO'}
          **Timestamp:** ${report.timestamp}
          
          ### Results Summary
          
          | Stage | Status | Details |
          |-------|--------|---------|
          | 🔨 Build | ${report.buildVerification.success ? '✅' : '❌'} | ${report.buildVerification.buildTime}ms, ${(report.buildVerification.outputSize.total / 1024 / 1024).toFixed(2)} MB |
          | 🧪 Tests | ${report.testResults.every(t => t.passed) ? '✅' : '❌'} | ${report.testResults.filter(t => t.passed).length}/${report.testResults.length} suites passed |
          | ⚡ Performance | ${report.performanceMetrics.lighthouse.performance >= 90 ? '✅' : '❌'} | LCP: ${report.performanceMetrics.lcp}ms, Lighthouse: ${report.performanceMetrics.lighthouse.performance} |
          | ♿ Accessibility | ${report.accessibilityResults.compliant ? '✅' : '❌'} | ${report.accessibilityResults.violations.length} violations |
          | 📱 PWA | ${report.pwaValidation.serviceWorkerRegistered && report.pwaValidation.manifestValid ? '✅' : '❌'} | SW: ${report.pwaValidation.serviceWorkerRegistered ? '✅' : '❌'}, Manifest: ${report.pwaValidation.manifestValid ? '✅' : '❌'} |
          | 🌐 Dependencies | ${report.dependencyStatus.googleMaps.available ? '✅' : '❌'} | Google Maps: ${report.dependencyStatus.googleMaps.responseTime}ms |
          
          ### Recommendations
          ${report.recommendations.map(rec => `- ${rec}`).join('\n')}
          
          [View detailed HTML report in artifacts](https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }})
          `;
          
          github.rest.issues.createComment({
            issue_number: context.issue.number,
            owner: context.repo.owner,
            repo: context.repo.repo,
            body: comment
          });
          
    - name: Set deployment status
      if: always()
      run: |
        if [ -f verification-report.json ]; then
          DEPLOYMENT_READY=$(node -e "
            const report = JSON.parse(require('fs').readFileSync('verification-report.json', 'utf8'));
            console.log(report.deploymentReady);
          ")
          
          echo "DEPLOYMENT_READY=$DEPLOYMENT_READY" >> $GITHUB_ENV
          
          if [ "$DEPLOYMENT_READY" = "true" ]; then
            echo "✅ Deployment verification passed - ready for production"
          else
            echo "❌ Deployment verification failed - not ready for production"
            exit 1
          fi
        else
          echo "❌ Verification report not found"
          exit 1
        fi

  deployment-gate:
    name: Deployment Gate
    runs-on: ubuntu-latest
    needs: verification
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    
    steps:
    - name: Download verification reports
      uses: actions/download-artifact@v4
      with:
        pattern: verification-reports-*
        merge-multiple: true
        
    - name: Check deployment readiness
      run: |
        echo "🔍 Checking deployment readiness across all Node.js versions..."
        
        ALL_READY=true
        
        for report in verification-report*.json; do
          if [ -f "$report" ]; then
            READY=$(node -e "
              const report = JSON.parse(require('fs').readFileSync('$report', 'utf8'));
              console.log(report.deploymentReady);
            ")
            
            echo "Report $report: Deployment ready = $READY"
            
            if [ "$READY" != "true" ]; then
              ALL_READY=false
            fi
          fi
        done
        
        if [ "$ALL_READY" = "true" ]; then
          echo "✅ All verification checks passed - deployment approved"
          echo "DEPLOYMENT_APPROVED=true" >> $GITHUB_ENV
        else
          echo "❌ Some verification checks failed - deployment blocked"
          echo "DEPLOYMENT_APPROVED=false" >> $GITHUB_ENV
          exit 1
        fi
        
    - name: Create deployment
      if: ${{ env.DEPLOYMENT_APPROVED == 'true' }}
      uses: actions/github-script@v7
      with:
        script: |
          const deployment = await github.rest.repos.createDeployment({
            owner: context.repo.owner,
            repo: context.repo.repo,
            ref: context.sha,
            environment: 'production',
            description: 'Automated deployment after verification',
            auto_merge: false,
            required_contexts: []
          });
          
          console.log('Deployment created:', deployment.data.id);
          
          // Update deployment status to success
          await github.rest.repos.createDeploymentStatus({
            owner: context.repo.owner,
            repo: context.repo.repo,
            deployment_id: deployment.data.id,
            state: 'success',
            description: 'Verification passed - ready for deployment'
          });