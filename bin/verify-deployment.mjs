#!/usr/bin/env node

/**
 * Executable CLI script for production deployment verification
 */

import path from 'path';
import { spawn } from 'child_process';
import { fileURLToPath } from 'url';

// Get current directory in ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Get the path to the TypeScript CLI file
const cliPath = path.join(__dirname, '..', 'src', 'verification', 'cli.ts');

// Use tsx to run the TypeScript file directly
const child = spawn('npx', ['tsx', cliPath, ...process.argv.slice(2)], {
  stdio: 'inherit',
  shell: true
});

child.on('exit', (code) => {
  process.exit(code || 0);
});

child.on('error', (error) => {
  console.error('Failed to start verification CLI:', error.message);
  process.exit(1);
});