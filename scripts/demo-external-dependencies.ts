#!/usr/bin/env tsx

/**
 * Demo script for External Dependencies Checker
 * 
 * This script demonstrates how to use the external dependency checker
 * to verify that all external services and resources are available.
 */

import { createExternalDependencyChecker } from '../src/verification/external-dependencies';
import { ConfigManager } from '../src/verification/config';
import { ExternalDependency } from '../src/verification/types';

async function runExternalDependencyDemo() {
  console.log('🔍 External Dependencies Checker Demo');
  console.log('=====================================\n');

  // Create configuration manager and dependency checker
  const configManager = new ConfigManager();
  const dependencyChecker = createExternalDependencyChecker({
    timeout: 10000,
    retries: 2,
    retryDelay: 1000,
  }, process.env.GOOGLE_MAPS_API_KEY);

  // Get dependencies from configuration
  const config = configManager.getConfig();
  const dependencies = config.externalDependencies;

  console.log('📋 Configured Dependencies:');
  dependencies.forEach((dep, index) => {
    console.log(`  ${index + 1}. ${dep.name} (${dep.critical ? 'Critical' : 'Optional'})`);
    console.log(`     URL: ${dep.url}`);
    console.log(`     Timeout: ${dep.timeout}ms\n`);
  });

  // Add some additional test dependencies
  const testDependencies: ExternalDependency[] = [
    ...dependencies,
    {
      name: 'GitHub API',
      url: 'https://api.github.com',
      timeout: 5000,
      critical: false,
    },
    {
      name: 'JSONPlaceholder API',
      url: 'https://jsonplaceholder.typicode.com/posts/1',
      timeout: 5000,
      critical: false,
    },
  ];

  console.log('🚀 Starting dependency checks...\n');

  try {
    // Check all dependencies
    const startTime = Date.now();
    const result = await dependencyChecker.checkAllDependencies(testDependencies);
    const endTime = Date.now();

    console.log(`✅ Dependency check completed in ${endTime - startTime}ms\n`);

    // Display Google Maps API result
    console.log('🗺️  Google Maps API:');
    console.log(`   Status: ${result.googleMaps.available ? '✅ Available' : '❌ Unavailable'}`);
    console.log(`   Response Time: ${result.googleMaps.responseTime}ms`);
    if (result.googleMaps.error) {
      console.log(`   Error: ${result.googleMaps.error}`);
    }
    console.log();

    // Display CDN resources
    if (result.cdnResources.length > 0) {
      console.log('📦 CDN Resources:');
      result.cdnResources.forEach((resource, index) => {
        console.log(`   ${index + 1}. ${resource.available ? '✅' : '❌'} ${resource.service}`);
        console.log(`      Response Time: ${resource.responseTime}ms`);
        if (resource.error) {
          console.log(`      Error: ${resource.error}`);
        }
      });
      console.log();
    }

    // Display API endpoints
    if (result.apiEndpoints.length > 0) {
      console.log('🔌 API Endpoints:');
      result.apiEndpoints.forEach((endpoint, index) => {
        console.log(`   ${index + 1}. ${endpoint.available ? '✅' : '❌'} ${endpoint.service}`);
        console.log(`      Response Time: ${endpoint.responseTime}ms`);
        if (endpoint.error) {
          console.log(`      Error: ${endpoint.error}`);
        }
      });
      console.log();
    }

    // Get and display summary
    const summary = dependencyChecker.getDependencySummary(result);
    console.log('📊 Summary:');
    console.log(`   Total Dependencies: ${summary.total}`);
    console.log(`   Available: ${summary.available} (${Math.round((summary.available / summary.total) * 100)}%)`);
    console.log(`   Unavailable: ${summary.unavailable}`);
    
    if (summary.criticalFailures.length > 0) {
      console.log('\n❌ Critical Failures:');
      summary.criticalFailures.forEach((failure, index) => {
        console.log(`   ${index + 1}. ${failure}`);
      });
    }

    // Test critical dependencies only
    console.log('\n🎯 Testing Critical Dependencies Only...');
    const criticalResult = await dependencyChecker.checkCriticalDependencies(testDependencies);
    const criticalSummary = dependencyChecker.getDependencySummary(criticalResult);
    
    console.log(`   Critical Dependencies: ${criticalSummary.total}`);
    console.log(`   Available: ${criticalSummary.available}`);
    console.log(`   Unavailable: ${criticalSummary.unavailable}`);

    // Validate configuration
    console.log('\n🔧 Configuration Validation:');
    const validation = dependencyChecker.validateDependencyConfig(testDependencies);
    if (validation.valid) {
      console.log('   ✅ Configuration is valid');
    } else {
      console.log('   ❌ Configuration has errors:');
      validation.errors.forEach((error, index) => {
        console.log(`      ${index + 1}. ${error}`);
      });
    }

    // Deployment readiness assessment
    console.log('\n🚀 Deployment Readiness:');
    const deploymentReady = summary.unavailable === 0 || summary.criticalFailures.length === 0;
    console.log(`   Status: ${deploymentReady ? '✅ Ready for deployment' : '❌ Not ready for deployment'}`);
    
    if (!deploymentReady) {
      console.log('   Reasons:');
      if (summary.criticalFailures.length > 0) {
        console.log('   - Critical dependencies are failing');
      }
      console.log('   - Please resolve dependency issues before deploying');
    }

  } catch (error) {
    console.error('❌ Error during dependency check:', error);
    process.exit(1);
  }

  console.log('\n🎉 Demo completed successfully!');
}

// Run the demo
if (import.meta.url === `file://${process.argv[1]}`) {
  runExternalDependencyDemo().catch((error) => {
    console.error('Demo failed:', error);
    process.exit(1);
  });
}

export { runExternalDependencyDemo };