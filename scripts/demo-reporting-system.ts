#!/usr/bin/env tsx

/**
 * Demo script for the comprehensive reporting system
 * 
 * This script demonstrates the reporting system capabilities by:
 * 1. Creating sample verification reports
 * 2. Generating both JSON and HTML reports
 * 3. Showcasing deployment decision logic
 * 4. Demonstrating different report scenarios
 */

import { promises as fs } from 'fs';
import path from 'path';
import { 
  generateVerificationReport,
  createReportGenerator,
  ReportGenerator 
} from '../src/verification/reporting';
import { VerificationReport } from '../src/verification/types';

const DEMO_OUTPUT_DIR = './demo-reports';

/**
 * Create a sample passing verification report
 */
function createPassingReport(): VerificationReport {
  return {
    timestamp: new Date(),
    overallStatus: 'passed',
    buildVerification: {
      success: true,
      buildTime: 8500,
      errors: [],
      warnings: [],
      outputSize: {
        total: 1572864, // 1.5MB
        chunks: [
          { name: 'main', size: 786432, modules: ['src/main.tsx', 'src/App.tsx'] },
          { name: 'vendor', size: 524288, modules: ['node_modules/react'] },
          { name: 'utils', size: 262144, modules: ['src/utils/index.ts'] }
        ]
      }
    },
    testResults: [
      {
        passed: true,
        duration: 5200,
        testCount: 35,
        failures: [],
        coverage: {
          lines: 92,
          functions: 95,
          branches: 88,
          statements: 93
        }
      },
      {
        passed: true,
        duration: 8500,
        testCount: 28,
        failures: [],
        coverage: {
          lines: 87,
          functions: 90,
          branches: 85,
          statements: 88
        }
      }
    ],
    performanceMetrics: {
      lcp: 1800, // Good
      fid: 45,   // Good
      cls: 0.05, // Good
      fcp: 1200,
      lighthouse: {
        performance: 96,
        accessibility: 98,
        bestPractices: 92,
        seo: 95
      }
    },
    accessibilityResults: {
      compliant: true,
      violations: [],
      warnings: [],
      testedPages: ['/', '/team-sales', '/harbor-city', '/products']
    },
    pwaValidation: {
      serviceWorkerRegistered: true,
      manifestValid: true,
      offlineFunctionality: true,
      installable: true,
      cacheStrategy: {
        staticAssetsCache: true,
        apiResponseCache: true,
        offlinePages: ['/', '/offline', '/team-sales']
      }
    },
    dependencyStatus: {
      googleMaps: {
        service: 'Google Maps API',
        available: true,
        responseTime: 120
      },
      cdnResources: [
        {
          service: 'Google Fonts',
          available: true,
          responseTime: 85
        },
        {
          service: 'Font Awesome CDN',
          available: true,
          responseTime: 95
        }
      ],
      apiEndpoints: [
        {
          service: 'Weather API',
          available: true,
          responseTime: 180
        }
      ]
    },
    recommendations: [
      'All verification checks passed - ready for deployment',
      'Consider implementing additional performance monitoring',
      'Maintain current accessibility standards'
    ],
    deploymentReady: true
  };
}

/**
 * Create a sample failing verification report
 */
function createFailingReport(): VerificationReport {
  return {
    timestamp: new Date(),
    overallStatus: 'failed',
    buildVerification: {
      success: false,
      buildTime: 0,
      errors: [
        {
          file: 'src/components/ProductCard.tsx',
          line: 45,
          column: 12,
          message: 'Property "price" does not exist on type "Product"',
          type: 'typescript'
        },
        {
          file: 'src/utils/api.ts',
          line: 23,
          column: 8,
          message: 'Cannot find module "./config"',
          type: 'bundling'
        }
      ],
      warnings: [
        {
          file: 'src/hooks/useSearch.ts',
          line: 15,
          column: 5,
          message: 'Unused variable "searchHistory"',
          type: 'typescript'
        }
      ],
      outputSize: {
        total: 0,
        chunks: []
      }
    },
    testResults: [
      {
        passed: false,
        duration: 2500,
        testCount: 35,
        failures: [
          {
            testName: 'ProductCard should display price correctly',
            error: 'TypeError: Cannot read property "price" of undefined',
            stack: 'TypeError: Cannot read property "price" of undefined\n    at ProductCard.test.tsx:25:15',
            duration: 150
          },
          {
            testName: 'API integration should handle errors',
            error: 'Module not found: ./config',
            stack: 'Module not found: ./config\n    at api.test.ts:10:1',
            duration: 100
          }
        ]
      }
    ],
    performanceMetrics: {
      lcp: 0,
      fid: 0,
      cls: 0,
      fcp: 0,
      lighthouse: {
        performance: 0,
        accessibility: 0,
        bestPractices: 0,
        seo: 0
      }
    },
    accessibilityResults: {
      compliant: false,
      violations: [],
      warnings: [],
      testedPages: []
    },
    pwaValidation: {
      serviceWorkerRegistered: false,
      manifestValid: false,
      offlineFunctionality: false,
      installable: false,
      cacheStrategy: {
        staticAssetsCache: false,
        apiResponseCache: false,
        offlinePages: []
      }
    },
    dependencyStatus: {
      googleMaps: {
        service: 'Google Maps API',
        available: false,
        responseTime: 0,
        error: 'Network timeout'
      },
      cdnResources: [],
      apiEndpoints: []
    },
    recommendations: [
      'Fix critical build errors before deployment',
      'Resolve TypeScript compilation issues',
      'Address module resolution problems',
      'Cannot proceed with deployment until build succeeds'
    ],
    deploymentReady: false
  };
}

/**
 * Create a sample warning verification report
 */
function createWarningReport(): VerificationReport {
  return {
    timestamp: new Date(),
    overallStatus: 'warning',
    buildVerification: {
      success: true,
      buildTime: 15200,
      errors: [],
      warnings: [
        {
          file: 'src/components/Header.tsx',
          line: 32,
          column: 8,
          message: 'React Hook useEffect has a missing dependency',
          type: 'typescript'
        },
        {
          file: 'src/pages/TeamSales.tsx',
          line: 67,
          column: 15,
          message: 'img element should have alt attribute',
          type: 'typescript'
        }
      ],
      outputSize: {
        total: 4194304, // 4MB - larger than ideal
        chunks: [
          { name: 'main', size: 2097152, modules: ['src/main.tsx', 'src/App.tsx'] },
          { name: 'vendor', size: 1572864, modules: ['node_modules/react', 'node_modules/react-dom'] },
          { name: 'utils', size: 524288, modules: ['src/utils/index.ts'] }
        ]
      }
    },
    testResults: [
      {
        passed: true,
        duration: 6800,
        testCount: 42,
        failures: [],
        coverage: {
          lines: 78, // Below ideal
          functions: 82,
          branches: 75,
          statements: 79
        }
      },
      {
        passed: false,
        duration: 4200,
        testCount: 18,
        failures: [
          {
            testName: 'Mobile navigation should close on outside click',
            error: 'Expected element to be hidden but it was visible',
            stack: 'Error: Expected element to be hidden but it was visible\n    at navigation.test.tsx:89:12',
            duration: 200
          }
        ]
      }
    ],
    performanceMetrics: {
      lcp: 2800, // Above threshold
      fid: 120,  // Above threshold
      cls: 0.12, // Above threshold
      fcp: 2200,
      lighthouse: {
        performance: 75, // Below threshold
        accessibility: 88,
        bestPractices: 85,
        seo: 92
      }
    },
    accessibilityResults: {
      compliant: false,
      violations: [
        {
          rule: 'color-contrast',
          impact: 'moderate',
          element: 'button.secondary',
          description: 'Element has insufficient color contrast of 3.8:1'
        },
        {
          rule: 'alt-text',
          impact: 'minor',
          element: 'img.decoration',
          description: 'Decorative image should have empty alt attribute'
        }
      ],
      warnings: [
        {
          rule: 'aria-labels',
          element: 'button.icon-only',
          description: 'Icon-only button should have aria-label'
        }
      ],
      testedPages: ['/', '/team-sales', '/harbor-city']
    },
    pwaValidation: {
      serviceWorkerRegistered: true,
      manifestValid: true,
      offlineFunctionality: false, // Issue
      installable: true,
      cacheStrategy: {
        staticAssetsCache: true,
        apiResponseCache: false, // Issue
        offlinePages: ['/']
      }
    },
    dependencyStatus: {
      googleMaps: {
        service: 'Google Maps API',
        available: true,
        responseTime: 250 // Slow
      },
      cdnResources: [
        {
          service: 'Google Fonts',
          available: true,
          responseTime: 150
        },
        {
          service: 'Font Awesome CDN',
          available: false,
          responseTime: 0,
          error: 'Service temporarily unavailable'
        }
      ],
      apiEndpoints: [
        {
          service: 'Weather API',
          available: true,
          responseTime: 350 // Slow
        }
      ]
    },
    recommendations: [
      'Address performance issues to improve user experience',
      'Fix failing test case for mobile navigation',
      'Optimize bundle size to reduce load times',
      'Implement proper offline functionality for PWA',
      'Consider deployment with caution due to performance concerns'
    ],
    deploymentReady: false // Due to performance and test issues
  };
}

/**
 * Generate demo reports
 */
async function generateDemoReports() {
  console.log('🚀 Starting Reporting System Demo\n');

  // Ensure demo output directory exists
  await fs.mkdir(DEMO_OUTPUT_DIR, { recursive: true });

  // Generate passing report
  console.log('📊 Generating PASSING verification report...');
  const passingReport = createPassingReport();
  const passingResult = await generateVerificationReport(
    passingReport, 
    path.join(DEMO_OUTPUT_DIR, 'passing')
  );
  
  console.log(`✅ Passing report generated:`);
  console.log(`   JSON: ${passingResult.jsonPath}`);
  console.log(`   HTML: ${passingResult.htmlPath}`);
  console.log(`   Deployment Ready: ${passingResult.summary.deploymentReady}`);
  console.log(`   Overall Status: ${passingResult.summary.overallStatus}`);
  console.log(`   Passed Checks: ${passingResult.summary.passedChecks}/${passingResult.summary.totalChecks}\n`);

  // Generate failing report
  console.log('📊 Generating FAILING verification report...');
  const failingReport = createFailingReport();
  const failingResult = await generateVerificationReport(
    failingReport,
    path.join(DEMO_OUTPUT_DIR, 'failing')
  );
  
  console.log(`❌ Failing report generated:`);
  console.log(`   JSON: ${failingResult.jsonPath}`);
  console.log(`   HTML: ${failingResult.htmlPath}`);
  console.log(`   Deployment Ready: ${failingResult.summary.deploymentReady}`);
  console.log(`   Overall Status: ${failingResult.summary.overallStatus}`);
  console.log(`   Critical Issues: ${failingResult.summary.criticalIssues.length}`);
  console.log(`   Failed Checks: ${failingResult.summary.failedChecks}/${failingResult.summary.totalChecks}\n`);

  // Generate warning report
  console.log('📊 Generating WARNING verification report...');
  const warningReport = createWarningReport();
  const warningResult = await generateVerificationReport(
    warningReport,
    path.join(DEMO_OUTPUT_DIR, 'warning')
  );
  
  console.log(`⚠️  Warning report generated:`);
  console.log(`   JSON: ${warningResult.jsonPath}`);
  console.log(`   HTML: ${warningResult.htmlPath}`);
  console.log(`   Deployment Ready: ${warningResult.summary.deploymentReady}`);
  console.log(`   Overall Status: ${warningResult.summary.overallStatus}`);
  console.log(`   Warning Checks: ${warningResult.summary.warningChecks}/${warningResult.summary.totalChecks}\n`);

  // Demonstrate custom theme
  console.log('🎨 Generating report with CUSTOM THEME...');
  const customThemeGenerator = createReportGenerator({
    outputDir: path.join(DEMO_OUTPUT_DIR, 'custom-theme'),
    includeTimestamp: true,
    customTheme: {
      primaryColor: '#8b5cf6',
      successColor: '#10b981',
      warningColor: '#f59e0b',
      errorColor: '#ef4444',
      backgroundColor: '#f9fafb',
      textColor: '#111827'
    }
  });

  const customThemeResult = await customThemeGenerator.generateReport(passingReport);
  console.log(`🎨 Custom theme report generated:`);
  console.log(`   HTML: ${customThemeResult.htmlPath}\n`);

  // Demonstrate deployment decision logic
  console.log('🤖 Demonstrating Deployment Decision Logic:\n');
  
  const reports = [
    { name: 'Passing Report', report: passingReport },
    { name: 'Failing Report', report: failingReport },
    { name: 'Warning Report', report: warningReport }
  ];

  const generator = new ReportGenerator({ outputDir: DEMO_OUTPUT_DIR });
  
  for (const { name, report } of reports) {
    const decision = generator.generateDeploymentDecision(report);
    console.log(`${name}:`);
    console.log(`   Ready for Deployment: ${decision.ready ? '✅' : '❌'}`);
    console.log(`   Confidence Level: ${decision.confidence}%`);
    console.log(`   Blockers: ${decision.blockers.length}`);
    console.log(`   Warnings: ${decision.warnings.length}`);
    console.log(`   Recommendations: ${decision.recommendations.length}`);
    
    if (decision.blockers.length > 0) {
      console.log(`   Critical Issues:`);
      decision.blockers.forEach(blocker => console.log(`     - ${blocker}`));
    }
    console.log('');
  }

  // Generate summary statistics
  console.log('📈 Demo Summary Statistics:\n');
  
  const allResults = [passingResult, failingResult, warningResult];
  const totalReports = allResults.length;
  const readyForDeployment = allResults.filter(r => r.summary.deploymentReady).length;
  const totalChecks = allResults.reduce((sum, r) => sum + r.summary.totalChecks, 0);
  const totalPassed = allResults.reduce((sum, r) => sum + r.summary.passedChecks, 0);
  const totalFailed = allResults.reduce((sum, r) => sum + r.summary.failedChecks, 0);
  const totalWarnings = allResults.reduce((sum, r) => sum + r.summary.warningChecks, 0);

  console.log(`Total Reports Generated: ${totalReports}`);
  console.log(`Reports Ready for Deployment: ${readyForDeployment}/${totalReports}`);
  console.log(`Total Verification Checks: ${totalChecks}`);
  console.log(`Overall Pass Rate: ${Math.round((totalPassed / totalChecks) * 100)}%`);
  console.log(`Failed Checks: ${totalFailed}`);
  console.log(`Warning Checks: ${totalWarnings}\n`);

  console.log('🎉 Demo completed successfully!');
  console.log(`📁 All reports saved to: ${DEMO_OUTPUT_DIR}`);
  console.log('💡 Open the HTML files in your browser to see the interactive reports');
}

/**
 * Demonstrate report analysis
 */
async function demonstrateReportAnalysis() {
  console.log('\n🔍 Demonstrating Report Analysis Features:\n');

  const generator = new ReportGenerator({ outputDir: DEMO_OUTPUT_DIR });
  const warningReport = createWarningReport();

  // Show detailed section analysis
  const sections = generator['generateDetailedSections'](warningReport);
  
  console.log('📋 Detailed Section Analysis:');
  sections.forEach(section => {
    console.log(`\n${section.name}:`);
    console.log(`   Status: ${section.status}`);
    console.log(`   Summary: ${section.summary}`);
    console.log(`   Errors: ${section.errors.length}`);
    console.log(`   Warnings: ${section.warnings.length}`);
    console.log(`   Recommendations: ${section.recommendations.length}`);
    
    if (section.errors.length > 0) {
      console.log(`   Error Details:`);
      section.errors.slice(0, 2).forEach(error => {
        console.log(`     - ${error}`);
      });
    }
  });

  // Show summary generation
  const summary = generator['generateSummary'](warningReport);
  console.log(`\n📊 Summary Statistics:`);
  console.log(`   Overall Status: ${summary.overallStatus}`);
  console.log(`   Deployment Ready: ${summary.deploymentReady}`);
  console.log(`   Total Checks: ${summary.totalChecks}`);
  console.log(`   Passed: ${summary.passedChecks} (${Math.round((summary.passedChecks / summary.totalChecks) * 100)}%)`);
  console.log(`   Failed: ${summary.failedChecks} (${Math.round((summary.failedChecks / summary.totalChecks) * 100)}%)`);
  console.log(`   Warnings: ${summary.warningChecks} (${Math.round((summary.warningChecks / summary.totalChecks) * 100)}%)`);
  console.log(`   Critical Issues: ${summary.criticalIssues.length}`);
}

/**
 * Main demo function
 */
async function main() {
  try {
    await generateDemoReports();
    await demonstrateReportAnalysis();
  } catch (error) {
    console.error('❌ Demo failed:', error);
    process.exit(1);
  }
}

// Run the demo
main();