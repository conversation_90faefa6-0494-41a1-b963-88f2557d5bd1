#!/usr/bin/env tsx

/**
 * Basic test script for the reporting system
 * Tests core functionality without Jest dependencies
 */

import { promises as fs } from 'fs';
import path from 'path';
import { tmpdir } from 'os';
import { 
  ReportGenerator, 
  generateVerificationReport,
  createReportGenerator 
} from '../src/verification/reporting';
import { VerificationReport } from '../src/verification/types';

/**
 * Create a simple test report
 */
function createTestReport(): VerificationReport {
  return {
    timestamp: new Date('2024-01-15T10:30:00Z'),
    overallStatus: 'warning',
    buildVerification: {
      success: true,
      buildTime: 12000,
      errors: [],
      warnings: [
        {
          file: 'src/test.ts',
          line: 10,
          column: 5,
          message: 'Unused variable',
          type: 'typescript'
        }
      ],
      outputSize: {
        total: 2048000,
        chunks: [
          { name: 'main', size: 1024000, modules: ['src/main.tsx'] },
          { name: 'vendor', size: 1024000, modules: ['node_modules/react'] }
        ]
      }
    },
    testResults: [
      {
        passed: true,
        duration: 5000,
        testCount: 20,
        failures: []
      },
      {
        passed: false,
        duration: 3000,
        testCount: 10,
        failures: [
          {
            testName: 'test failure',
            error: 'Test failed',
            duration: 100
          }
        ]
      }
    ],
    performanceMetrics: {
      lcp: 2200,
      fid: 80,
      cls: 0.08,
      fcp: 1800,
      lighthouse: {
        performance: 92,
        accessibility: 95,
        bestPractices: 88,
        seo: 90
      }
    },
    accessibilityResults: {
      compliant: false,
      violations: [
        {
          rule: 'color-contrast',
          impact: 'serious',
          element: 'button',
          description: 'Insufficient color contrast'
        }
      ],
      warnings: [],
      testedPages: ['/', '/test']
    },
    pwaValidation: {
      serviceWorkerRegistered: true,
      manifestValid: true,
      offlineFunctionality: true,
      installable: true,
      cacheStrategy: {
        staticAssetsCache: true,
        apiResponseCache: true,
        offlinePages: ['/']
      }
    },
    dependencyStatus: {
      googleMaps: {
        service: 'Google Maps API',
        available: true,
        responseTime: 150
      },
      cdnResources: [],
      apiEndpoints: []
    },
    recommendations: ['Test recommendation'],
    deploymentReady: false
  };
}

/**
 * Test basic report generation
 */
async function testBasicReportGeneration() {
  console.log('🧪 Testing basic report generation...');
  
  const tempDir = await fs.mkdtemp(path.join(tmpdir(), 'test-reports-'));
  const testReport = createTestReport();
  
  try {
    const result = await generateVerificationReport(testReport, tempDir);
    
    // Verify files exist
    await fs.access(result.jsonPath);
    await fs.access(result.htmlPath);
    
    // Verify JSON content
    const jsonContent = await fs.readFile(result.jsonPath, 'utf-8');
    const jsonData = JSON.parse(jsonContent);
    
    console.log('✅ JSON report structure verified');
    console.log(`   - Metadata: ${jsonData.metadata ? '✓' : '✗'}`);
    console.log(`   - Summary: ${jsonData.summary ? '✓' : '✗'}`);
    console.log(`   - Deployment Decision: ${jsonData.deploymentDecision ? '✓' : '✗'}`);
    console.log(`   - Original Report: ${jsonData.originalReport ? '✓' : '✗'}`);
    
    // Verify HTML content
    const htmlContent = await fs.readFile(result.htmlPath, 'utf-8');
    
    console.log('✅ HTML report structure verified');
    console.log(`   - DOCTYPE: ${htmlContent.includes('<!DOCTYPE html>') ? '✓' : '✗'}`);
    console.log(`   - Title: ${htmlContent.includes('Production Deployment Verification Report') ? '✓' : '✗'}`);
    console.log(`   - CSS: ${htmlContent.includes('<style>') ? '✓' : '✗'}`);
    console.log(`   - JavaScript: ${htmlContent.includes('<script>') ? '✓' : '✗'}`);
    
    // Verify summary
    console.log('✅ Summary data verified');
    console.log(`   - Overall Status: ${result.summary.overallStatus}`);
    console.log(`   - Deployment Ready: ${result.summary.deploymentReady}`);
    console.log(`   - Total Checks: ${result.summary.totalChecks}`);
    console.log(`   - Passed Checks: ${result.summary.passedChecks}`);
    console.log(`   - Failed Checks: ${result.summary.failedChecks}`);
    console.log(`   - Warning Checks: ${result.summary.warningChecks}`);
    
    return true;
  } catch (error) {
    console.error('❌ Basic report generation test failed:', error);
    return false;
  } finally {
    // Cleanup
    await fs.rm(tempDir, { recursive: true, force: true });
  }
}

/**
 * Test deployment decision logic
 */
async function testDeploymentDecision() {
  console.log('\n🧪 Testing deployment decision logic...');
  
  const generator = new ReportGenerator({ outputDir: './temp' });
  const testReport = createTestReport();
  
  try {
    const decision = generator.generateDeploymentDecision(testReport);
    
    console.log('✅ Deployment decision logic verified');
    console.log(`   - Ready: ${decision.ready}`);
    console.log(`   - Confidence: ${decision.confidence}%`);
    console.log(`   - Blockers: ${decision.blockers.length}`);
    console.log(`   - Warnings: ${decision.warnings.length}`);
    console.log(`   - Recommendations: ${decision.recommendations.length}`);
    
    // Test with passing report
    const passingReport = {
      ...testReport,
      overallStatus: 'passed' as const,
      buildVerification: {
        ...testReport.buildVerification,
        warnings: []
      },
      testResults: [
        {
          passed: true,
          duration: 5000,
          testCount: 30,
          failures: []
        }
      ],
      accessibilityResults: {
        ...testReport.accessibilityResults,
        compliant: true,
        violations: []
      }
    };
    
    const passingDecision = generator.generateDeploymentDecision(passingReport);
    console.log('✅ Passing report decision verified');
    console.log(`   - Ready: ${passingDecision.ready}`);
    console.log(`   - Confidence: ${passingDecision.confidence}%`);
    
    return true;
  } catch (error) {
    console.error('❌ Deployment decision test failed:', error);
    return false;
  }
}

/**
 * Test custom theme functionality
 */
async function testCustomTheme() {
  console.log('\n🧪 Testing custom theme functionality...');
  
  const tempDir = await fs.mkdtemp(path.join(tmpdir(), 'theme-test-'));
  const testReport = createTestReport();
  
  try {
    const customTheme = {
      primaryColor: '#8b5cf6',
      successColor: '#10b981',
      warningColor: '#f59e0b',
      errorColor: '#ef4444',
      backgroundColor: '#f9fafb',
      textColor: '#111827'
    };
    
    const generator = createReportGenerator({
      outputDir: tempDir,
      customTheme
    });
    
    const result = await generator.generateReport(testReport);
    
    // Verify custom theme is applied
    const htmlContent = await fs.readFile(result.htmlPath, 'utf-8');
    
    console.log('✅ Custom theme verified');
    console.log(`   - Primary Color: ${htmlContent.includes(customTheme.primaryColor) ? '✓' : '✗'}`);
    console.log(`   - Success Color: ${htmlContent.includes(customTheme.successColor) ? '✓' : '✗'}`);
    console.log(`   - Warning Color: ${htmlContent.includes(customTheme.warningColor) ? '✓' : '✗'}`);
    console.log(`   - Error Color: ${htmlContent.includes(customTheme.errorColor) ? '✓' : '✗'}`);
    
    return true;
  } catch (error) {
    console.error('❌ Custom theme test failed:', error);
    return false;
  } finally {
    // Cleanup
    await fs.rm(tempDir, { recursive: true, force: true });
  }
}

/**
 * Test factory functions
 */
async function testFactoryFunctions() {
  console.log('\n🧪 Testing factory functions...');
  
  try {
    // Test createReportGenerator
    const generator = createReportGenerator({
      outputDir: './temp',
      includeTimestamp: true
    });
    
    console.log('✅ createReportGenerator verified');
    console.log(`   - Instance created: ${generator instanceof ReportGenerator ? '✓' : '✗'}`);
    
    // Test generateVerificationReport utility
    const tempDir = await fs.mkdtemp(path.join(tmpdir(), 'factory-test-'));
    const testReport = createTestReport();
    
    const result = await generateVerificationReport(testReport, tempDir);
    
    console.log('✅ generateVerificationReport utility verified');
    console.log(`   - JSON path: ${result.jsonPath ? '✓' : '✗'}`);
    console.log(`   - HTML path: ${result.htmlPath ? '✓' : '✗'}`);
    console.log(`   - Summary: ${result.summary ? '✓' : '✗'}`);
    
    // Cleanup
    await fs.rm(tempDir, { recursive: true, force: true });
    
    return true;
  } catch (error) {
    console.error('❌ Factory functions test failed:', error);
    return false;
  }
}

/**
 * Main test runner
 */
async function runTests() {
  console.log('🚀 Starting Basic Reporting System Tests\n');
  
  const tests = [
    testBasicReportGeneration,
    testDeploymentDecision,
    testCustomTheme,
    testFactoryFunctions
  ];
  
  let passed = 0;
  let failed = 0;
  
  for (const test of tests) {
    try {
      const result = await test();
      if (result) {
        passed++;
      } else {
        failed++;
      }
    } catch (error) {
      console.error(`❌ Test failed with error:`, error);
      failed++;
    }
  }
  
  console.log('\n📊 Test Results Summary:');
  console.log(`   ✅ Passed: ${passed}`);
  console.log(`   ❌ Failed: ${failed}`);
  console.log(`   📈 Success Rate: ${Math.round((passed / (passed + failed)) * 100)}%`);
  
  if (failed === 0) {
    console.log('\n🎉 All tests passed! Reporting system is working correctly.');
  } else {
    console.log('\n⚠️  Some tests failed. Please review the errors above.');
    process.exit(1);
  }
}

// Run the tests
runTests();