#!/usr/bin/env tsx

/**
 * Demo script for the verification configuration system
 * 
 * This script demonstrates various configuration features including:
 * - Environment-specific configurations
 * - Test suite filtering
 * - Performance threshold customization
 * - Configuration validation and suggestions
 */

import { ConfigManager, ConfigurationOptions } from '../src/verification/config';
import { ConfigTemplateGenerator, ConfigValidator, ConfigComparison } from '../src/verification/config-utils';
import * as fs from 'fs/promises';

async function main() {
  console.log('🔧 Verification Configuration System Demo\n');

  // 1. Basic Configuration Management
  console.log('1. Basic Configuration Management');
  console.log('================================');
  
  const basicConfig = new ConfigManager();
  const config = basicConfig.getConfig();
  
  console.log(`Default build mode: ${config.buildSettings.mode}`);
  console.log(`Default LCP threshold: ${config.performanceThresholds.lcp}ms`);
  console.log(`Default accessibility level: ${config.accessibilityLevel}`);
  console.log(`Test suites configured: ${config.testSuites.length}`);
  console.log();

  // 2. Environment-Specific Configuration
  console.log('2. Environment-Specific Configuration');
  console.log('====================================');
  
  try {
    const prodConfig = await ConfigManager.loadFromFile('verification.config.json', {
      environment: 'production'
    });
    
    const prodSettings = prodConfig.getConfig();
    console.log(`Production LCP threshold: ${prodSettings.performanceThresholds.lcp}ms`);
    console.log(`Production build mode: ${prodSettings.buildSettings.mode}`);
    console.log(`Production source maps: ${prodSettings.buildSettings.sourceMaps}`);
    
    const devConfig = await ConfigManager.loadFromFile('verification.config.json', {
      environment: 'development'
    });
    
    const devSettings = devConfig.getConfig();
    console.log(`Development LCP threshold: ${devSettings.performanceThresholds.lcp}ms`);
    console.log(`Development build mode: ${devSettings.buildSettings.mode}`);
    console.log(`Development source maps: ${devSettings.buildSettings.sourceMaps}`);
    console.log();
  } catch (error) {
    console.log('⚠️  Configuration file not found, using defaults');
    console.log();
  }

  // 3. Test Suite Filtering
  console.log('3. Test Suite Filtering');
  console.log('=======================');
  
  const filteredConfig = new ConfigManager(undefined, {
    testSuiteFilter: {
      types: ['unit', 'integration'],
      exclude: ['e2e-tests']
    }
  });
  
  const filteredSuites = filteredConfig.getEnabledTestSuites();
  console.log('Filtered test suites:');
  filteredSuites.forEach(suite => {
    console.log(`  - ${suite.name} (${suite.type})`);
  });
  console.log();

  // 4. Performance Threshold Customization
  console.log('4. Performance Threshold Customization');
  console.log('======================================');
  
  const customPerfConfig = new ConfigManager(undefined, {
    performanceOverrides: {
      lcp: 3000,
      fid: 150,
      cls: 0.15
    },
    customThresholds: {
      'performance.bundleSize': 1048576, // 1MB
      'performance.imageOptimization': 0.9
    }
  });
  
  const customPerfSettings = customPerfConfig.getConfig();
  console.log('Custom performance thresholds:');
  console.log(`  LCP: ${customPerfSettings.performanceThresholds.lcp}ms`);
  console.log(`  FID: ${customPerfSettings.performanceThresholds.fid}ms`);
  console.log(`  CLS: ${customPerfSettings.performanceThresholds.cls}`);
  
  if (customPerfSettings.customThresholds) {
    console.log('Custom thresholds:');
    Object.entries(customPerfSettings.customThresholds).forEach(([key, value]) => {
      console.log(`  ${key}: ${value}`);
    });
  }
  console.log();

  // 5. Configuration Validation
  console.log('5. Configuration Validation');
  console.log('===========================');
  
  const validation = basicConfig.validateConfig();
  console.log(`Configuration valid: ${validation.valid}`);
  
  if (!validation.valid) {
    console.log('Validation errors:');
    validation.errors.forEach(error => {
      console.log(`  - ${error}`);
    });
  }
  
  // Test with invalid configuration
  const invalidConfig = new ConfigManager({
    performanceThresholds: {
      lcp: -100, // Invalid
      fid: 100,
      cls: 2, // Invalid
      lighthousePerformance: 150 // Invalid
    }
  });
  
  const invalidValidation = invalidConfig.validateConfig();
  console.log(`Invalid configuration valid: ${invalidValidation.valid}`);
  console.log('Expected validation errors:');
  invalidValidation.errors.forEach(error => {
    console.log(`  - ${error}`);
  });
  console.log();

  // 6. Configuration Templates
  console.log('6. Configuration Templates');
  console.log('==========================');
  
  const basicTemplate = ConfigTemplateGenerator.generateBasicTemplate();
  console.log('Basic template test suites:');
  basicTemplate.testSuites.forEach(suite => {
    console.log(`  - ${suite.name}: ${suite.description}`);
  });
  
  const devTemplate = ConfigTemplateGenerator.generateEnvironmentConfig('development');
  console.log('\nDevelopment template created with environment overrides');
  console.log();

  // 7. Configuration Comparison
  console.log('7. Configuration Comparison');
  console.log('===========================');
  
  const config1 = basicConfig.getConfig();
  const config2 = customPerfConfig.getConfig();
  
  const comparison = ConfigComparison.compareConfigs(config1, config2);
  console.log(`Total differences: ${comparison.summary.totalDifferences}`);
  console.log(`Performance changes: ${comparison.summary.performanceChanges}`);
  console.log(`Test suite changes: ${comparison.summary.testSuiteChanges}`);
  
  if (comparison.differences.length > 0) {
    console.log('\nKey differences:');
    comparison.differences.slice(0, 3).forEach(diff => {
      console.log(`  ${diff.path}: ${diff.value1} → ${diff.value2}`);
    });
  }
  console.log();

  // 8. Configuration Management Operations
  console.log('8. Configuration Management Operations');
  console.log('=====================================');
  
  const managedConfig = new ConfigManager();
  
  // Update performance thresholds
  managedConfig.updatePerformanceThresholds({
    lcp: 2000,
    fid: 80
  });
  
  // Update accessibility settings
  managedConfig.updateAccessibilitySettings({
    level: 'AAA'
  });
  
  // Add custom test suite
  managedConfig.updateTestSuite('custom-performance-tests', {
    type: 'performance',
    enabled: true,
    timeout: 300000,
    retries: 1,
    description: 'Custom performance validation tests',
    priority: 'high'
  });
  
  // Toggle test suites by type
  managedConfig.toggleTestSuitesByType('accessibility', false);
  
  const summary = managedConfig.getConfigSummary();
  console.log('Configuration summary after updates:');
  console.log(`  Environment: ${summary.environment || 'default'}`);
  console.log(`  Enabled test suites: ${summary.enabledTestSuites}/${summary.totalTestSuites}`);
  console.log(`  Accessibility level: ${summary.accessibilityLevel}`);
  console.log(`  LCP threshold: ${summary.performanceThresholds.lcp}ms`);
  console.log(`  FID threshold: ${summary.performanceThresholds.fid}ms`);
  console.log();

  // 9. Configuration Validation with Suggestions
  console.log('9. Configuration Validation with Suggestions');
  console.log('============================================');
  
  try {
    // Create a configuration with some issues for demonstration
    const testConfigPath = 'temp-test-config.json';
    const testConfig = {
      default: {
        buildSettings: { mode: 'production', sourceMaps: false, minification: true },
        performanceThresholds: { lcp: 4000, fid: 400, cls: 0.3, lighthousePerformance: 60 },
        accessibilityLevel: 'AA',
        testSuites: [],
        externalDependencies: []
      }
    };
    
    await fs.writeFile(testConfigPath, JSON.stringify(testConfig, null, 2));
    
    const validationResult = await ConfigValidator.validateWithSuggestions(testConfigPath);
    
    console.log(`Configuration valid: ${validationResult.valid}`);
    
    if (validationResult.warnings.length > 0) {
      console.log('\nWarnings:');
      validationResult.warnings.forEach(warning => {
        console.log(`  ⚠️  ${warning}`);
      });
    }
    
    if (validationResult.suggestions.length > 0) {
      console.log('\nSuggestions:');
      validationResult.suggestions.forEach(suggestion => {
        console.log(`  💡 ${suggestion}`);
      });
    }
    
    // Clean up test file
    await fs.unlink(testConfigPath);
    
  } catch (error) {
    console.log('⚠️  Could not run validation with suggestions demo');
  }
  console.log();

  // 10. Performance Recommendations
  console.log('10. Performance Recommendations');
  console.log('===============================');
  
  const slowThresholds = {
    lcp: 4000,
    fid: 300,
    cls: 0.25,
    lighthousePerformance: 70
  };
  
  const recommendations = ConfigValidator.generatePerformanceRecommendations(slowThresholds);
  console.log('Performance recommendations for slow thresholds:');
  recommendations.forEach(rec => {
    console.log(`  📈 ${rec}`);
  });
  console.log();

  console.log('✅ Configuration system demo completed!');
  console.log('\nNext steps:');
  console.log('  - Run "verify-deployment init" to create your configuration');
  console.log('  - Use "verify-deployment validate-config" to validate your setup');
  console.log('  - Try different environments with "--environment <env>"');
  console.log('  - Customize thresholds based on your application needs');
}

// Run the demo
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(error => {
    console.error('❌ Demo failed:', error);
    process.exit(1);
  });
}

export { main as runConfigurationDemo };