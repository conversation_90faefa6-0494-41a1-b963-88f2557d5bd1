#!/usr/bin/env tsx

/**
 * Demo script for PWA validation
 * 
 * This script demonstrates the PWA validation functionality
 * by running validation against the local development server
 */

import { validatePWA, PWATestSuite } from '../src/verification/pwa';
import { spawn, ChildProcess } from 'child_process';

interface DemoConfig {
  baseUrl: string;
  manifestPath: string;
  serviceWorkerPath: string;
  timeout: number;
  offlinePages: string[];
  criticalResources: string[];
}

class PWAValidationDemo {
  private serverProcess?: ChildProcess;
  private config: DemoConfig;

  constructor() {
    this.config = {
      baseUrl: process.env.BASE_URL || 'http://localhost:5173',
      manifestPath: '/manifest.json',
      serviceWorkerPath: '/sw.js',
      timeout: 30000,
      offlinePages: ['/', '/teamsales', '/harbor-city'],
      criticalResources: [
        '/Icebox.webp',
        '/manifest.json',
        '/sw.js',
        '/favicon.svg'
      ]
    };
  }

  /**
   * Start development server for testing
   */
  private async startServer(): Promise<void> {
    console.log('🚀 Starting development server...');
    
    this.serverProcess = spawn('npm', ['run', 'dev'], {
      stdio: 'pipe',
      detached: false
    });

    // Wait for server to be ready
    await new Promise<void>((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('Server failed to start within 30 seconds'));
      }, 30000);

      const checkServer = async () => {
        try {
          const response = await fetch(this.config.baseUrl);
          if (response.ok) {
            clearTimeout(timeout);
            console.log('✅ Development server is ready');
            resolve();
          } else {
            setTimeout(checkServer, 1000);
          }
        } catch (error) {
          setTimeout(checkServer, 1000);
        }
      };

      setTimeout(checkServer, 2000); // Wait 2 seconds before first check
    });
  }

  /**
   * Stop development server
   */
  private async stopServer(): Promise<void> {
    if (this.serverProcess) {
      console.log('🛑 Stopping development server...');
      this.serverProcess.kill('SIGTERM');
      
      await new Promise<void>((resolve) => {
        this.serverProcess!.on('exit', () => {
          console.log('✅ Development server stopped');
          resolve();
        });
        
        setTimeout(() => {
          this.serverProcess!.kill('SIGKILL');
          resolve();
        }, 5000);
      });
    }
  }

  /**
   * Run PWA validation demo
   */
  async runDemo(): Promise<void> {
    console.log('🔍 PWA Validation Demo');
    console.log('='.repeat(50));

    try {
      // Start server if needed
      if (this.config.baseUrl.includes('localhost:5173')) {
        await this.startServer();
      }

      console.log('\n📋 Configuration:');
      console.log(`  Base URL: ${this.config.baseUrl}`);
      console.log(`  Manifest: ${this.config.manifestPath}`);
      console.log(`  Service Worker: ${this.config.serviceWorkerPath}`);
      console.log(`  Timeout: ${this.config.timeout}ms`);
      console.log(`  Offline Pages: ${this.config.offlinePages.join(', ')}`);
      console.log(`  Critical Resources: ${this.config.criticalResources.join(', ')}`);

      console.log('\n🧪 Running PWA Validation...');
      const startTime = Date.now();

      // Run standalone validation
      console.log('\n1️⃣ Running standalone PWA validation...');
      const validationResult = await validatePWA(this.config);
      
      console.log('\n📊 PWA Validation Results:');
      console.log(`  ✅ Service Worker Registered: ${validationResult.serviceWorkerRegistered ? '✅ Yes' : '❌ No'}`);
      console.log(`  ✅ Manifest Valid: ${validationResult.manifestValid ? '✅ Yes' : '❌ No'}`);
      console.log(`  ✅ Offline Functionality: ${validationResult.offlineFunctionality ? '✅ Yes' : '❌ No'}`);
      console.log(`  ✅ PWA Installable: ${validationResult.installable ? '✅ Yes' : '❌ No'}`);
      
      console.log('\n📦 Cache Strategy:');
      console.log(`  Static Assets Cache: ${validationResult.cacheStrategy.staticAssetsCache ? '✅ Yes' : '❌ No'}`);
      console.log(`  API Response Cache: ${validationResult.cacheStrategy.apiResponseCache ? '✅ Yes' : '❌ No'}`);
      console.log(`  Offline Pages: ${validationResult.cacheStrategy.offlinePages.length} pages cached`);
      
      if (validationResult.cacheStrategy.offlinePages.length > 0) {
        console.log('    Cached pages:');
        validationResult.cacheStrategy.offlinePages.forEach(page => {
          console.log(`      - ${page}`);
        });
      }

      // Run test suite
      console.log('\n2️⃣ Running PWA Test Suite...');
      const testSuite = new PWATestSuite();
      
      // Override config for demo
      (testSuite as any).config = this.config;
      (testSuite as any).validator = new (await import('../src/verification/pwa')).PWAValidator(this.config);
      
      const testResult = await testSuite.execute();
      
      console.log('\n🧪 Test Suite Results:');
      console.log(`  Tests Run: ${testResult.testCount}`);
      console.log(`  Passed: ${testResult.passed ? '✅ Yes' : '❌ No'}`);
      console.log(`  Duration: ${testResult.duration}ms`);
      console.log(`  Failures: ${testResult.failures.length}`);
      
      if (testResult.failures.length > 0) {
        console.log('\n❌ Test Failures:');
        testResult.failures.forEach((failure, index) => {
          console.log(`  ${index + 1}. ${failure.testName}`);
          console.log(`     Error: ${failure.error}`);
          if (failure.stack) {
            console.log(`     Stack: ${failure.stack.split('\n')[0]}`);
          }
        });
      }

      const totalTime = Date.now() - startTime;
      console.log(`\n⏱️  Total validation time: ${totalTime}ms`);

      // Overall assessment
      const overallPassed = validationResult.serviceWorkerRegistered &&
                           validationResult.manifestValid &&
                           validationResult.offlineFunctionality &&
                           validationResult.installable &&
                           validationResult.cacheStrategy.staticAssetsCache;

      console.log('\n🎯 Overall PWA Assessment:');
      if (overallPassed) {
        console.log('✅ Your application meets PWA standards!');
        console.log('   Ready for production deployment.');
      } else {
        console.log('⚠️  Your application has some PWA issues that should be addressed:');
        
        if (!validationResult.serviceWorkerRegistered) {
          console.log('   - Service worker is not registered or not active');
        }
        if (!validationResult.manifestValid) {
          console.log('   - PWA manifest is invalid or missing required fields');
        }
        if (!validationResult.offlineFunctionality) {
          console.log('   - Offline functionality is not working properly');
        }
        if (!validationResult.installable) {
          console.log('   - Application does not meet installability criteria');
        }
        if (!validationResult.cacheStrategy.staticAssetsCache) {
          console.log('   - Static assets are not being cached properly');
        }
      }

      console.log('\n📚 Recommendations:');
      if (!validationResult.serviceWorkerRegistered) {
        console.log('   - Ensure service worker is properly registered in your main application file');
        console.log('   - Check that /sw.js is accessible and contains valid service worker code');
      }
      if (!validationResult.manifestValid) {
        console.log('   - Review your manifest.json file for required fields (name, start_url, display, icons)');
        console.log('   - Ensure at least one icon is 192x192 or larger');
      }
      if (!validationResult.offlineFunctionality) {
        console.log('   - Implement proper caching strategies in your service worker');
        console.log('   - Test offline functionality manually to identify issues');
      }
      if (!validationResult.installable) {
        console.log('   - Ensure your application is served over HTTPS (or localhost for development)');
        console.log('   - Verify that your manifest meets all PWA criteria');
      }

    } catch (error) {
      console.error('\n❌ Demo failed:', error);
      process.exit(1);
    } finally {
      // Clean up
      if (this.config.baseUrl.includes('localhost:5173')) {
        await this.stopServer();
      }
    }
  }
}

// Run demo if called directly
if (require.main === module) {
  const demo = new PWAValidationDemo();
  demo.runDemo().catch(error => {
    console.error('Demo failed:', error);
    process.exit(1);
  });
}

export { PWAValidationDemo };