#!/usr/bin/env tsx

/**
 * Demo script for Production Environment Testing
 * 
 * This script demonstrates the production environment testing capabilities
 * including build verification, server simulation, and cross-browser testing.
 */

import { createProductionEnvironmentTester, DEFAULT_PRODUCTION_CONFIG } from '../src/verification/production-environment';

async function runProductionEnvironmentDemo() {
  console.log('🚀 Production Environment Testing Demo');
  console.log('=====================================\n');

  // Create tester with demo configuration
  const demoConfig = {
    ...DEFAULT_PRODUCTION_CONFIG,
    previewPort: 4173,
    testTimeout: 45000,
    browsers: [
      { name: 'chromium' as const, enabled: true },
      { name: 'firefox' as const, enabled: false }, // Disable for demo
      { name: 'webkit' as const, enabled: false }   // Disable for demo
    ],
    criticalPages: [
      '/',
      '/team-sales',
      '/harbor-city'
    ],
    networkConditions: [
      {
        name: 'Fast 3G',
        downloadThroughput: 1.5 * 1024 * 1024 / 8, // 1.5 Mbps
        uploadThroughput: 750 * 1024 / 8,           // 750 Kbps
        latency: 150
      },
      {
        name: 'Slow 3G',
        downloadThroughput: 500 * 1024 / 8,         // 500 Kbps
        uploadThroughput: 500 * 1024 / 8,           // 500 Kbps
        latency: 300
      }
    ]
  };

  const tester = createProductionEnvironmentTester(demoConfig);

  try {
    console.log('📋 Configuration:');
    console.log(`  Preview Port: ${demoConfig.previewPort}`);
    console.log(`  Test Timeout: ${demoConfig.testTimeout}ms`);
    console.log(`  Browsers: ${demoConfig.browsers.filter(b => b.enabled).map(b => b.name).join(', ')}`);
    console.log(`  Critical Pages: ${demoConfig.criticalPages.join(', ')}`);
    console.log(`  Network Conditions: ${demoConfig.networkConditions.map(nc => nc.name).join(', ')}`);
    console.log();

    console.log('🔄 Starting production environment testing...\n');

    const startTime = Date.now();
    const result = await tester.test();
    const duration = Date.now() - startTime;

    console.log('\n📊 Test Results Summary:');
    console.log('========================');
    console.log(`Overall Success: ${result.overallSuccess ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`Total Duration: ${duration}ms (${(duration / 1000).toFixed(2)}s)`);
    console.log(`Build Success: ${result.buildSuccess ? '✅' : '❌'}`);
    console.log(`Server Started: ${result.serverStarted ? '✅' : '❌'}`);
    console.log();

    // Page Load Test Results
    if (result.pageLoadTests.length > 0) {
      console.log('📄 Page Load Test Results:');
      console.log('---------------------------');
      result.pageLoadTests.forEach(test => {
        const status = test.success ? '✅' : '❌';
        const loadTime = `${test.loadTime}ms`;
        const metrics = test.metrics;
        
        console.log(`${status} ${test.page} (${test.browser})`);
        console.log(`   Load Time: ${loadTime}`);
        console.log(`   DOM Content Loaded: ${metrics.domContentLoaded}ms`);
        console.log(`   First Contentful Paint: ${metrics.firstContentfulPaint}ms`);
        console.log(`   Largest Contentful Paint: ${metrics.largestContentfulPaint}ms`);
        console.log(`   Resources: ${metrics.resourceCount} (${(metrics.totalSize / 1024).toFixed(2)}KB)`);
        
        if (test.errors.length > 0) {
          console.log(`   Errors: ${test.errors.length}`);
          test.errors.forEach(error => console.log(`     - ${error}`));
        }
        console.log();
      });
    }

    // Functionality Test Results
    if (result.functionalityTests.length > 0) {
      console.log('⚙️  Functionality Test Results:');
      console.log('-------------------------------');
      result.functionalityTests.forEach(test => {
        const status = test.success ? '✅' : '❌';
        console.log(`${status} ${test.testName} (${test.browser}) - ${test.duration}ms`);
        if (test.error) {
          console.log(`   Error: ${test.error}`);
        }
      });
      console.log();
    }

    // Network Condition Test Results
    if (result.networkConditionTests.length > 0) {
      console.log('🌐 Network Condition Test Results:');
      console.log('-----------------------------------');
      result.networkConditionTests.forEach(test => {
        const status = test.overallSuccess ? '✅' : '❌';
        console.log(`${status} ${test.condition} (${test.browser})`);
        
        test.pages.forEach(page => {
          const pageStatus = page.success ? '✅' : '❌';
          console.log(`   ${pageStatus} ${page.page} - ${page.loadTime}ms`);
          if (page.error) {
            console.log(`     Error: ${page.error}`);
          }
        });
        console.log();
      });
    }

    // Errors and Warnings
    if (result.errors.length > 0) {
      console.log('❌ Errors:');
      console.log('----------');
      result.errors.forEach(error => console.log(`  - ${error}`));
      console.log();
    }

    if (result.warnings.length > 0) {
      console.log('⚠️  Warnings:');
      console.log('-------------');
      result.warnings.forEach(warning => console.log(`  - ${warning}`));
      console.log();
    }

    // Generate and display detailed report
    console.log('📋 Detailed Report:');
    console.log('===================');
    const report = tester.generateReport(result);
    console.log(report);

    // Performance Analysis
    if (result.pageLoadTests.length > 0) {
      console.log('\n📈 Performance Analysis:');
      console.log('========================');
      
      const avgLoadTime = result.pageLoadTests.reduce((sum, test) => sum + test.loadTime, 0) / result.pageLoadTests.length;
      const maxLoadTime = Math.max(...result.pageLoadTests.map(test => test.loadTime));
      const minLoadTime = Math.min(...result.pageLoadTests.map(test => test.loadTime));
      
      console.log(`Average Load Time: ${avgLoadTime.toFixed(2)}ms`);
      console.log(`Max Load Time: ${maxLoadTime}ms`);
      console.log(`Min Load Time: ${minLoadTime}ms`);
      
      // Performance thresholds
      const slowPages = result.pageLoadTests.filter(test => test.loadTime > 3000);
      if (slowPages.length > 0) {
        console.log(`\n⚠️  Slow Loading Pages (>3s):`);
        slowPages.forEach(page => {
          console.log(`  - ${page.page}: ${page.loadTime}ms`);
        });
      }
      
      const fastPages = result.pageLoadTests.filter(test => test.loadTime <= 1000);
      if (fastPages.length > 0) {
        console.log(`\n✅ Fast Loading Pages (≤1s):`);
        fastPages.forEach(page => {
          console.log(`  - ${page.page}: ${page.loadTime}ms`);
        });
      }
    }

    // Recommendations
    console.log('\n💡 Recommendations:');
    console.log('===================');
    
    if (!result.overallSuccess) {
      console.log('❌ Production deployment is NOT recommended due to test failures.');
      
      if (!result.buildSuccess) {
        console.log('  - Fix build errors before deployment');
      }
      
      if (!result.serverStarted) {
        console.log('  - Resolve server startup issues');
      }
      
      const failedPageLoads = result.pageLoadTests.filter(test => !test.success);
      if (failedPageLoads.length > 0) {
        console.log(`  - Fix ${failedPageLoads.length} page load failures`);
      }
      
      const failedFunctionality = result.functionalityTests.filter(test => !test.success);
      if (failedFunctionality.length > 0) {
        console.log(`  - Fix ${failedFunctionality.length} functionality issues`);
      }
    } else {
      console.log('✅ Production deployment is RECOMMENDED.');
      console.log('  - All critical tests passed');
      console.log('  - Application loads successfully across browsers');
      console.log('  - Core functionality works as expected');
      
      if (result.warnings.length > 0) {
        console.log(`  - Consider addressing ${result.warnings.length} warnings for optimal performance`);
      }
    }

    console.log('\n🎉 Production Environment Testing Demo Complete!');
    
    // Exit with appropriate code
    process.exit(result.overallSuccess ? 0 : 1);

  } catch (error) {
    console.error('\n❌ Demo failed with error:', error);
    console.error('\nThis might be due to:');
    console.error('  - Missing dependencies (npm install)');
    console.error('  - Build configuration issues');
    console.error('  - Port conflicts');
    console.error('  - Browser installation issues');
    
    process.exit(1);
  }
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n\n🛑 Demo interrupted by user');
  process.exit(1);
});

process.on('SIGTERM', () => {
  console.log('\n\n🛑 Demo terminated');
  process.exit(1);
});

// Run the demo
if (import.meta.url === `file://${process.argv[1]}`) {
  runProductionEnvironmentDemo().catch(error => {
    console.error('Demo execution failed:', error);
    process.exit(1);
  });
}