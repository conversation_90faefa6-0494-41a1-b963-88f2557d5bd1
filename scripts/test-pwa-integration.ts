#!/usr/bin/env tsx

/**
 * Test PWA integration with test orchestrator
 */

import { TestOrchestrator } from '../src/verification/test-orchestrator';
import { ConfigManager } from '../src/verification/config';

async function testPWAIntegration() {
  console.log('🧪 Testing PWA Integration with Test Orchestrator...');
  
  try {
    // Create config manager
    const configManager = new ConfigManager();
    
    // Create test orchestrator with PWA test suite enabled
    const orchestrator = new TestOrchestrator({
      testSuites: [
        {
          name: 'pwa',
          type: 'e2e',
          enabled: true,
          timeout: 30000,
          retries: 1
        }
      ],
      parallel: false,
      timeout: 30000
    }, configManager);
    
    console.log('✅ Test Orchestrator created with PWA test suite');
    
    // Test that PWA test suite is available
    const testSuites = (orchestrator as any).testSuites;
    const pwaTestSuite = testSuites.get('pwa');
    
    if (pwaTestSuite) {
      console.log('✅ PWA test suite found in orchestrator');
      console.log(`   Name: ${pwaTestSuite.name}`);
      console.log(`   Type: ${pwaTestSuite.type}`);
    } else {
      throw new Error('PWA test suite not found in orchestrator');
    }
    
    console.log('\n🔍 PWA integration test completed successfully');
    
  } catch (error) {
    console.error('❌ PWA integration test failed:', error);
    process.exit(1);
  }
}

testPWAIntegration();