#!/usr/bin/env tsx

/**
 * Demo script for the accessibility validation system
 * Demonstrates all implemented features including axe-core integration,
 * keyboard navigation testing, color contrast validation, and screen reader compatibility
 */

import { AccessibilityValidator, AccessibilityTestSuite } from '../src/verification/accessibility.js';
import { ConfigManager } from '../src/verification/config.js';

async function demoAccessibilityValidation() {
  console.log('🎯 Accessibility Validation System Demo');
  console.log('=======================================\n');

  // Demo 1: Basic Validator Configuration
  console.log('📋 Demo 1: Validator Configuration Options');
  console.log('------------------------------------------');
  
  const basicValidator = new AccessibilityValidator({
    baseUrl: 'http://localhost:4175',
    pages: ['/', '/team-sales', '/harbor-city'],
    wcagLevel: 'AA',
    timeout: 30000,
    keyboardNavigation: true,
    colorContrast: true,
    screenReader: true,
  });

  console.log('✅ Basic validator configured:');
  console.log('   - WCAG Level: AA');
  console.log('   - Pages: /, /team-sales, /harbor-city');
  console.log('   - Keyboard navigation testing: Enabled');
  console.log('   - Color contrast validation: Enabled');
  console.log('   - Screen reader compatibility: Enabled');

  // Demo 2: WCAG Level Support
  console.log('\n📋 Demo 2: WCAG Level Support');
  console.log('------------------------------');
  
  const wcagLevels: Array<'A' | 'AA' | 'AAA'> = ['A', 'AA', 'AAA'];
  wcagLevels.forEach(level => {
    const validator = new AccessibilityValidator({ wcagLevel: level });
    const tags = (validator as any).getAxeTags();
    console.log(`   - WCAG ${level}: ${tags.join(', ')}`);
  });

  // Demo 3: Color Contrast Calculation
  console.log('\n📋 Demo 3: Color Contrast Calculation');
  console.log('-------------------------------------');
  
  const contrastTests = [
    { fg: 'rgb(0, 0, 0)', bg: 'rgb(255, 255, 255)', name: 'Black on White' },
    { fg: '#000000', bg: '#ffffff', name: 'Hex Black on White' },
    { fg: 'black', bg: 'white', name: 'Named Colors' },
    { fg: 'rgb(128, 128, 128)', bg: 'rgb(255, 255, 255)', name: 'Gray on White' },
    { fg: 'rgb(0, 0, 255)', bg: 'rgb(255, 255, 255)', name: 'Blue on White' },
  ];

  contrastTests.forEach(({ fg, bg, name }) => {
    const ratio = (basicValidator as any).calculateContrastRatio(fg, bg);
    const wcagAA = ratio >= 4.5 ? '✅' : '❌';
    const wcagAAA = ratio >= 7 ? '✅' : '❌';
    console.log(`   - ${name}: ${ratio.toFixed(2)}:1 (AA: ${wcagAA}, AAA: ${wcagAAA})`);
  });

  // Demo 4: Remediation Guidance System
  console.log('\n📋 Demo 4: Remediation Guidance System');
  console.log('--------------------------------------');
  
  const commonRules = [
    'color-contrast',
    'image-alt',
    'label',
    'button-name',
    'keyboard-navigation',
    'heading-order',
  ];

  commonRules.forEach(rule => {
    const guidance = (basicValidator as any).getRemediationGuidance(rule);
    console.log(`   - ${rule}:`);
    console.log(`     ${guidance.substring(0, 80)}...`);
  });

  // Demo 5: Report Generation
  console.log('\n📋 Demo 5: Accessibility Report Generation');
  console.log('------------------------------------------');
  
  const mockViolationResult = {
    compliant: false,
    violations: [
      {
        rule: 'color-contrast',
        impact: 'serious' as const,
        element: 'div.hero-text',
        description: 'Text has insufficient color contrast ratio of 3.2:1 (expected 4.5:1)',
      },
      {
        rule: 'image-alt',
        impact: 'critical' as const,
        element: 'img.product-image',
        description: 'Image element missing required alt attribute',
      },
      {
        rule: 'keyboard-navigation',
        impact: 'serious' as const,
        element: 'button.menu-toggle',
        description: 'Interactive element not reachable via keyboard navigation',
      },
    ],
    warnings: [
      {
        rule: 'aria-valid-attr',
        element: 'div.modal',
        description: 'ARIA attribute may need manual verification',
      },
    ],
    testedPages: ['/', '/team-sales', '/harbor-city'],
  };

  const violationReport = basicValidator.generateReport(mockViolationResult);
  console.log('✅ Generated violation report:');
  console.log(`   - Length: ${violationReport.length} characters`);
  console.log(`   - Contains violations: ${violationReport.includes('Violations (Must Fix)') ? '✅' : '❌'}`);
  console.log(`   - Contains warnings: ${violationReport.includes('Warnings (Should Fix)') ? '✅' : '❌'}`);
  console.log(`   - Contains remediation: ${violationReport.includes('Remediation:') ? '✅' : '❌'}`);

  const mockCompliantResult = {
    compliant: true,
    violations: [],
    warnings: [],
    testedPages: ['/'],
  };

  const compliantReport = basicValidator.generateReport(mockCompliantResult);
  console.log('\n✅ Generated compliant report:');
  console.log(`   - Status: ${compliantReport.includes('✅ PASSED') ? 'PASSED' : 'FAILED'}`);
  console.log(`   - Success message: ${compliantReport.includes('All Tests Passed') ? '✅' : '❌'}`);

  // Demo 6: Test Suite Integration
  console.log('\n📋 Demo 6: Test Suite Integration');
  console.log('---------------------------------');
  
  const configManager = new ConfigManager({
    accessibilityLevel: 'AA',
    testSuites: [{
      name: 'accessibility-validation',
      type: 'accessibility',
      enabled: true,
      timeout: 60000,
      retries: 2,
    }],
  });

  const testSuite = new AccessibilityTestSuite(configManager);
  console.log('✅ Test suite created with ConfigManager:');
  console.log('   - WCAG Level from config: AA');
  console.log('   - Timeout: 60000ms');
  console.log('   - Retries: 2');

  // Demo 7: Advanced Configuration
  console.log('\n📋 Demo 7: Advanced Configuration Options');
  console.log('-----------------------------------------');
  
  const advancedValidator = new AccessibilityValidator({
    baseUrl: 'http://localhost:4175',
    pages: ['/', '/team-sales', '/harbor-city'],
    wcagLevel: 'AAA',
    includeRules: ['color-contrast', 'image-alt', 'label', 'button-name'],
    excludeRules: ['bypass', 'duplicate-id'],
    timeout: 45000,
    retries: 3,
    keyboardNavigation: true,
    colorContrast: true,
    screenReader: true,
  });

  console.log('✅ Advanced validator configured:');
  console.log('   - WCAG Level: AAA (highest standard)');
  console.log('   - Specific rules included: 4 rules');
  console.log('   - Rules excluded: 2 rules');
  console.log('   - Extended timeout: 45000ms');
  console.log('   - Retry attempts: 3');

  // Demo 8: Error Handling Demonstration
  console.log('\n📋 Demo 8: Error Handling Capabilities');
  console.log('--------------------------------------');
  
  console.log('✅ Error handling features:');
  console.log('   - Browser launch failures: Graceful degradation');
  console.log('   - Page navigation timeouts: Detailed error reporting');
  console.log('   - Axe-core analysis failures: Fallback to manual checks');
  console.log('   - Invalid color values: Safe default calculations');
  console.log('   - Network connectivity issues: Retry logic with backoff');

  // Demo 9: Feature Summary
  console.log('\n🎉 Feature Implementation Summary');
  console.log('=================================');
  
  const implementedFeatures = [
    '✅ Axe-core integration with Playwright',
    '✅ WCAG 2.1 AA compliance checking (A, AA, AAA levels)',
    '✅ Accessibility violation reporting with detailed descriptions',
    '✅ Comprehensive remediation guidance for 30+ rules',
    '✅ Keyboard navigation testing with focus management',
    '✅ Screen reader compatibility validation',
    '✅ Color contrast calculation and validation',
    '✅ Heading hierarchy and landmark structure testing',
    '✅ Form accessibility and label association checking',
    '✅ ARIA attribute validation and semantic markup testing',
    '✅ Test suite integration with ConfigManager',
    '✅ Multiple output formats (console, JSON, HTML reports)',
    '✅ Error handling and graceful failure recovery',
    '✅ Configurable timeouts, retries, and rule selection',
    '✅ Production-ready deployment verification',
  ];

  implementedFeatures.forEach(feature => console.log(`   ${feature}`));

  console.log('\n🚀 The accessibility validation system is fully implemented and ready!');
  console.log('\n📖 Usage Examples:');
  console.log('   - Basic validation: new AccessibilityValidator().validate()');
  console.log('   - Custom config: new AccessibilityValidator({ wcagLevel: "AAA" })');
  console.log('   - Test suite: new AccessibilityTestSuite(configManager).execute()');
  console.log('   - Report generation: validator.generateReport(results)');
  
  console.log('\n💡 Next Steps:');
  console.log('   1. Start your development server: npm run build && npm run preview');
  console.log('   2. Run accessibility validation: npm run test:accessibility');
  console.log('   3. Review generated reports for violations and warnings');
  console.log('   4. Apply remediation guidance to fix accessibility issues');
  console.log('   5. Re-run validation to verify fixes');
}

demoAccessibilityValidation().catch(console.error);