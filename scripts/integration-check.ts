#!/usr/bin/env tsx

/**
 * Integration check script to verify all verification system components
 * work correctly with the existing project structure
 */

import fs from 'fs';
import path from 'path';
import { execSync } from 'child_process';

interface IntegrationCheckResult {
  component: string;
  status: 'passed' | 'failed' | 'warning';
  message: string;
  details?: string;
}

class IntegrationChecker {
  private results: IntegrationCheckResult[] = [];
  private projectRoot: string;

  constructor() {
    this.projectRoot = process.cwd();
  }

  private addResult(component: string, status: 'passed' | 'failed' | 'warning', message: string, details?: string) {
    this.results.push({ component, status, message, details });
  }

  private log(message: string, type: 'info' | 'success' | 'warning' | 'error' = 'info') {
    const colors = {
      info: '\x1b[34m',
      success: '\x1b[32m',
      warning: '\x1b[33m',
      error: '\x1b[31m',
    };
    const reset = '\x1b[0m';
    console.log(`${colors[type]}[${type.toUpperCase()}]${reset} ${message}`);
  }

  async checkPackageJsonIntegration(): Promise<void> {
    this.log('Checking package.json integration...');
    
    try {
      const packageJsonPath = path.join(this.projectRoot, 'package.json');
      const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));

      // Check if verification scripts are present
      const requiredScripts = [
        'verify',
        'verify:verbose',
        'verify:json',
        'verify:html',
        'verify:build',
        'verify:test',
        'verify:performance',
        'verify:accessibility',
        'verify:ci',
        'ci:verify'
      ];

      const missingScripts = requiredScripts.filter(script => !packageJson.scripts[script]);
      
      if (missingScripts.length === 0) {
        this.addResult('package.json', 'passed', 'All verification scripts are present');
      } else {
        this.addResult('package.json', 'failed', `Missing scripts: ${missingScripts.join(', ')}`);
      }

      // Check if bin entry exists
      if (packageJson.bin && packageJson.bin['verify-deployment']) {
        this.addResult('package.json', 'passed', 'CLI binary entry is configured');
      } else {
        this.addResult('package.json', 'failed', 'CLI binary entry is missing');
      }

      // Check required dependencies
      const requiredDevDeps = ['jest-junit', '@playwright/test', 'tsx'];
      const missingDeps = requiredDevDeps.filter(dep => 
        !packageJson.dependencies[dep] && !packageJson.devDependencies[dep]
      );

      if (missingDeps.length === 0) {
        this.addResult('package.json', 'passed', 'All required dependencies are present');
      } else {
        this.addResult('package.json', 'warning', `Missing dependencies: ${missingDeps.join(', ')}`);
      }

    } catch (error) {
      this.addResult('package.json', 'failed', 'Failed to read or parse package.json', error.message);
    }
  }

  async checkJestIntegration(): Promise<void> {
    this.log('Checking Jest integration...');
    
    try {
      const jestConfigPath = path.join(this.projectRoot, 'jest.config.js');
      
      if (!fs.existsSync(jestConfigPath)) {
        this.addResult('Jest', 'failed', 'jest.config.js not found');
        return;
      }

      // Check if global setup/teardown files exist
      const globalSetupPath = path.join(this.projectRoot, 'src/test/globalSetup.js');
      const globalTeardownPath = path.join(this.projectRoot, 'src/test/globalTeardown.js');

      if (fs.existsSync(globalSetupPath) && fs.existsSync(globalTeardownPath)) {
        this.addResult('Jest', 'passed', 'Global setup and teardown files are configured');
      } else {
        this.addResult('Jest', 'failed', 'Global setup or teardown files are missing');
      }

      // Try to run Jest configuration validation
      try {
        execSync('npx jest --showConfig', { stdio: 'pipe', cwd: this.projectRoot });
        this.addResult('Jest', 'passed', 'Jest configuration is valid');
      } catch (error) {
        this.addResult('Jest', 'failed', 'Jest configuration validation failed', error.message);
      }

    } catch (error) {
      this.addResult('Jest', 'failed', 'Jest integration check failed', error.message);
    }
  }

  async checkPlaywrightIntegration(): Promise<void> {
    this.log('Checking Playwright integration...');
    
    try {
      const playwrightConfigPath = path.join(this.projectRoot, 'playwright.config.ts');
      
      if (!fs.existsSync(playwrightConfigPath)) {
        this.addResult('Playwright', 'failed', 'playwright.config.ts not found');
        return;
      }

      // Check if global setup/teardown files exist
      const globalSetupPath = path.join(this.projectRoot, 'src/test/playwright-global-setup.ts');
      const globalTeardownPath = path.join(this.projectRoot, 'src/test/playwright-global-teardown.ts');

      if (fs.existsSync(globalSetupPath) && fs.existsSync(globalTeardownPath)) {
        this.addResult('Playwright', 'passed', 'Global setup and teardown files are configured');
      } else {
        this.addResult('Playwright', 'failed', 'Global setup or teardown files are missing');
      }

      // Check if test directory exists
      const testsDir = path.join(this.projectRoot, 'tests');
      if (fs.existsSync(testsDir)) {
        const testFiles = fs.readdirSync(testsDir).filter(file => file.endsWith('.spec.ts'));
        this.addResult('Playwright', 'passed', `Found ${testFiles.length} test files`);
      } else {
        this.addResult('Playwright', 'warning', 'Tests directory not found');
      }

    } catch (error) {
      this.addResult('Playwright', 'failed', 'Playwright integration check failed', error.message);
    }
  }

  async checkCIIntegration(): Promise<void> {
    this.log('Checking CI/CD integration...');
    
    try {
      // Check GitHub workflow file
      const workflowPath = path.join(this.projectRoot, '.github/workflows/deployment-verification.yml');
      if (fs.existsSync(workflowPath)) {
        this.addResult('CI/CD', 'passed', 'GitHub workflow file exists');
      } else {
        this.addResult('CI/CD', 'warning', 'GitHub workflow file not found');
      }

      // Check CI script
      const ciScriptPath = path.join(this.projectRoot, 'scripts/ci-verification.sh');
      if (fs.existsSync(ciScriptPath)) {
        // Check if script is executable
        const stats = fs.statSync(ciScriptPath);
        if (stats.mode & parseInt('111', 8)) {
          this.addResult('CI/CD', 'passed', 'CI verification script is executable');
        } else {
          this.addResult('CI/CD', 'warning', 'CI verification script exists but is not executable');
        }
      } else {
        this.addResult('CI/CD', 'failed', 'CI verification script not found');
      }

    } catch (error) {
      this.addResult('CI/CD', 'failed', 'CI/CD integration check failed', error.message);
    }
  }

  async checkVerificationSystemIntegration(): Promise<void> {
    this.log('Checking verification system integration...');
    
    try {
      // Check if verification CLI exists
      const cliPath = path.join(this.projectRoot, 'src/verification/cli.ts');
      if (fs.existsSync(cliPath)) {
        this.addResult('Verification System', 'passed', 'CLI file exists');
      } else {
        this.addResult('Verification System', 'failed', 'CLI file not found');
      }

      // Check if bin executable exists
      const binPath = path.join(this.projectRoot, 'bin/verify-deployment');
      if (fs.existsSync(binPath)) {
        this.addResult('Verification System', 'passed', 'Binary executable exists');
      } else {
        this.addResult('Verification System', 'failed', 'Binary executable not found');
      }

      // Check if configuration file exists
      const configPath = path.join(this.projectRoot, 'verification.config.json');
      if (fs.existsSync(configPath)) {
        try {
          JSON.parse(fs.readFileSync(configPath, 'utf8'));
          this.addResult('Verification System', 'passed', 'Configuration file is valid JSON');
        } catch (error) {
          this.addResult('Verification System', 'failed', 'Configuration file is invalid JSON');
        }
      } else {
        this.addResult('Verification System', 'warning', 'Configuration file not found');
      }

      // Try to run verification CLI help command
      try {
        execSync('npx tsx src/verification/cli.ts --help', { stdio: 'pipe', cwd: this.projectRoot });
        this.addResult('Verification System', 'passed', 'CLI is functional');
      } catch (error) {
        this.addResult('Verification System', 'failed', 'CLI is not functional', error.message);
      }

    } catch (error) {
      this.addResult('Verification System', 'failed', 'Verification system check failed', error.message);
    }
  }

  async checkBuildIntegration(): Promise<void> {
    this.log('Checking build system integration...');
    
    try {
      // Check if Vite config exists
      const viteConfigPath = path.join(this.projectRoot, 'vite.config.ts');
      if (fs.existsSync(viteConfigPath)) {
        this.addResult('Build System', 'passed', 'Vite configuration exists');
      } else {
        this.addResult('Build System', 'warning', 'Vite configuration not found');
      }

      // Check if TypeScript config exists
      const tsConfigPath = path.join(this.projectRoot, 'tsconfig.json');
      if (fs.existsSync(tsConfigPath)) {
        this.addResult('Build System', 'passed', 'TypeScript configuration exists');
      } else {
        this.addResult('Build System', 'warning', 'TypeScript configuration not found');
      }

      // Try to run build command
      try {
        execSync('npm run build', { stdio: 'pipe', cwd: this.projectRoot });
        this.addResult('Build System', 'passed', 'Build command executes successfully');
      } catch (error) {
        this.addResult('Build System', 'failed', 'Build command failed', error.message);
      }

    } catch (error) {
      this.addResult('Build System', 'failed', 'Build system check failed', error.message);
    }
  }

  async checkDirectoryStructure(): Promise<void> {
    this.log('Checking directory structure...');
    
    try {
      const requiredDirs = [
        'src/verification',
        'src/test',
        'tests',
        'scripts',
        'bin',
        '.github/workflows'
      ];

      const missingDirs = requiredDirs.filter(dir => 
        !fs.existsSync(path.join(this.projectRoot, dir))
      );

      if (missingDirs.length === 0) {
        this.addResult('Directory Structure', 'passed', 'All required directories exist');
      } else {
        this.addResult('Directory Structure', 'failed', `Missing directories: ${missingDirs.join(', ')}`);
      }

      // Check if reports directory can be created
      const reportsDir = path.join(this.projectRoot, 'verification-reports');
      if (!fs.existsSync(reportsDir)) {
        fs.mkdirSync(reportsDir, { recursive: true });
        this.addResult('Directory Structure', 'passed', 'Reports directory created successfully');
      } else {
        this.addResult('Directory Structure', 'passed', 'Reports directory already exists');
      }

    } catch (error) {
      this.addResult('Directory Structure', 'failed', 'Directory structure check failed', error.message);
    }
  }

  async runAllChecks(): Promise<void> {
    this.log('Starting integration checks...', 'info');
    
    await this.checkDirectoryStructure();
    await this.checkPackageJsonIntegration();
    await this.checkJestIntegration();
    await this.checkPlaywrightIntegration();
    await this.checkCIIntegration();
    await this.checkVerificationSystemIntegration();
    await this.checkBuildIntegration();
  }

  generateReport(): void {
    this.log('\n=== Integration Check Report ===', 'info');
    
    const passed = this.results.filter(r => r.status === 'passed').length;
    const failed = this.results.filter(r => r.status === 'failed').length;
    const warnings = this.results.filter(r => r.status === 'warning').length;
    
    this.log(`Total checks: ${this.results.length}`, 'info');
    this.log(`Passed: ${passed}`, 'success');
    this.log(`Failed: ${failed}`, failed > 0 ? 'error' : 'info');
    this.log(`Warnings: ${warnings}`, warnings > 0 ? 'warning' : 'info');
    
    console.log('\nDetailed Results:');
    this.results.forEach(result => {
      const icon = result.status === 'passed' ? '✅' : result.status === 'warning' ? '⚠️' : '❌';
      console.log(`${icon} ${result.component}: ${result.message}`);
      if (result.details) {
        console.log(`   Details: ${result.details}`);
      }
    });

    // Save report to file
    const reportPath = path.join(this.projectRoot, 'verification-reports', 'integration-check.json');
    const report = {
      timestamp: new Date().toISOString(),
      summary: { total: this.results.length, passed, failed, warnings },
      results: this.results
    };
    
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    this.log(`\nReport saved to: ${reportPath}`, 'info');

    // Exit with error code if there are failures
    if (failed > 0) {
      process.exit(1);
    }
  }
}

async function main() {
  const checker = new IntegrationChecker();
  await checker.runAllChecks();
  checker.generateReport();
}

// Run main function if this is the entry point
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(error => {
    console.error('Integration check failed:', error);
    process.exit(1);
  });
}

export { IntegrationChecker };