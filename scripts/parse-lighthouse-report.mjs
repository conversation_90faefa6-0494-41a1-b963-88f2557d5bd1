import fs from 'fs/promises';
import path from 'path';

async function parseLighthouseReport() {
  const reportPath = path.resolve(process.cwd(), 'lighthouse-report.json');
  const reportContent = await fs.readFile(reportPath, 'utf-8');
  const report = JSON.parse(reportContent);

  const performance = report.categories.performance.score * 100;
  const accessibility = report.categories.accessibility.score * 100;
  const bestPractices = report.categories['best-practices'].score * 100;
  const seo = report.categories.seo.score * 100;

  const auditPath = path.resolve(process.cwd(), 'PERFORMANCE_AUDIT.md');
  let auditContent = await fs.readFile(auditPath, 'utf-8');

  auditContent = auditContent.replace('| **Performance** | | |', `| **Performance** | ${performance} | |`);
  auditContent = auditContent.replace('| **Accessibility** | | |', `| **Accessibility** | ${accessibility} | |`);
  auditContent = auditContent.replace('| **Best Practices** | | |', `| **Best Practices** | ${bestPractices} | |`);
  auditContent = auditContent.replace('| **SEO** | | |', `| **SEO** | ${seo} | |`);

  await fs.writeFile(auditPath, auditContent);

  console.log('Performance audit report updated with Lighthouse scores.');
}

parseLighthouseReport();