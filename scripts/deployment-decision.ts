#!/usr/bin/env tsx

/**
 * Deployment decision script that integrates with existing deployment processes
 * This script determines if the application is ready for deployment based on verification results
 */

import fs from 'fs';
import path from 'path';
import { execSync } from 'child_process';

interface DeploymentDecision {
  ready: boolean;
  reason: string;
  recommendations: string[];
  verificationResults?: any;
  timestamp: string;
}

class DeploymentDecisionMaker {
  private projectRoot: string;
  private reportsDir: string;

  constructor() {
    this.projectRoot = process.cwd();
    this.reportsDir = path.join(this.projectRoot, 'verification-reports');
  }

  private log(message: string, type: 'info' | 'success' | 'warning' | 'error' = 'info') {
    const colors = {
      info: '\x1b[34m',
      success: '\x1b[32m',
      warning: '\x1b[33m',
      error: '\x1b[31m',
    };
    const reset = '\x1b[0m';
    console.log(`${colors[type]}[${type.toUpperCase()}]${reset} ${message}`);
  }

  async runVerification(): Promise<any> {
    this.log('Running comprehensive verification...');
    
    try {
      // Ensure reports directory exists
      if (!fs.existsSync(this.reportsDir)) {
        fs.mkdirSync(this.reportsDir, { recursive: true });
      }

      // Run verification with JSON output
      const reportFile = path.join(this.reportsDir, `deployment-verification-${Date.now()}.json`);
      
      execSync(`npx tsx src/verification/cli.ts run --output json --output-file "${reportFile}" --timeout 600`, {
        stdio: 'inherit',
        cwd: this.projectRoot,
        env: {
          ...process.env,
          NODE_ENV: 'production',
          VERIFICATION_MODE: 'deployment'
        }
      });

      // Read and return the verification results
      if (fs.existsSync(reportFile)) {
        const results = JSON.parse(fs.readFileSync(reportFile, 'utf8'));
        this.log('Verification completed successfully');
        return results;
      } else {
        throw new Error('Verification report file not found');
      }

    } catch (error) {
      this.log(`Verification failed: ${error.message}`, 'error');
      throw error;
    }
  }

  analyzeResults(verificationResults: any): DeploymentDecision {
    const decision: DeploymentDecision = {
      ready: false,
      reason: '',
      recommendations: [],
      verificationResults,
      timestamp: new Date().toISOString()
    };

    // Check overall status
    if (verificationResults.overallStatus === 'failed') {
      decision.ready = false;
      decision.reason = 'Verification failed - critical issues detected';
      decision.recommendations.push('Fix all critical issues before attempting deployment');
      return decision;
    }

    // Check deployment readiness flag
    if (!verificationResults.deploymentReady) {
      decision.ready = false;
      decision.reason = 'Application not ready for deployment based on verification criteria';
      
      // Analyze specific failures
      if (!verificationResults.buildVerification.success) {
        decision.recommendations.push('Fix build errors before deployment');
      }

      if (verificationResults.testResults.some((test: any) => !test.passed)) {
        decision.recommendations.push('Fix failing tests before deployment');
      }

      if (verificationResults.performanceMetrics.lighthouse.performance < 90) {
        decision.recommendations.push('Improve performance metrics to meet deployment standards');
      }

      if (!verificationResults.accessibilityResults.compliant) {
        decision.recommendations.push('Fix accessibility violations before deployment');
      }

      if (!verificationResults.pwaValidation.serviceWorkerRegistered || !verificationResults.pwaValidation.manifestValid) {
        decision.recommendations.push('Fix PWA configuration issues');
      }

      return decision;
    }

    // Check for warnings that might affect deployment
    if (verificationResults.overallStatus === 'warning') {
      decision.ready = true;
      decision.reason = 'Application ready for deployment with warnings';
      decision.recommendations.push('Review warnings and consider addressing them in the next release');
      
      // Add specific warning recommendations
      if (verificationResults.recommendations && verificationResults.recommendations.length > 0) {
        decision.recommendations.push(...verificationResults.recommendations);
      }
      
      return decision;
    }

    // All checks passed
    decision.ready = true;
    decision.reason = 'All verification checks passed - ready for deployment';
    decision.recommendations.push('Deployment approved - all systems green');

    return decision;
  }

  async checkExistingDeployment(): Promise<boolean> {
    this.log('Checking existing deployment status...');
    
    try {
      // Check if there's a recent successful deployment
      const deploymentFile = path.join(this.reportsDir, 'last-deployment.json');
      
      if (fs.existsSync(deploymentFile)) {
        const lastDeployment = JSON.parse(fs.readFileSync(deploymentFile, 'utf8'));
        const lastDeploymentTime = new Date(lastDeployment.timestamp);
        const timeDiff = Date.now() - lastDeploymentTime.getTime();
        const hoursDiff = timeDiff / (1000 * 60 * 60);
        
        if (hoursDiff < 1 && lastDeployment.success) {
          this.log('Recent successful deployment found', 'warning');
          return true;
        }
      }
      
      return false;
    } catch (error) {
      this.log(`Error checking deployment status: ${error.message}`, 'warning');
      return false;
    }
  }

  saveDecision(decision: DeploymentDecision): void {
    const decisionFile = path.join(this.reportsDir, 'deployment-decision.json');
    fs.writeFileSync(decisionFile, JSON.stringify(decision, null, 2));
    
    // Also save to a timestamped file for history
    const timestampedFile = path.join(this.reportsDir, `deployment-decision-${Date.now()}.json`);
    fs.writeFileSync(timestampedFile, JSON.stringify(decision, null, 2));
    
    this.log(`Deployment decision saved to: ${decisionFile}`);
  }

  displayDecision(decision: DeploymentDecision): void {
    console.log('\n=== DEPLOYMENT DECISION ===');
    
    if (decision.ready) {
      this.log(`✅ DEPLOYMENT APPROVED`, 'success');
    } else {
      this.log(`❌ DEPLOYMENT BLOCKED`, 'error');
    }
    
    console.log(`\nReason: ${decision.reason}`);
    console.log(`Timestamp: ${decision.timestamp}`);
    
    if (decision.recommendations.length > 0) {
      console.log('\nRecommendations:');
      decision.recommendations.forEach((rec, index) => {
        console.log(`  ${index + 1}. ${rec}`);
      });
    }

    // Display key metrics if available
    if (decision.verificationResults) {
      const results = decision.verificationResults;
      console.log('\nKey Metrics:');
      console.log(`  Build Status: ${results.buildVerification.success ? '✅' : '❌'}`);
      console.log(`  Test Results: ${results.testResults.filter((t: any) => t.passed).length}/${results.testResults.length} passed`);
      console.log(`  Performance Score: ${results.performanceMetrics.lighthouse.performance}/100`);
      console.log(`  Accessibility: ${results.accessibilityResults.compliant ? '✅' : '❌'} (${results.accessibilityResults.violations.length} violations)`);
      console.log(`  PWA Status: ${results.pwaValidation.serviceWorkerRegistered && results.pwaValidation.manifestValid ? '✅' : '❌'}`);
    }

    console.log('\n========================\n');
  }

  async updateDeploymentRecord(decision: DeploymentDecision): Promise<void> {
    if (decision.ready) {
      const deploymentRecord = {
        timestamp: decision.timestamp,
        success: true,
        verificationResults: decision.verificationResults,
        decision: decision
      };
      
      const deploymentFile = path.join(this.reportsDir, 'last-deployment.json');
      fs.writeFileSync(deploymentFile, JSON.stringify(deploymentRecord, null, 2));
      
      this.log('Deployment record updated');
    }
  }
}

async function main() {
  const args = process.argv.slice(2);
  const skipVerification = args.includes('--skip-verification');
  const forceCheck = args.includes('--force');
  
  const decisionMaker = new DeploymentDecisionMaker();
  
  try {
    // Check if we should skip due to recent deployment
    if (!forceCheck) {
      const recentDeployment = await decisionMaker.checkExistingDeployment();
      if (recentDeployment) {
        console.log('Recent successful deployment found. Use --force to override.');
        process.exit(0);
      }
    }

    let verificationResults;
    
    if (skipVerification) {
      // Try to load the most recent verification results
      const reportsDir = path.join(process.cwd(), 'verification-reports');
      const reportFiles = fs.readdirSync(reportsDir)
        .filter(file => file.startsWith('deployment-verification-') && file.endsWith('.json'))
        .sort()
        .reverse();
      
      if (reportFiles.length > 0) {
        const latestReport = path.join(reportsDir, reportFiles[0]);
        verificationResults = JSON.parse(fs.readFileSync(latestReport, 'utf8'));
        console.log(`Using existing verification results from: ${reportFiles[0]}`);
      } else {
        throw new Error('No existing verification results found. Run without --skip-verification.');
      }
    } else {
      verificationResults = await decisionMaker.runVerification();
    }

    const decision = decisionMaker.analyzeResults(verificationResults);
    
    decisionMaker.saveDecision(decision);
    decisionMaker.displayDecision(decision);
    
    if (decision.ready) {
      await decisionMaker.updateDeploymentRecord(decision);
    }

    // Exit with appropriate code
    process.exit(decision.ready ? 0 : 1);

  } catch (error) {
    console.error('Deployment decision process failed:', error.message);
    
    const failedDecision: DeploymentDecision = {
      ready: false,
      reason: `Deployment decision process failed: ${error.message}`,
      recommendations: ['Fix verification system issues before attempting deployment'],
      timestamp: new Date().toISOString()
    };
    
    decisionMaker.saveDecision(failedDecision);
    decisionMaker.displayDecision(failedDecision);
    
    process.exit(1);
  }
}

// Run main function if this is the entry point
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { DeploymentDecisionMaker };