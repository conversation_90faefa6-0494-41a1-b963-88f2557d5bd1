#!/usr/bin/env tsx

/**
 * Demonstration script for the error handling and recovery system
 * 
 * This script shows how the error handling system works with different
 * types of errors, retry logic, and recovery suggestions.
 */

import { createErrorHandler, createLogger } from '../src/verification/error-handling';

async function demonstrateErrorHandling() {
  console.log('🔧 Error Handling and Recovery System Demo\n');

  // Create error handler with console logging enabled
  const errorHandler = createErrorHandler(
    {
      level: 'info',
      enableConsole: true,
      enableFile: false
    },
    {
      maxRetries: 3,
      baseDelay: 500,
      maxDelay: 5000
    }
  );

  const logger = errorHandler.getLogger();

  // Demo 1: Show error creation and handling (without retry to avoid async issues)
  console.log('📋 Demo 1: Error Creation and Handling');
  console.log('=' .repeat(50));
  
  const networkError = errorHandler.createError(
    'dependency',
    'dependency',
    'error',
    'ECONNRESET: Connection reset by peer',
    {
      details: 'Network connection was reset during API call',
      context: { endpoint: 'https://api.example.com/data', timeout: 5000 }
    }
  );

  const networkResult = await errorHandler.handleError(networkError);
  console.log(`Error: ${networkError.message}`);
  console.log(`Recoverable: ${networkError.recoverable}`);
  console.log(`Max retries: ${networkError.maxRetries}`);
  console.log('Recovery actions:');
  networkResult.recoveryActions.slice(0, 3).forEach(action => {
    console.log(`   • ${action}`);
  });
  console.log();

  // Demo 2: Non-retryable error with recovery suggestions
  console.log('📋 Demo 2: Non-Retryable Configuration Error');
  console.log('=' .repeat(50));

  const configError = errorHandler.createError(
    'build',
    'build',
    'error',
    'Syntax error in configuration file',
    {
      details: 'Invalid JSON syntax found in verification.config.json',
      context: { line: 15, column: 23, file: 'verification.config.json' }
    }
  );

  const configResult = await errorHandler.handleError(configError);
  console.log(`Error: ${configError.message}`);
  console.log(`Recoverable: ${configError.recoverable}`);
  console.log(`Max retries: ${configError.maxRetries}`);
  console.log('Recovery actions:');
  configResult.recoveryActions.slice(0, 3).forEach(action => {
    console.log(`   • ${action}`);
  });
  console.log();

  // Demo 3: Error creation and handling
  console.log('📋 Demo 3: Manual Error Creation and Handling');
  console.log('=' .repeat(50));

  const buildError = errorHandler.createError(
    'build',
    'build',
    'error',
    'TypeScript compilation failed with 5 errors',
    {
      details: 'Multiple type errors found in src/components/',
      context: {
        errorCount: 5,
        affectedFiles: ['Header.tsx', 'Footer.tsx', 'Navigation.tsx']
      }
    }
  );

  const recoveryResult = await errorHandler.handleError(buildError);
  console.log(`Error handled: ${buildError.message}`);
  console.log(`Recoverable: ${buildError.recoverable}`);
  console.log(`Max retries: ${buildError.maxRetries}`);
  console.log('\n💡 Recovery actions:');
  recoveryResult.recoveryActions.forEach(action => {
    console.log(`   • ${action}`);
  });
  console.log();

  // Demo 4: Error statistics
  console.log('📋 Demo 4: Error Statistics');
  console.log('=' .repeat(50));

  // Create some additional errors for statistics
  errorHandler.createError('test', 'test', 'warning', 'Test suite took longer than expected');
  errorHandler.createError('performance', 'performance', 'error', 'LCP threshold exceeded');
  errorHandler.createError('accessibility', 'accessibility', 'critical', 'Critical WCAG violation found');

  const stats = errorHandler.getErrorStatistics();
  console.log(`Total errors: ${stats.totalErrors}`);
  console.log(`Recoverable errors: ${stats.recoverableErrors}`);
  console.log('\nErrors by stage:');
  Object.entries(stats.errorsByStage).forEach(([stage, count]) => {
    console.log(`   ${stage}: ${count}`);
  });
  console.log('\nErrors by severity:');
  Object.entries(stats.errorsBySeverity).forEach(([severity, count]) => {
    console.log(`   ${severity}: ${count}`);
  });
  console.log();

  // Demo 5: Different error types and their recovery actions
  console.log('📋 Demo 5: Error Type-Specific Recovery Actions');
  console.log('=' .repeat(50));

  const errorTypes = [
    { type: 'build' as const, message: 'Build compilation failed' },
    { type: 'test' as const, message: 'Browser launch failed' },
    { type: 'performance' as const, message: 'Performance threshold exceeded' },
    { type: 'accessibility' as const, message: 'WCAG violations found' },
    { type: 'pwa' as const, message: 'Service worker registration failed' },
    { type: 'dependency' as const, message: 'External API unavailable' }
  ];

  for (const { type, message } of errorTypes) {
    const error = errorHandler.createError('demo', type, 'error', message);
    const result = await errorHandler.handleError(error);
    
    console.log(`\n${type.toUpperCase()} Error Recovery Actions:`);
    result.recoveryActions.slice(0, 3).forEach(action => {
      console.log(`   • ${action}`);
    });
  }

  console.log('\n🎉 Error handling demonstration completed!');
  console.log('\nThe error handling system provides:');
  console.log('   ✅ Automatic retry logic for transient errors');
  console.log('   ✅ Intelligent error classification');
  console.log('   ✅ Context-aware recovery suggestions');
  console.log('   ✅ Comprehensive logging and debugging');
  console.log('   ✅ Error statistics and monitoring');
  console.log('   ✅ Graceful failure handling');

  // Cleanup
  await errorHandler.cleanup();
}

// Run the demonstration
if (import.meta.url === `file://${process.argv[1]}`) {
  demonstrateErrorHandling().catch(error => {
    console.error('Demo failed:', error);
    process.exit(1);
  });
}

export { demonstrateErrorHandling };