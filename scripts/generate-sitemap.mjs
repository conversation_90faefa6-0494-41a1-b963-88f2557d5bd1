import fs from 'fs/promises';
import path from 'path';

// Assuming the structure of your products file
// You might need to adjust the import path and data structure
async function getProducts() {
  const productsPath = path.resolve(process.cwd(), 'src', 'data', 'products.ts');
  try {
    const module = await import(productsPath);
    return module.teamProducts || [];
  } catch (error) {
    console.error('Error reading products file:', error);
    // Attempt to read and parse the file manually as a fallback
    try {
      const fileContent = await fs.readFile(productsPath, 'utf-8');
      const match = fileContent.match(/export const teamProducts: Product\[\] = ([\s\S]*?);/);
      if (match && match[1]) {
        // This is a very brittle way to parse the products, but it's a fallback
        // It assumes the array is JSON-like. This will likely fail if the object is complex.
        const productsArrayStr = match[1].trim();
        // A safer eval alternative
        const products = (new Function(`return ${productsArrayStr}`))();
        return products;
      }
    } catch (e) {
      console.error('Could not manually parse products.', e);
    }
    return [];
  }
}

const BASE_URL = 'https://www.iceboxhockey.com';

async function generateSitemap() {
  const products = await getProducts();

  const staticPages = [
    {
      loc: `${BASE_URL}/`,
      lastmod: new Date().toISOString().split('T')[0],
      changefreq: 'weekly',
      priority: '1.0',
    },
    {
      loc: `${BASE_URL}/teamsales`,
      lastmod: new Date().toISOString().split('T')[0],
      changefreq: 'monthly',
      priority: '0.8',
    },
    {
      loc: `${BASE_URL}/harbor-city`,
      lastmod: new Date().toISOString().split('T')[0],
      changefreq: 'weekly',
      priority: '0.9',
    },
  ];

  const productPages = products.map(product => ({
    loc: `${BASE_URL}/product/${product.id}`,
    lastmod: new Date().toISOString().split('T')[0],
    changefreq: 'weekly',
    priority: '0.8',
  }));

  const allPages = [...staticPages, ...productPages];

  const sitemap = `
<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  ${allPages
    .map(
      page => `
    <url>
      <loc>${page.loc}</loc>
      <lastmod>${page.lastmod}</lastmod>
      <changefreq>${page.changefreq}</changefreq>
      <priority>${page.priority}</priority>
    </url>`,
    )
    .join('')}
</urlset>
  `.trim();

  const publicPath = path.resolve(process.cwd(), 'public');
  await fs.mkdir(publicPath, { recursive: true });
  await fs.writeFile(path.join(publicPath, 'sitemap.xml'), sitemap);

  console.log('Sitemap generated successfully!');
}

generateSitemap();