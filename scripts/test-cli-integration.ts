#!/usr/bin/env tsx

/**
 * CLI Integration Test
 * Tests the integration between the verification system and existing project infrastructure
 */

import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';

interface TestResult {
  name: string;
  passed: boolean;
  message: string;
  duration: number;
}

class CLIIntegrationTester {
  private results: TestResult[] = [];
  private projectRoot: string;

  constructor() {
    this.projectRoot = process.cwd();
  }

  private async runTest(name: string, testFn: () => Promise<void>): Promise<void> {
    const startTime = Date.now();
    
    try {
      await testFn();
      const duration = Date.now() - startTime;
      this.results.push({
        name,
        passed: true,
        message: 'Test passed',
        duration
      });
      console.log(`✅ ${name} (${duration}ms)`);
    } catch (error) {
      const duration = Date.now() - startTime;
      this.results.push({
        name,
        passed: false,
        message: error.message,
        duration
      });
      console.log(`❌ ${name} (${duration}ms): ${error.message}`);
    }
  }

  async testCLIHelp(): Promise<void> {
    const output = execSync('npx tsx src/verification/cli.ts --help', { 
      encoding: 'utf8',
      cwd: this.projectRoot 
    });
    
    if (!output.includes('Production deployment verification tool')) {
      throw new Error('CLI help output does not contain expected content');
    }
  }

  async testCLIInit(): Promise<void> {
    const configPath = path.join(this.projectRoot, 'test-verification.config.json');
    const originalConfigPath = path.join(this.projectRoot, 'verification.config.json');
    
    try {
      // Backup original config if it exists
      let originalConfig = null;
      if (fs.existsSync(originalConfigPath)) {
        originalConfig = fs.readFileSync(originalConfigPath, 'utf8');
        fs.unlinkSync(originalConfigPath);
      }
      
      // Run init command
      execSync('npx tsx src/verification/cli.ts init --force', {
        cwd: this.projectRoot,
        stdio: 'pipe'
      });
      
      if (!fs.existsSync(originalConfigPath)) {
        throw new Error('Configuration file was not created');
      }
      
      // Validate the configuration
      const config = JSON.parse(fs.readFileSync(originalConfigPath, 'utf8'));
      if (!config.default || !config.environments) {
        throw new Error('Configuration file structure is invalid');
      }
      
      // Restore original config
      if (originalConfig) {
        fs.writeFileSync(originalConfigPath, originalConfig);
      }
      
    } catch (error) {
      // Restore original config on error
      const originalConfigPath = path.join(this.projectRoot, 'verification.config.json');
      if (fs.existsSync(originalConfigPath)) {
        // Keep the current config since init worked
      }
      throw error;
    }
  }

  async testCLIValidateConfig(): Promise<void> {
    execSync('npx tsx src/verification/cli.ts validate-config', {
      cwd: this.projectRoot,
      stdio: 'pipe'
    });
  }

  async testCLIHealthCheck(): Promise<void> {
    execSync('npx tsx src/verification/cli.ts health-check', {
      cwd: this.projectRoot,
      stdio: 'pipe'
    });
  }

  async testJestIntegration(): Promise<void> {
    // Run a simple Jest test to verify integration
    execSync('npm test -- --testPathPattern=__tests__ --passWithNoTests', {
      cwd: this.projectRoot,
      stdio: 'pipe',
      env: {
        ...process.env,
        CI: 'true'
      }
    });
    
    // Check if Jest reports were generated
    const reportsDir = path.join(this.projectRoot, 'verification-reports');
    if (fs.existsSync(reportsDir)) {
      const jestReport = path.join(reportsDir, 'jest-results.xml');
      if (!fs.existsSync(jestReport)) {
        console.warn('Jest XML report not generated, but test passed');
      }
    }
  }

  async testPlaywrightIntegration(): Promise<void> {
    // Check if Playwright configuration is valid
    execSync('npx playwright test --list', {
      cwd: this.projectRoot,
      stdio: 'pipe'
    });
  }

  async testBuildIntegration(): Promise<void> {
    // Test that build works with verification system
    execSync('npm run build', {
      cwd: this.projectRoot,
      stdio: 'pipe'
    });
    
    // Check if dist directory was created
    const distDir = path.join(this.projectRoot, 'dist');
    if (!fs.existsSync(distDir)) {
      throw new Error('Build did not create dist directory');
    }
  }

  async testVerificationBuild(): Promise<void> {
    execSync('npx tsx src/verification/cli.ts build', {
      cwd: this.projectRoot,
      stdio: 'pipe'
    });
  }

  async testIntegrationCheck(): Promise<void> {
    execSync('npm run integration:check', {
      cwd: this.projectRoot,
      stdio: 'pipe'
    });
  }

  async testCIScript(): Promise<void> {
    // Test CI script with skip setup to avoid full installation
    execSync('./scripts/ci-verification.sh --skip-setup --help', {
      cwd: this.projectRoot,
      stdio: 'pipe'
    });
  }

  async testBinaryExecutable(): Promise<void> {
    const binPath = path.join(this.projectRoot, 'bin/verify-deployment.mjs');
    
    if (!fs.existsSync(binPath)) {
      throw new Error('Binary executable does not exist');
    }
    
    // Test that the binary works
    execSync(`node ${binPath} --help`, {
      cwd: this.projectRoot,
      stdio: 'pipe'
    });
  }

  async runAllTests(): Promise<void> {
    console.log('🧪 Running CLI Integration Tests...\n');
    
    await this.runTest('CLI Help Command', () => this.testCLIHelp());
    await this.runTest('CLI Init Command', () => this.testCLIInit());
    await this.runTest('CLI Validate Config', () => this.testCLIValidateConfig());
    await this.runTest('CLI Health Check', () => this.testCLIHealthCheck());
    await this.runTest('Binary Executable', () => this.testBinaryExecutable());
    await this.runTest('CI Script', () => this.testCIScript());
    
    // Skip slower tests in basic integration check
    if (process.env.FULL_INTEGRATION_TEST === 'true') {
      await this.runTest('Jest Integration', () => this.testJestIntegration());
      await this.runTest('Playwright Integration', () => this.testPlaywrightIntegration());
      await this.runTest('Build Integration', () => this.testBuildIntegration());
      await this.runTest('Verification Build', () => this.testVerificationBuild());
      await this.runTest('Integration Check Script', () => this.testIntegrationCheck());
    } else {
      console.log('⏭️  Skipping slower tests (set FULL_INTEGRATION_TEST=true to run all tests)');
    }
  }

  generateReport(): void {
    const passed = this.results.filter(r => r.passed).length;
    const failed = this.results.filter(r => !r.passed).length;
    const totalDuration = this.results.reduce((sum, r) => sum + r.duration, 0);
    
    console.log('\n=== CLI Integration Test Report ===');
    console.log(`Total Tests: ${this.results.length}`);
    console.log(`Passed: ${passed}`);
    console.log(`Failed: ${failed}`);
    console.log(`Total Duration: ${totalDuration}ms`);
    
    if (failed > 0) {
      console.log('\nFailed Tests:');
      this.results
        .filter(r => !r.passed)
        .forEach(r => {
          console.log(`  ❌ ${r.name}: ${r.message}`);
        });
    }
    
    // Save report
    const reportsDir = path.join(this.projectRoot, 'verification-reports');
    if (!fs.existsSync(reportsDir)) {
      fs.mkdirSync(reportsDir, { recursive: true });
    }
    
    const reportPath = path.join(reportsDir, 'cli-integration-test.json');
    const report = {
      timestamp: new Date().toISOString(),
      summary: { total: this.results.length, passed, failed, totalDuration },
      results: this.results
    };
    
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    console.log(`\nReport saved to: ${reportPath}`);
    
    if (failed > 0) {
      process.exit(1);
    }
  }
}

async function main() {
  const tester = new CLIIntegrationTester();
  
  try {
    await tester.runAllTests();
    tester.generateReport();
  } catch (error) {
    console.error('CLI integration test failed:', error);
    process.exit(1);
  }
}

// Run main function if this is the entry point
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { CLIIntegrationTester };