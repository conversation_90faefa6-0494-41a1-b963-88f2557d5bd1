#!/bin/bash

# CI/CD Verification Script for Production Deployment
# This script is designed to be used in continuous integration pipelines

set -e  # Exit on any error

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"
REPORT_DIR="${PROJECT_ROOT}/verification-reports"
TIMESTAMP=$(date -u +"%Y-%m-%dT%H-%M-%S")
REPORT_FILE="${REPORT_DIR}/ci-verification-${TIMESTAMP}.json"
HTML_REPORT_FILE="${REPORT_DIR}/ci-verification-${TIMESTAMP}.html"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check Node.js
    if ! command -v node &> /dev/null; then
        log_error "Node.js is not installed"
        exit 1
    fi
    
    # Check npm
    if ! command -v npm &> /dev/null; then
        log_error "npm is not installed"
        exit 1
    fi
    
    # Check if we're in the right directory
    if [ ! -f "${PROJECT_ROOT}/package.json" ]; then
        log_error "package.json not found. Are you in the project root?"
        exit 1
    fi
    
    log_success "Prerequisites check passed"
}

# Function to setup environment
setup_environment() {
    log_info "Setting up environment..."
    
    # Create reports directory
    mkdir -p "$REPORT_DIR"
    
    # Install dependencies if node_modules doesn't exist
    if [ ! -d "${PROJECT_ROOT}/node_modules" ]; then
        log_info "Installing dependencies..."
        cd "$PROJECT_ROOT"
        npm ci
    fi
    
    # Install Playwright browsers if needed
    if [ ! -d "${HOME}/.cache/ms-playwright" ] && [ ! -d "${PROJECT_ROOT}/node_modules/@playwright/test" ]; then
        log_info "Installing Playwright browsers..."
        npx playwright install
    fi
    
    log_success "Environment setup completed"
}

# Function to run verification
run_verification() {
    log_info "Starting verification pipeline..."
    
    cd "$PROJECT_ROOT"
    
    # Set CI environment variables
    export CI=true
    export NODE_ENV=production
    
    # Run verification with CI-specific options
    if npx tsx src/verification/cli.ts run \
        --ci \
        --output json \
        --output-file "$REPORT_FILE" \
        --timeout 600 \
        --verbose; then
        
        log_success "Verification completed successfully"
        
        # Generate HTML report for artifacts
        if [ -f "$REPORT_FILE" ]; then
            npx tsx src/verification/cli.ts report \
                --input "$REPORT_FILE" \
                --output html \
                --output-file "$HTML_REPORT_FILE"
            log_info "HTML report generated: $HTML_REPORT_FILE"
        fi
        
        return 0
    else
        log_error "Verification failed"
        
        # Still generate HTML report for debugging
        if [ -f "$REPORT_FILE" ]; then
            npx tsx src/verification/cli.ts report \
                --input "$REPORT_FILE" \
                --output html \
                --output-file "$HTML_REPORT_FILE"
            log_info "HTML report generated for debugging: $HTML_REPORT_FILE"
        fi
        
        return 1
    fi
}

# Function to handle artifacts
handle_artifacts() {
    log_info "Handling CI artifacts..."
    
    # Copy reports to CI artifacts directory if it exists
    if [ -n "$CI_ARTIFACTS_DIR" ] && [ -d "$CI_ARTIFACTS_DIR" ]; then
        cp "$REPORT_FILE" "$CI_ARTIFACTS_DIR/" 2>/dev/null || true
        cp "$HTML_REPORT_FILE" "$CI_ARTIFACTS_DIR/" 2>/dev/null || true
        log_info "Reports copied to CI artifacts directory"
    fi
    
    # Set output variables for CI systems
    if [ -f "$REPORT_FILE" ]; then
        # Extract key metrics from JSON report
        DEPLOYMENT_READY=$(node -e "
            const report = JSON.parse(require('fs').readFileSync('$REPORT_FILE', 'utf8'));
            console.log(report.deploymentReady);
        " 2>/dev/null || echo "false")
        
        OVERALL_STATUS=$(node -e "
            const report = JSON.parse(require('fs').readFileSync('$REPORT_FILE', 'utf8'));
            console.log(report.overallStatus);
        " 2>/dev/null || echo "failed")
        
        # Set environment variables for CI
        echo "DEPLOYMENT_READY=$DEPLOYMENT_READY" >> "${GITHUB_ENV:-/dev/null}" 2>/dev/null || true
        echo "VERIFICATION_STATUS=$OVERALL_STATUS" >> "${GITHUB_ENV:-/dev/null}" 2>/dev/null || true
        echo "VERIFICATION_REPORT=$REPORT_FILE" >> "${GITHUB_ENV:-/dev/null}" 2>/dev/null || true
        
        log_info "Deployment ready: $DEPLOYMENT_READY"
        log_info "Overall status: $OVERALL_STATUS"
    fi
}

# Function to cleanup
cleanup() {
    log_info "Cleaning up..."
    
    # Remove old reports (keep last 10)
    if [ -d "$REPORT_DIR" ]; then
        find "$REPORT_DIR" -name "ci-verification-*.json" -type f | sort -r | tail -n +11 | xargs rm -f 2>/dev/null || true
        find "$REPORT_DIR" -name "ci-verification-*.html" -type f | sort -r | tail -n +11 | xargs rm -f 2>/dev/null || true
    fi
    
    log_success "Cleanup completed"
}

# Function to display usage
usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  --skip-setup    Skip environment setup"
    echo "  --keep-reports  Don't cleanup old reports"
    echo "  --help         Show this help message"
    echo ""
    echo "Environment Variables:"
    echo "  CI_ARTIFACTS_DIR  Directory to copy reports for CI artifacts"
    echo "  GITHUB_ENV       GitHub Actions environment file"
}

# Main execution
main() {
    local skip_setup=false
    local keep_reports=false
    
    # Parse command line arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            --skip-setup)
                skip_setup=true
                shift
                ;;
            --keep-reports)
                keep_reports=true
                shift
                ;;
            --help)
                usage
                exit 0
                ;;
            *)
                log_error "Unknown option: $1"
                usage
                exit 1
                ;;
        esac
    done
    
    log_info "Starting CI verification process..."
    log_info "Timestamp: $TIMESTAMP"
    log_info "Report file: $REPORT_FILE"
    
    # Run the verification process
    check_prerequisites
    
    if [ "$skip_setup" = false ]; then
        setup_environment
    fi
    
    if run_verification; then
        handle_artifacts
        
        if [ "$keep_reports" = false ]; then
            cleanup
        fi
        
        log_success "CI verification completed successfully"
        exit 0
    else
        handle_artifacts
        
        if [ "$keep_reports" = false ]; then
            cleanup
        fi
        
        log_error "CI verification failed"
        exit 1
    fi
}

# Run main function with all arguments
main "$@"